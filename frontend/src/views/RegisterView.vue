<template>
  <div class="register-view">
    <div class="register-container">
      <div class="register-header">
        <h1 class="app-title">AIDE</h1>
        <p class="app-subtitle">AI-Integrated Design Environment</p>
      </div>
      
      <div class="register-form">
        <h2 class="form-title">注册</h2>
        <p class="form-subtitle">创建您的账号</p>
        
        <div class="form-group">
          <label for="username">用户名</label>
          <input 
            type="text" 
            id="username" 
            v-model="username" 
            class="form-input"
            placeholder="请输入用户名"
          >
          <div v-if="errors.username" class="error-message">{{ errors.username }}</div>
        </div>
        
        <div class="form-group">
          <label for="password">密码</label>
          <input 
            type="password" 
            id="password" 
            v-model="password" 
            class="form-input"
            placeholder="请输入密码"
          >
          <div v-if="errors.password" class="error-message">{{ errors.password }}</div>
        </div>
        
        <div class="form-group">
          <label for="confirmPassword">确认密码</label>
          <input 
            type="password" 
            id="confirmPassword" 
            v-model="confirmPassword" 
            class="form-input"
            placeholder="请再次输入密码"
          >
          <div v-if="errors.confirmPassword" class="error-message">{{ errors.confirmPassword }}</div>
        </div>
        
        <div class="form-group">
          <label for="displayName">显示名称</label>
          <input 
            type="text" 
            id="displayName" 
            v-model="displayName" 
            class="form-input"
            placeholder="请输入您的显示名称"
          >
          <div v-if="errors.displayName" class="error-message">{{ errors.displayName }}</div>
        </div>
        
        <div class="form-actions">
          <button class="btn-primary register-btn" @click="register" :disabled="isLoading">
            {{ isLoading ? '注册中...' : '注册' }}
          </button>
        </div>
        
        <div v-if="registerError" class="register-error">
          {{ registerError }}
        </div>
        
        <div class="login-link">
          已有账号？<router-link to="/login">立即登录</router-link>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive } from 'vue';
import { useRouter } from 'vue-router';
import { authAPI, setCurrentUserToCache } from '../services/api.js';

const router = useRouter();
const username = ref('');
const password = ref('');
const confirmPassword = ref('');
const displayName = ref('');
const isLoading = ref(false);
const registerError = ref('');
const errors = reactive({
  username: '',
  password: '',
  confirmPassword: '',
  displayName: ''
});

// 注册功能
async function register() {
  // 重置错误信息
  registerError.value = '';
  errors.username = '';
  errors.password = '';
  errors.confirmPassword = '';
  errors.displayName = '';
  
  // 表单验证
  let isValid = true;
  
  if (!username.value.trim()) {
    errors.username = '请输入用户名';
    isValid = false;
  } else if (username.value.length < 3) {
    errors.username = '用户名至少需要3个字符';
    isValid = false;
  }
  
  if (!password.value.trim()) {
    errors.password = '请输入密码';
    isValid = false;
  } else if (password.value.length < 6) {
    errors.password = '密码至少需要6个字符';
    isValid = false;
  }
  
  if (!confirmPassword.value.trim()) {
    errors.confirmPassword = '请确认密码';
    isValid = false;
  } else if (confirmPassword.value !== password.value) {
    errors.confirmPassword = '两次输入的密码不一致';
    isValid = false;
  }
  
  if (!displayName.value.trim()) {
    errors.displayName = '请输入显示名称';
    isValid = false;
  }
  
  if (!isValid) return;
  
  // 开始注册流程
  isLoading.value = true;
  
  try {
    // 调用注册API
    const response = await authAPI.register(username.value, password.value, displayName.value);
    
    // 检查响应中是否包含用户数据
    if (!response || !response.user) {
      throw new Error('注册成功但未返回用户数据');
    }

    // 构造用户数据
    const userData = {
      id: response.user.id,
      username: response.user.username,
      displayName: response.user.displayName,
      isAdmin: response.user.role === 'admin',
      role: response.user.role,
      avatarUrl: response.user.avatarUrl,
      lastLogin: new Date().toISOString()
    };
    
    // 将用户信息存储到缓存
    setCurrentUserToCache(userData);
    
    // 保存token
    if (response.tokens && response.tokens.accessToken) {
      setToken(response.tokens.accessToken);
    }
    
    // 注册成功，重定向到首页
    router.push('/');
  } catch (error) {
    // 注册失败
    registerError.value = error.message || '注册失败，请重试';
  } finally {
    isLoading.value = false;
  }
}
</script>

<style scoped>
.register-view {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 100vh;
  background-color: #f5f5f5;
}

.register-container {
  width: 100%;
  max-width: 400px;
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.register-header {
  background-color: #4a6baf;
  color: white;
  padding: 2rem;
  text-align: center;
}

.app-title {
  font-size: 2rem;
  margin-bottom: 0.5rem;
}

.app-subtitle {
  font-size: 0.9rem;
  opacity: 0.9;
}

.register-form {
  padding: 2rem;
}

.form-title {
  font-size: 1.5rem;
  margin-bottom: 0.5rem;
  color: #333;
}

.form-subtitle {
  color: #666;
  margin-bottom: 1.5rem;
  font-size: 0.9rem;
}

.form-group {
  margin-bottom: 1.5rem;
}

.form-group label {
  display: block;
  margin-bottom: 0.5rem;
  font-weight: 500;
  color: #333;
}

.form-input {
  width: 100%;
  padding: 10px 12px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 1rem;
  transition: border-color 0.2s;
}

.form-input:focus {
  border-color: #4a6baf;
  outline: none;
}

.form-actions {
  margin-top: 2rem;
}

.register-btn {
  width: 100%;
  padding: 12px;
  font-size: 1rem;
  font-weight: 500;
}

.btn-primary {
  background-color: #4a6baf;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  transition: background-color 0.2s;
}

.btn-primary:hover {
  background-color: #3d5a8f;
}

.btn-primary:disabled {
  background-color: #7a92c9;
  cursor: not-allowed;
}

.error-message {
  color: #dc3545;
  font-size: 0.85rem;
  margin-top: 0.5rem;
}

.register-error {
  margin-top: 1rem;
  padding: 0.75rem;
  background-color: #f8d7da;
  color: #721c24;
  border-radius: 4px;
  font-size: 0.9rem;
  text-align: center;
}

.login-link {
  margin-top: 1.5rem;
  text-align: center;
  font-size: 0.9rem;
}

.login-link a {
  color: #4a6baf;
  text-decoration: none;
  font-weight: 500;
}

.login-link a:hover {
  text-decoration: underline;
}
</style>