-- SCAIA 数据库表结构 (MySQL 8.0)
-- 创建日期: 2024-01-01
-- 描述: SCAIA平台数据库结构定义

-- 设置字符集
SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- 用户表
CREATE TABLE IF NOT EXISTS `users` (
    `id` int(11) NOT NULL AUTO_INCREMENT,
    `username` varchar(50) NOT NULL,
    `password_hash` varchar(255) NOT NULL COMMENT '存储加密后的密码',
    `display_name` varchar(100) NOT NULL,
    `email` varchar(100) DEFAULT NULL,
    `role` varchar(20) NOT NULL DEFAULT 'user' COMMENT 'admin 或 user',
    `status` varchar(20) NOT NULL DEFAULT 'active' COMMENT 'active, inactive, banned',
    `avatar_url` text,
    `last_login_at` datetime DEFAULT NULL,
    `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_users_username` (`username`),
    UNIQUE KEY `uk_users_email` (`email`),
    KEY `idx_users_role` (`role`),
    KEY `idx_users_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户表';

-- 用户设置表
CREATE TABLE IF NOT EXISTS `user_settings` (
    `id` int(11) NOT NULL AUTO_INCREMENT,
    `user_id` int(11) NOT NULL,
    `theme` varchar(20) NOT NULL DEFAULT 'light',
    `language` varchar(10) NOT NULL DEFAULT 'zh-CN',
    `font_size` int(11) NOT NULL DEFAULT 14,
    `font_family` varchar(50) NOT NULL DEFAULT 'system',
    `enable_notifications` tinyint(1) NOT NULL DEFAULT 1,
    `enable_sound` tinyint(1) NOT NULL DEFAULT 1,
    `auto_save` tinyint(1) NOT NULL DEFAULT 1,
    `show_line_numbers` tinyint(1) NOT NULL DEFAULT 1,
    `word_wrap` tinyint(1) NOT NULL DEFAULT 1,
    `tab_size` int(11) NOT NULL DEFAULT 2,
    `enable_ai_suggestions` tinyint(1) NOT NULL DEFAULT 1,
    `default_ai_model` varchar(50) NOT NULL DEFAULT 'gpt-4',
    `max_tokens` int(11) NOT NULL DEFAULT 2048,
    `temperature` decimal(3,2) NOT NULL DEFAULT 0.70,
    `enable_web_search` tinyint(1) NOT NULL DEFAULT 0,
    `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_user_settings_user_id` (`user_id`),
    CONSTRAINT `fk_user_settings_user_id` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户设置表';

-- 用户活动日志表
CREATE TABLE IF NOT EXISTS `user_activity_logs` (
    `id` int(11) NOT NULL AUTO_INCREMENT,
    `user_id` int(11) NOT NULL,
    `action` varchar(50) NOT NULL,
    `description` text,
    `ip_address` varchar(45) DEFAULT NULL,
    `user_agent` text,
    `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`),
    KEY `idx_user_activity_logs_user_id` (`user_id`),
    KEY `idx_user_activity_logs_action` (`action`),
    KEY `idx_user_activity_logs_created_at` (`created_at`),
    CONSTRAINT `fk_user_activity_logs_user_id` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户活动日志表';

-- AI模型配置表
CREATE TABLE IF NOT EXISTS `ai_models` (
    `id` varchar(50) NOT NULL,
    `name` varchar(100) NOT NULL,
    `type` varchar(20) NOT NULL COMMENT 'chat 或 image',
    `provider` varchar(50) NOT NULL DEFAULT 'openai',
    `api_key` varchar(255) DEFAULT NULL,
    `api_endpoint` text NOT NULL,
    `system_prompt` text DEFAULT NULL COMMENT '系统提示词',
    `max_tokens` int(11) DEFAULT 4096,
    `temperature` decimal(3,2) DEFAULT 0.70,
    `is_active` tinyint(1) NOT NULL DEFAULT 1,
    `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`),
    KEY `idx_ai_models_type` (`type`),
    KEY `idx_ai_models_provider` (`provider`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='AI模型配置表';

-- 聊天会话表
CREATE TABLE IF NOT EXISTS `chat_sessions` (
    `id` int(11) NOT NULL AUTO_INCREMENT,
    `user_id` int(11) NOT NULL,
    `title` varchar(255) NOT NULL DEFAULT '新对话' COMMENT '会话标题',
    `model_id` varchar(50) DEFAULT NULL COMMENT '使用的模型ID',
    `knowledge_base_enabled` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否启用知识库',
    `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`),
    KEY `idx_chat_sessions_user_id` (`user_id`),
    KEY `idx_chat_sessions_updated_at` (`updated_at`),
    CONSTRAINT `fk_chat_sessions_user_id` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE,
    CONSTRAINT `fk_chat_sessions_model_id` FOREIGN KEY (`model_id`) REFERENCES `ai_models` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='聊天会话表';

-- 聊天消息表
CREATE TABLE IF NOT EXISTS `chat_messages` (
    `id` int(11) NOT NULL AUTO_INCREMENT,
    `session_id` int(11) NOT NULL COMMENT '会话ID',
    `role` varchar(20) NOT NULL COMMENT '消息角色: user, assistant, system',
    `content` text NOT NULL COMMENT '消息内容',
    `model_response` text DEFAULT NULL COMMENT '模型原始响应',
    `knowledge_context` text DEFAULT NULL COMMENT '知识库上下文(JSON格式)',
    `tokens_used` int(11) DEFAULT 0 COMMENT '使用的token数量',
    `response_time` decimal(10,3) DEFAULT 0 COMMENT '响应时间(秒)',
    `user_feedback` varchar(10) DEFAULT NULL COMMENT '用户反馈: like, dislike',
    `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`),
    KEY `idx_chat_messages_session_id` (`session_id`),
    KEY `idx_chat_messages_role` (`role`),
    KEY `idx_chat_messages_created_at` (`created_at`),
    CONSTRAINT `fk_chat_messages_session_id` FOREIGN KEY (`session_id`) REFERENCES `chat_sessions` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='聊天消息表';

-- 消息表
CREATE TABLE IF NOT EXISTS `messages` (
    `id` int(11) NOT NULL AUTO_INCREMENT,
    `session_id` int(11) NOT NULL,
    `user_id` int(11) NOT NULL,
    `content` longtext NOT NULL,
    `type` varchar(20) NOT NULL DEFAULT 'text' COMMENT 'text, image, file',
    `role` varchar(20) NOT NULL COMMENT 'user, assistant, system',
    `token_count` int(11) DEFAULT 0,
    `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`),
    KEY `idx_messages_session_id` (`session_id`),
    KEY `idx_messages_user_id` (`user_id`),
    KEY `idx_messages_type` (`type`),
    KEY `idx_messages_role` (`role`),
    KEY `idx_messages_created_at` (`created_at`),
    CONSTRAINT `fk_messages_session_id` FOREIGN KEY (`session_id`) REFERENCES `chat_sessions` (`id`) ON DELETE CASCADE,
    CONSTRAINT `fk_messages_user_id` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='消息表';

-- 消息引用表
CREATE TABLE IF NOT EXISTS `message_references` (
    `id` int(11) NOT NULL AUTO_INCREMENT,
    `message_id` int(11) NOT NULL,
    `reference_type` varchar(20) NOT NULL COMMENT 'knowledge, master, image, prompt, script',
    `reference_id` int(11) NOT NULL,
    `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`),
    KEY `idx_message_references_message_id` (`message_id`),
    KEY `idx_message_references_type_id` (`reference_type`, `reference_id`),
    CONSTRAINT `fk_message_references_message_id` FOREIGN KEY (`message_id`) REFERENCES `messages` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='消息引用表';

-- 知识库分类表
CREATE TABLE IF NOT EXISTS `knowledge_categories` (
    `id` int(11) NOT NULL AUTO_INCREMENT,
    `name` varchar(100) NOT NULL,
    `description` text,
    `created_by` int(11) NOT NULL,
    `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_knowledge_categories_name` (`name`),
    KEY `idx_knowledge_categories_created_by` (`created_by`),
    CONSTRAINT `fk_knowledge_categories_created_by` FOREIGN KEY (`created_by`) REFERENCES `users` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='知识库分类表';

-- 知识库项目表
CREATE TABLE IF NOT EXISTS `knowledge_items` (
    `id` int(11) NOT NULL AUTO_INCREMENT,
    `title` varchar(200) NOT NULL,
    `content` longtext,
    `category_id` int(11) DEFAULT NULL,
    `file_name` varchar(255) DEFAULT NULL,
    `file_path` varchar(500) DEFAULT NULL,
    `file_size` bigint(20) DEFAULT NULL,
    `file_type` varchar(50) DEFAULT NULL,
    `is_public` tinyint(1) NOT NULL DEFAULT 0,
    `created_by` int(11) NOT NULL,
    `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`),
    KEY `idx_knowledge_items_category_id` (`category_id`),
    KEY `idx_knowledge_items_created_by` (`created_by`),
    KEY `idx_knowledge_items_is_public` (`is_public`),
    KEY `idx_knowledge_items_created_at` (`created_at`),
    CONSTRAINT `fk_knowledge_items_category_id` FOREIGN KEY (`category_id`) REFERENCES `knowledge_categories` (`id`) ON DELETE SET NULL,
    CONSTRAINT `fk_knowledge_items_created_by` FOREIGN KEY (`created_by`) REFERENCES `users` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='知识库项目表';

-- 知识库标签表
CREATE TABLE IF NOT EXISTS `knowledge_tags` (
    `id` int(11) NOT NULL AUTO_INCREMENT,
    `name` varchar(50) NOT NULL,
    `color` varchar(7) DEFAULT '#007bff',
    `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_knowledge_tags_name` (`name`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='知识库标签表';

-- 知识库项目标签关联表
CREATE TABLE IF NOT EXISTS `knowledge_item_tags` (
    `id` int(11) NOT NULL AUTO_INCREMENT,
    `item_id` int(11) NOT NULL,
    `tag_id` int(11) NOT NULL,
    `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_knowledge_item_tags` (`item_id`, `tag_id`),
    KEY `idx_knowledge_item_tags_tag_id` (`tag_id`),
    CONSTRAINT `fk_knowledge_item_tags_item_id` FOREIGN KEY (`item_id`) REFERENCES `knowledge_items` (`id`) ON DELETE CASCADE,
    CONSTRAINT `fk_knowledge_item_tags_tag_id` FOREIGN KEY (`tag_id`) REFERENCES `knowledge_tags` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='知识库项目标签关联表';

-- 知识库向量表
CREATE TABLE IF NOT EXISTS `knowledge_vectors` (
    `id` int(11) NOT NULL AUTO_INCREMENT,
    `item_id` int(11) NOT NULL,
    `chunk_id` varchar(50) NOT NULL COMMENT '文档块ID',
    `content` text NOT NULL COMMENT '文档块内容',
    `vector_data` longtext NOT NULL COMMENT '向量数据(JSON格式)',
    `embedding_model` varchar(50) NOT NULL DEFAULT 'text-embedding-ada-002' COMMENT '嵌入模型名称',
    `chunk_index` int(11) NOT NULL DEFAULT 0 COMMENT '块在文档中的索引',
    `token_count` int(11) NOT NULL DEFAULT 0 COMMENT 'token数量',
    `status` varchar(20) NOT NULL DEFAULT 'pending' COMMENT '状态: pending, processing, completed, failed',
    `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_knowledge_vectors_item_chunk` (`item_id`, `chunk_id`),
    KEY `idx_knowledge_vectors_item_id` (`item_id`),
    KEY `idx_knowledge_vectors_status` (`status`),
    KEY `idx_knowledge_vectors_embedding_model` (`embedding_model`),
    CONSTRAINT `fk_knowledge_vectors_item_id` FOREIGN KEY (`item_id`) REFERENCES `knowledge_items` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='知识库向量表';

-- 向量检索日志表
CREATE TABLE IF NOT EXISTS `vector_search_logs` (
    `id` int(11) NOT NULL AUTO_INCREMENT,
    `user_id` int(11) NOT NULL,
    `query` text NOT NULL COMMENT '搜索查询',
    `results_count` int(11) NOT NULL DEFAULT 0 COMMENT '返回结果数量',
    `search_time` decimal(10,3) NOT NULL DEFAULT 0 COMMENT '搜索耗时(秒)',
    `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`),
    KEY `idx_vector_search_logs_user_id` (`user_id`),
    KEY `idx_vector_search_logs_created_at` (`created_at`),
    CONSTRAINT `fk_vector_search_logs_user_id` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='向量检索日志表';

-- 大师库分类表
CREATE TABLE IF NOT EXISTS `master_categories` (
    `id` int(11) NOT NULL AUTO_INCREMENT,
    `name` varchar(100) NOT NULL,
    `description` text,
    `created_by` int(11) NOT NULL,
    `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_master_categories_name` (`name`),
    KEY `idx_master_categories_created_by` (`created_by`),
    CONSTRAINT `fk_master_categories_created_by` FOREIGN KEY (`created_by`) REFERENCES `users` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='大师库分类表';

-- 大师库项目表
CREATE TABLE IF NOT EXISTS `master_items` (
    `id` int(11) NOT NULL AUTO_INCREMENT,
    `name` varchar(100) NOT NULL,
    `description` text,
    `category_id` int(11) DEFAULT NULL,
    `avatar_url` text,
    `system_prompt` longtext,
    `is_public` tinyint(1) NOT NULL DEFAULT 0,
    `created_by` int(11) NOT NULL,
    `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`),
    KEY `idx_master_items_category_id` (`category_id`),
    KEY `idx_master_items_created_by` (`created_by`),
    KEY `idx_master_items_is_public` (`is_public`),
    KEY `idx_master_items_created_at` (`created_at`),
    CONSTRAINT `fk_master_items_category_id` FOREIGN KEY (`category_id`) REFERENCES `master_categories` (`id`) ON DELETE SET NULL,
    CONSTRAINT `fk_master_items_created_by` FOREIGN KEY (`created_by`) REFERENCES `users` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='大师库项目表';

-- 大师库属性表
CREATE TABLE IF NOT EXISTS `master_attributes` (
    `id` int(11) NOT NULL AUTO_INCREMENT,
    `master_id` int(11) NOT NULL,
    `attribute_key` varchar(50) NOT NULL,
    `attribute_value` text,
    `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`),
    KEY `idx_master_attributes_master_id` (`master_id`),
    KEY `idx_master_attributes_key` (`attribute_key`),
    CONSTRAINT `fk_master_attributes_master_id` FOREIGN KEY (`master_id`) REFERENCES `master_items` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='大师库属性表';

-- 大师库标签表
CREATE TABLE IF NOT EXISTS `master_tags` (
    `id` int(11) NOT NULL AUTO_INCREMENT,
    `name` varchar(50) NOT NULL,
    `color` varchar(7) DEFAULT '#28a745',
    `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_master_tags_name` (`name`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='大师库标签表';

-- 大师库项目标签关联表
CREATE TABLE IF NOT EXISTS `master_item_tags` (
    `id` int(11) NOT NULL AUTO_INCREMENT,
    `item_id` int(11) NOT NULL,
    `tag_id` int(11) NOT NULL,
    `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_master_item_tags` (`item_id`, `tag_id`),
    KEY `idx_master_item_tags_tag_id` (`tag_id`),
    CONSTRAINT `fk_master_item_tags_item_id` FOREIGN KEY (`item_id`) REFERENCES `master_items` (`id`) ON DELETE CASCADE,
    CONSTRAINT `fk_master_item_tags_tag_id` FOREIGN KEY (`tag_id`) REFERENCES `master_tags` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='大师库项目标签关联表';

-- 图片库分类表
CREATE TABLE IF NOT EXISTS `image_categories` (
    `id` int(11) NOT NULL AUTO_INCREMENT,
    `name` varchar(100) NOT NULL,
    `description` text,
    `created_by` int(11) NOT NULL,
    `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_image_categories_name` (`name`),
    KEY `idx_image_categories_created_by` (`created_by`),
    CONSTRAINT `fk_image_categories_created_by` FOREIGN KEY (`created_by`) REFERENCES `users` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='图片库分类表';

-- 图片库项目表
CREATE TABLE IF NOT EXISTS `image_items` (
    `id` int(11) NOT NULL AUTO_INCREMENT,
    `title` varchar(200) NOT NULL,
    `description` text,
    `category_id` int(11) DEFAULT NULL,
    `file_name` varchar(255) NOT NULL,
    `file_path` varchar(500) NOT NULL,
    `file_size` bigint(20) NOT NULL,
    `file_type` varchar(50) NOT NULL,
    `width` int(11) DEFAULT NULL,
    `height` int(11) DEFAULT NULL,
    `is_public` tinyint(1) NOT NULL DEFAULT 0,
    `created_by` int(11) NOT NULL,
    `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`),
    KEY `idx_image_items_category_id` (`category_id`),
    KEY `idx_image_items_created_by` (`created_by`),
    KEY `idx_image_items_is_public` (`is_public`),
    KEY `idx_image_items_created_at` (`created_at`),
    CONSTRAINT `fk_image_items_category_id` FOREIGN KEY (`category_id`) REFERENCES `image_categories` (`id`) ON DELETE SET NULL,
    CONSTRAINT `fk_image_items_created_by` FOREIGN KEY (`created_by`) REFERENCES `users` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='图片库项目表';

-- 图片库标签表
CREATE TABLE IF NOT EXISTS `image_tags` (
    `id` int(11) NOT NULL AUTO_INCREMENT,
    `name` varchar(50) NOT NULL,
    `color` varchar(7) DEFAULT '#dc3545',
    `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_image_tags_name` (`name`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='图片库标签表';

-- 图片库项目标签关联表
CREATE TABLE IF NOT EXISTS `image_item_tags` (
    `id` int(11) NOT NULL AUTO_INCREMENT,
    `item_id` int(11) NOT NULL,
    `tag_id` int(11) NOT NULL,
    `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_image_item_tags` (`item_id`, `tag_id`),
    KEY `idx_image_item_tags_tag_id` (`tag_id`),
    CONSTRAINT `fk_image_item_tags_item_id` FOREIGN KEY (`item_id`) REFERENCES `image_items` (`id`) ON DELETE CASCADE,
    CONSTRAINT `fk_image_item_tags_tag_id` FOREIGN KEY (`tag_id`) REFERENCES `image_tags` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='图片库项目标签关联表';

-- 图片提示词表
CREATE TABLE IF NOT EXISTS `image_prompts` (
    `id` int(11) NOT NULL AUTO_INCREMENT,
    `image_id` int(11) NOT NULL,
    `prompt_type` varchar(20) NOT NULL COMMENT 'positive, negative',
    `prompt_text` text NOT NULL,
    `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`),
    KEY `idx_image_prompts_image_id` (`image_id`),
    KEY `idx_image_prompts_type` (`prompt_type`),
    CONSTRAINT `fk_image_prompts_image_id` FOREIGN KEY (`image_id`) REFERENCES `image_items` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='图片提示词表';

-- 提示词库分类表
CREATE TABLE IF NOT EXISTS `prompt_categories` (
    `id` int(11) NOT NULL AUTO_INCREMENT,
    `name` varchar(100) NOT NULL,
    `description` text,
    `list_order` int(11) NOT NULL DEFAULT 0,
    `created_by` int(11) NOT NULL,
    `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_prompt_categories_name` (`name`),
    KEY `idx_prompt_categories_created_by` (`created_by`),
    CONSTRAINT `fk_prompt_categories_created_by` FOREIGN KEY (`created_by`) REFERENCES `users` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='提示词库分类表';

-- 提示词库项目表
CREATE TABLE IF NOT EXISTS `prompt_items` (
    `id` int(11) NOT NULL AUTO_INCREMENT,
    `title` varchar(200) NOT NULL,
    `content` longtext NOT NULL,
    `category_id` int(11) DEFAULT NULL,
    `description` text,
    `usage_count` int(11) NOT NULL DEFAULT 0,
    `is_public` tinyint(1) NOT NULL DEFAULT 0,
    `created_by` int(11) NOT NULL,
    `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`),
    KEY `idx_prompt_items_category_id` (`category_id`),
    KEY `idx_prompt_items_created_by` (`created_by`),
    KEY `idx_prompt_items_is_public` (`is_public`),
    KEY `idx_prompt_items_usage_count` (`usage_count`),
    KEY `idx_prompt_items_created_at` (`created_at`),
    CONSTRAINT `fk_prompt_items_category_id` FOREIGN KEY (`category_id`) REFERENCES `prompt_categories` (`id`) ON DELETE SET NULL,
    CONSTRAINT `fk_prompt_items_created_by` FOREIGN KEY (`created_by`) REFERENCES `users` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='提示词库项目表';

-- 话术库分类表
CREATE TABLE IF NOT EXISTS `script_categories` (
    `id` int(11) NOT NULL AUTO_INCREMENT,
    `name` varchar(100) NOT NULL,
    `description` text,
    `created_by` int(11) NOT NULL,
    `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_script_categories_name` (`name`),
    KEY `idx_script_categories_created_by` (`created_by`),
    CONSTRAINT `fk_script_categories_created_by` FOREIGN KEY (`created_by`) REFERENCES `users` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='话术库分类表';

-- 话术库项目表
CREATE TABLE IF NOT EXISTS `scripts` (
    `id` int(11) NOT NULL AUTO_INCREMENT,
    `title` varchar(200) NOT NULL,
    `content` longtext NOT NULL,
    `category_id` int(11) DEFAULT NULL,
    `description` text,
    `usage_count` int(11) NOT NULL DEFAULT 0,
    `is_public` tinyint(1) NOT NULL DEFAULT 0,
    `created_by` int(11) NOT NULL,
    `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`),
    KEY `idx_scripts_category_id` (`category_id`),
    KEY `idx_scripts_created_by` (`created_by`),
    KEY `idx_scripts_is_public` (`is_public`),
    KEY `idx_scripts_usage_count` (`usage_count`),
    KEY `idx_scripts_created_at` (`created_at`),
    CONSTRAINT `fk_scripts_category_id` FOREIGN KEY (`category_id`) REFERENCES `script_categories` (`id`) ON DELETE SET NULL,
    CONSTRAINT `fk_scripts_created_by` FOREIGN KEY (`created_by`) REFERENCES `users` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='话术库项目表';

-- 话术库标签表
CREATE TABLE IF NOT EXISTS `script_tags` (
    `id` int(11) NOT NULL AUTO_INCREMENT,
    `name` varchar(50) NOT NULL,
    `color` varchar(7) DEFAULT '#6f42c1',
    `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_script_tags_name` (`name`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='话术库标签表';

-- 话术库项目标签关联表
CREATE TABLE IF NOT EXISTS `script_item_tags` (
    `id` int(11) NOT NULL AUTO_INCREMENT,
    `item_id` int(11) NOT NULL,
    `tag_id` int(11) NOT NULL,
    `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_script_item_tags` (`item_id`, `tag_id`),
    KEY `idx_script_item_tags_tag_id` (`tag_id`),
    CONSTRAINT `fk_script_item_tags_item_id` FOREIGN KEY (`item_id`) REFERENCES `scripts` (`id`) ON DELETE CASCADE,
    CONSTRAINT `fk_script_item_tags_tag_id` FOREIGN KEY (`tag_id`) REFERENCES `script_tags` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='话术库项目标签关联表';

-- 话术参数表
CREATE TABLE IF NOT EXISTS `script_parameters` (
    `id` int(11) NOT NULL AUTO_INCREMENT,
    `script_id` int(11) NOT NULL,
    `parameter_name` varchar(50) NOT NULL,
    `parameter_type` varchar(20) NOT NULL DEFAULT 'text' COMMENT 'text, number, select, textarea',
    `parameter_options` text COMMENT 'JSON格式的选项列表',
    `default_value` text,
    `is_required` tinyint(1) NOT NULL DEFAULT 0,
    `description` text,
    `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`),
    KEY `idx_script_parameters_script_id` (`script_id`),
    CONSTRAINT `fk_script_parameters_script_id` FOREIGN KEY (`script_id`) REFERENCES `scripts` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='话术参数表';

-- 模型使用统计表
CREATE TABLE IF NOT EXISTS `model_usage` (
    `id` int(11) NOT NULL AUTO_INCREMENT,
    `model_id` varchar(50) NOT NULL,
    `user_id` int(11) NOT NULL,
    `call_count` int(11) NOT NULL DEFAULT 0,
    `token_count` int(11) NOT NULL DEFAULT 0,
    `month` varchar(7) NOT NULL COMMENT '格式: YYYY-MM',
    `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_model_usage_model_user_month` (`model_id`, `user_id`, `month`),
    KEY `idx_model_usage_user_id` (`user_id`),
    CONSTRAINT `fk_model_usage_user_id` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='模型使用统计表';

-- 启用外键约束
SET FOREIGN_KEY_CHECKS = 1;

-- 插入默认数据
-- 插入默认管理员用户 (密码: admin123)
INSERT IGNORE INTO `users` (`id`, `username`, `password_hash`, `display_name`, `email`, `role`, `status`) VALUES
(1, 'admin', '$2a$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewdBPj/hL.hl.vHW', '系统管理员', '<EMAIL>', 'admin', 'active');

-- 插入默认AI模型
INSERT IGNORE INTO `ai_models` (`id`, `name`, `type`, `