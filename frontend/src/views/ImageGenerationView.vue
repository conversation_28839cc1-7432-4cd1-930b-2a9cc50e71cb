<template>
  <div class="image-generation-view">
    <h1 class="page-title">生图</h1>
    <p class="page-description">基于 Midjourney 的专业级、结构化图像生成引擎</p>
    
    <div class="generation-container">
      <!-- 左侧边栏 - Midjourney参数设置 -->
      <div class="sidebar-left">
        <h2 class="sidebar-title">Midjourney参数</h2>
        <p class="sidebar-description">点击参数可添加到构图/出图指令</p>
        
        <div class="parameter-actions">
          <button class="param-action-btn" @click="copyAllParameters" title="复制所有已选参数">
            <span class="action-icon">📋</span> 复制参数
          </button>
          <button class="param-action-btn" @click="clearAllParameters" title="清除所有已选参数">
            <span class="action-icon">🗑️</span> 清除参数
          </button>
        </div>
        
        <div class="parameter-groups">
          <div class="parameter-group">
            <h3 class="parameter-group-title">宽高比</h3>
            <div class="parameter-list">
              <div class="parameter-item" :class="{ 'parameter-active': isParameterActive('--ar 16:9') }" @click="toggleParameter('--ar 16:9')">
                <span class="parameter-name">--ar 16:9</span>
                <span class="parameter-desc">宽高比16:9 (宽屏)</span>
                <span class="parameter-status">{{ isParameterActive('--ar 16:9') ? '✓' : '+' }}</span>
              </div>
              <div class="parameter-item" :class="{ 'parameter-active': isParameterActive('--ar 4:5') }" @click="toggleParameter('--ar 4:5')">
                <span class="parameter-name">--ar 4:5</span>
                <span class="parameter-desc">宽高比4:5 (社交媒体常用)</span>
                <span class="parameter-status">{{ isParameterActive('--ar 4:5') ? '✓' : '+' }}</span>
              </div>
              <div class="parameter-item" :class="{ 'parameter-active': isParameterActive('--ar 1:1') }" @click="toggleParameter('--ar 1:1')">
                <span class="parameter-name">--ar 1:1</span>
                <span class="parameter-desc">宽高比1:1 (方形)</span>
                <span class="parameter-status">{{ isParameterActive('--ar 1:1') ? '✓' : '+' }}</span>
              </div>
              <div class="parameter-item" :class="{ 'parameter-active': isParameterActive('--ar 9:16') }" @click="toggleParameter('--ar 9:16')">
                <span class="parameter-name">--ar 9:16</span>
                <span class="parameter-desc">宽高比9:16 (垂直)</span>
                <span class="parameter-status">{{ isParameterActive('--ar 9:16') ? '✓' : '+' }}</span>
              </div>
            </div>
          </div>
          
          <div class="parameter-group">
            <h3 class="parameter-group-title">风格控制</h3>
            <div class="parameter-list">
              <div class="parameter-item" :class="{ 'parameter-active': isParameterActive('--style raw') }" @click="toggleParameter('--style raw')">
                <span class="parameter-name">--style raw</span>
                <span class="parameter-desc">原始风格，更具摄影感</span>
                <span class="parameter-status">{{ isParameterActive('--style raw') ? '✓' : '+' }}</span>
              </div>
              <div class="parameter-item" :class="{ 'parameter-active': hasParameterPrefix('--stylize') }" @click="showValueInputModal('--stylize', 250, 0, 1000)">
                <span class="parameter-name">--stylize {{ getParameterValue('--stylize', 250) }}</span>
                <span class="parameter-desc">风格化程度 (0-1000, 默认为100)</span>
                <span class="parameter-status">{{ hasParameterPrefix('--stylize') ? '✓' : '+' }}</span>
              </div>
              <div class="parameter-item" :class="{ 'parameter-active': hasParameterPrefix('--chaos') }" @click="showValueInputModal('--chaos', 10, 0, 100)">
                <span class="parameter-name">--chaos {{ getParameterValue('--chaos', 10) }}</span>
                <span class="parameter-desc">变化性/随机性 (0-100)</span>
                <span class="parameter-status">{{ hasParameterPrefix('--chaos') ? '✓' : '+' }}</span>
              </div>
              <div class="parameter-item" :class="{ 'parameter-active': hasParameterPrefix('--weird') }" @click="showValueInputModal('--weird', 250, 0, 3000)">
                <span class="parameter-name">--weird {{ getParameterValue('--weird', 250) }}</span>
                <span class="parameter-desc">奇异度 (0-3000)</span>
                <span class="parameter-status">{{ hasParameterPrefix('--weird') ? '✓' : '+' }}</span>
              </div>
            </div>
          </div>
          
          <div class="parameter-group">
            <h3 class="parameter-group-title">特殊效果</h3>
            <div class="parameter-list">
              <div class="parameter-item" :class="{ 'parameter-active': isParameterActive('--tile') }" @click="toggleParameter('--tile')">
                <span class="parameter-name">--tile</span>
                <span class="parameter-desc">生成可平铺的图案</span>
                <span class="parameter-status">{{ isParameterActive('--tile') ? '✓' : '+' }}</span>
              </div>
              <div class="parameter-item" :class="{ 'parameter-active': isParameterActive('--v 7') }" @click="toggleParameter('--v 7')">
                <span class="parameter-name">--v 7</span>
                <span class="parameter-desc">指定使用v7版本 (当前最新版本)</span>
                <span class="parameter-status">{{ isParameterActive('--v 7') ? '✓' : '+' }}</span>
              </div>
              <div class="parameter-item" :class="{ 'parameter-active': isParameterActive('--no people') }" @click="toggleParameter('--no people')">
                <span class="parameter-name">--no people</span>
                <span class="parameter-desc">画面中不要出现人物</span>
                <span class="parameter-status">{{ isParameterActive('--no people') ? '✓' : '+' }}</span>
              </div>
              <div class="parameter-item" :class="{ 'parameter-active': hasParameterPrefix('--stop') }" @click="showValueInputModal('--stop', 90, 10, 100)">
                <span class="parameter-name">--stop {{ getParameterValue('--stop', 90) }}</span>
                <span class="parameter-desc">在90%完成度时停止生成</span>
                <span class="parameter-status">{{ hasParameterPrefix('--stop') ? '✓' : '+' }}</span>
              </div>
            </div>
          </div>
          
          <div class="parameter-group">
            <h3 class="parameter-group-title">参考图片</h3>
            <div class="parameter-list">
              <div class="parameter-item" :class="{ 'parameter-active': hasParameterPrefix('--cref') }" @click="showUrlInputModal('--cref')">
                <span class="parameter-name">--cref {{ hasParameterPrefix('--cref') ? '[已设置]' : '[image_url]' }}</span>
                <span class="parameter-desc">参考一张图片的风格</span>
                <span class="parameter-status">{{ hasParameterPrefix('--cref') ? '✓' : '+' }}</span>
              </div>
              <div class="parameter-item" :class="{ 'parameter-active': hasParameterPrefix('--sref') }" @click="showUrlInputModal('--sref')">
                <span class="parameter-name">--sref {{ hasParameterPrefix('--sref') ? '[已设置]' : '[image_url]' }}</span>
                <span class="parameter-desc">参考一张图片的风格 (比cref更强力)</span>
                <span class="parameter-status">{{ hasParameterPrefix('--sref') ? '✓' : '+' }}</span>
              </div>
              <div class="parameter-item" :class="{ 'parameter-active': hasParameterPrefix('--cw') }" @click="showValueInputModal('--cw', 50, 0, 100)">
                <span class="parameter-name">--cw {{ getParameterValue('--cw', 50) }}</span>
                <span class="parameter-desc">调整cref的参考强度 (0-100)</span>
                <span class="parameter-status">{{ hasParameterPrefix('--cw') ? '✓' : '+' }}</span>
              </div>
              <div class="parameter-item" :class="{ 'parameter-active': hasParameterPrefix('--sw') }" @click="showValueInputModal('--sw', 500, 0, 1000)">
                <span class="parameter-name">--sw {{ getParameterValue('--sw', 500) }}</span>
                <span class="parameter-desc">调整sref的参考强度 (0-1000)</span>
                <span class="parameter-status">{{ hasParameterPrefix('--sw') ? '✓' : '+' }}</span>
              </div>
            </div>
          </div>
          
          <div class="parameter-group">
            <h3 class="parameter-group-title">质量提升</h3>
            <div class="parameter-list">
              <div class="parameter-item" :class="{ 'parameter-active': isParameterActive('Ultra high quality') }" @click="toggleParameter('Ultra high quality')">
                <span class="parameter-name">Ultra high quality</span>
                <span class="parameter-desc">超高质量</span>
                <span class="parameter-status">{{ isParameterActive('Ultra high quality') ? '✓' : '+' }}</span>
              </div>
              <div class="parameter-item" :class="{ 'parameter-active': isParameterActive('8K resolution') }" @click="toggleParameter('8K resolution')">
                <span class="parameter-name">8K resolution</span>
                <span class="parameter-desc">8K分辨率</span>
                <span class="parameter-status">{{ isParameterActive('8K resolution') ? '✓' : '+' }}</span>
              </div>
              <div class="parameter-item" :class="{ 'parameter-active': isParameterActive('Professionally color graded') }" @click="toggleParameter('Professionally color graded')">
                <span class="parameter-name">Professionally color graded</span>
                <span class="parameter-desc">专业色彩分级</span>
                <span class="parameter-status">{{ isParameterActive('Professionally color graded') ? '✓' : '+' }}</span>
              </div>
              <div class="parameter-item" :class="{ 'parameter-active': isParameterActive('Sharp focus') }" @click="toggleParameter('Sharp focus')">
                <span class="parameter-name">Sharp focus</span>
                <span class="parameter-desc">清晰对焦</span>
                <span class="parameter-status">{{ isParameterActive('Sharp focus') ? '✓' : '+' }}</span>
              </div>
            </div>
          </div>
        </div>
      </div>
      
      <div class="input-section">
        <div class="section-header">
          <h2 class="section-title">结构化描述</h2>
          <button class="library-btn" @click="showLibraryModal = true" title="打开图片库">
            <span class="btn-icon">🖼️</span> 图片库
          </button>
        </div>
        
        <div class="structured-inputs">
          <div class="form-group">
            <label class="form-label">项目 <span class="field-hint">(图像核心主体)</span></label>
            <div class="input-with-button">
              <textarea :ref="el => textareas.project = el" v-model="promptData.project" class="form-input" placeholder="例如：现代风格的住宅建筑" rows="3"></textarea>
              <button class="prompt-btn" @click="openPromptSelector('project')" title="添加提示词">+提示词</button>
            </div>
          </div>
          
          <div class="form-group">
            <label class="form-label">风格 <span class="field-hint">(参考的设计风格或大师)</span></label>
            <div class="input-with-button">
              <textarea :ref="el => textareas.style = el" v-model="promptData.style" class="form-input" placeholder="例如：极简主义，或安藤忠雄风格" rows="3"></textarea>
              <button class="prompt-btn" @click="openPromptSelector('style')" title="添加提示词">+提示词</button>
            </div>
          </div>
          
          <div class="form-group">
            <label class="form-label">材料 <span class="field-hint">(关键的建筑或装饰材料)</span></label>
            <div class="input-with-button">
              <textarea :ref="el => textareas.material = el" v-model="promptData.material" class="form-input" placeholder="例如：混凝土，玻璃，木材" rows="3"></textarea>
              <button class="prompt-btn" @click="openPromptSelector('material')" title="添加提示词">+提示词</button>
            </div>
          </div>
          
          <div class="form-group">
            <label class="form-label">环境 <span class="field-hint">(主体所处的背景和场景)</span></label>
            <div class="input-with-button">
              <textarea :ref="el => textareas.environment = el" v-model="promptData.environment" class="form-input" placeholder="例如：海边悬崖，或城市中心" rows="3"></textarea>
              <button class="prompt-btn" @click="openPromptSelector('environment')" title="添加提示词">+提示词</button>
            </div>
          </div>
          
          <div class="form-group">
            <label class="form-label">光影/氛围 <span class="field-hint">(光线效果和整体感觉)</span></label>
            <div class="input-with-button">
              <textarea :ref="el => textareas.lighting = el" v-model="promptData.lighting" class="form-input" placeholder="例如：黄昏柔和光线，或清晨薄雾" rows="3"></textarea>
              <button class="prompt-btn" @click="openPromptSelector('lighting')" title="添加提示词">+提示词</button>
            </div>
          </div>
          
          <div class="form-group">
            <label class="form-label">构图/出图指令 <span class="field-hint">(镜头、视角及参数)</span></label>
            <div class="input-with-button">
              <textarea :ref="el => textareas.composition = el" v-model="promptData.composition" class="form-input" placeholder="例如：广角镜头，鸟瞰视角，--ar 16:9" rows="3"></textarea>
              <button class="prompt-btn" @click="openPromptSelector('composition')" title="添加提示词">+提示词</button>
            </div>
          </div>
        </div>
        
        <div class="preview-prompt">
          <h3>完整 Prompt 预览</h3>
          <div class="prompt-box">{{ fullPrompt }}</div>
        </div>

        <!-- Midjourney状态提示 -->
        <div v-if="!midjourneyStatus.configured" class="midjourney-warning">
          <div class="warning-content">
            <span class="warning-icon">⚠️</span>
            <div class="warning-text">
              <p><strong>Midjourney未配置</strong></p>
              <p>请先在<router-link to="/settings">设置页面</router-link>配置Discord Token和频道信息</p>
            </div>
          </div>
        </div>

        <div class="action-buttons">
          <button
            class="btn btn-primary"
            @click="generateImage"
            :disabled="isGenerating || !midjourneyStatus.configured"
          >
            {{ isGenerating ? '生成中...' : '生成图像' }}
          </button>
          <button class="btn btn-secondary" @click="clearInputs">
            清空输入
          </button>
        </div>
      </div>
      
      <div class="result-section">
        <h2 class="section-title">生成结果</h2>

        <div v-if="isGenerating" class="generating-indicator">
          <div class="spinner"></div>
          <p v-if="midjourneyStatus.configured">正在使用Midjourney生成图像，请稍候...</p>
          <p v-else>🎭 正在使用演示模式生成图像，请稍候...</p>
          <p v-if="midjourneyStatus.configured" class="generation-tip">提示：Midjourney生成高质量图像通常需要2-5分钟</p>
          <p v-else class="generation-tip">提示：演示模式将快速生成示例图片（3-8秒）</p>
          <p class="generation-tip">生成完成后图片将显示在下方结果区域</p>
        </div>
        
        <div v-else-if="generatedImages.length > 0" class="image-results">
          <div 
            v-for="(image, index) in generatedImages" 
            :key="index"
            class="image-result-item"
          >
            <img :src="image.url" :alt="'生成图像 ' + (index + 1)" class="result-image">
            <div class="image-actions">
              <button class="action-btn" @click="saveToLibrary(image)" title="保存到图片库">
                💾 保存到图片库
              </button>
              <button class="action-btn" @click="downloadImage(image)" title="下载图像">
                ⬇️ 下载
              </button>
              <button class="action-btn" @click="regenerateVariation(image)" title="生成变体">
                🔄 变体
              </button>
              <button class="action-btn" @click="upscaleImage(image)" title="放大图像">
                🔍 放大
              </button>
            </div>
          </div>
        </div>
        
        <div v-else class="empty-result">
          <div class="empty-icon">🖼️</div>
          <p>填写左侧的结构化描述并点击"生成图像"按钮</p>
        </div>
      </div>
    </div>
    
    <!-- 右侧边栏 - 图片库联动 -->
    <div class="sidebar-right">
      <h2 class="sidebar-title">图片库</h2>
      <p class="sidebar-description">点击图片可将其结构化描述填充到输入框</p>
      
      <div class="library-images">
        <div 
          v-for="(image, index) in libraryImages" 
          :key="index"
          class="library-image-item"
          @click="fillFromLibrary(image)"
        >
          <img :src="image.url" :alt="'库图片 ' + (index + 1)" class="library-image">
          <div class="image-overlay">
            <span class="overlay-text">点击使用</span>
          </div>
        </div>
      </div>
    </div>
    
    <!-- 图片库模态框 -->
    <div class="modal" v-if="showLibraryModal">
      <div class="modal-content">
        <div class="modal-header">
          <h3>图片库</h3>
          <button class="close-btn" @click="showLibraryModal = false">&times;</button>
        </div>
        <div class="modal-body">
          <p class="modal-description">点击图片可将其结构化描述填充到输入框</p>
          
          <div class="library-filter">
            <div class="filter-row">
              <select v-model="selectedCategory" class="form-input select-input">
                <option value="all">所有分类</option>
                <option v-for="category in categories" :key="category.id" :value="category.name">
                  {{ category.name }}
                </option>
              </select>
              <input 
                type="text" 
                v-model="searchQuery" 
                class="form-input" 
                placeholder="搜索图片..."
              >
            </div>
          </div>
          
          <div class="library-grid">
            <div 
              v-for="(image, index) in filteredLibraryImages" 
              :key="index"
              class="library-grid-item"
              @click="fillFromLibraryAndClose(image)"
            >
              <img :src="image.url" :alt="image.name" class="library-grid-image">
              <div class="library-grid-overlay">
                <span class="overlay-text">选择</span>
              </div>
              <div class="library-grid-info">
                <h4 class="library-image-name">{{ image.name }}</h4>
                <div class="library-image-category">{{ image.category }}</div>
                <div class="library-image-tags">
                  <span v-for="(tag, tagIndex) in image.tags" :key="tagIndex" class="tag">{{ tag }}</span>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div class="modal-footer">
          <button class="btn" @click="showLibraryModal = false">关闭111</button>
        </div>
      </div>
    </div>
    
    <!-- 参数值输入模态框 -->
    <div class="modal" v-if="showInputModal">
      <div class="modal-content">
        <div class="modal-header">
          <h3>{{ inputModalTitle }}</h3>
          <button class="close-btn" @click="showInputModal = false">&times;</button>
        </div>
        <div class="modal-body">
          <div v-if="inputModalType === 'number'">
            <label :for="inputModalParam">请输入 {{ inputModalParam }} 的值 ({{ inputModalMin }}-{{ inputModalMax }}):</label>
            <input 
              :id="inputModalParam" 
              v-model="inputModalValue" 
              type="number" 
              :min="inputModalMin" 
              :max="inputModalMax" 
              class="input-field"
            />
          </div>
          <div v-else-if="inputModalType === 'url'">
            <label :for="inputModalParam">请输入图片URL:</label>
            <input 
              :id="inputModalParam" 
              v-model="inputModalValue" 
              type="text" 
              placeholder="https://example.com/image.jpg" 
              class="input-field"
            />
          </div>
        </div>
        <div class="modal-footer">
          <button class="btn primary-btn" @click="confirmParameterValue">确认</button>
          <button class="btn" @click="showInputModal = false">取消</button>
        </div>
      </div>
    </div>
    
    <!-- 保存到图片库模态框 -->
    <div v-if="showSaveModal" class="modal">
      <div class="modal-content">
        <div class="modal-header">
          <h3>保存到图片库</h3>
          <button class="close-btn" @click="showSaveModal = false">×</button>
        </div>
        <div class="modal-body">
          <div class="selected-image">
            <img :src="selectedImage.url" alt="选中的图像" class="preview-image">
          </div>
          
          <div class="form-group">
            <label class="form-label">图片名称</label>
            <input type="text" v-model="saveImageData.name" class="form-input" placeholder="为图片命名">
          </div>
          
          <div class="form-group">
            <label class="form-label">标签</label>
            <input type="text" v-model="saveImageData.tags" class="form-input" placeholder="用逗号分隔多个标签">
          </div>
          
          <div class="structured-data-preview">
            <h4>结构化数据</h4>
            <div class="data-preview">
              <p><strong>项目:</strong> {{ promptData.project }}</p>
              <p><strong>风格:</strong> {{ promptData.style }}</p>
              <p><strong>材料:</strong> {{ promptData.material }}</p>
              <p><strong>环境:</strong> {{ promptData.environment }}</p>
              <p><strong>光影/氛围:</strong> {{ promptData.lighting }}</p>
              <p><strong>构图/出图指令:</strong> {{ promptData.composition }}</p>
            </div>
          </div>
        </div>
        <div class="modal-footer">
          <button class="btn btn-secondary" @click="showSaveModal = false">取消</button>
          <button class="btn btn-primary" @click="confirmSaveToLibrary">保存</button>
        </div>
      </div>
    </div>
    
    <!-- 提示词选择器对话框 -->
    <div v-if="showPromptSelector" class="modal">
      <div class="modal-content">
        <div class="modal-header">
          <h3>选择{{ getFieldName(currentField) }}提示词</h3>
          <button class="close-btn" @click="showPromptSelector = false">&times;</button>
        </div>
        <div class="modal-body">
          <div class="prompt-selector-list">
            <div 
              v-for="(prompt, index) in getPromptsForField(currentField)" 
              :key="index"
              class="prompt-selector-item"
              @click="selectPrompt(prompt)"
            >
              <div class="prompt-selector-text">{{ prompt.title }}</div>
            </div>
          </div>
        </div>
        <div class="modal-footer">
          <button class="btn" @click="showPromptSelector = false">关闭</button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, nextTick, watch } from 'vue'
import { imageAPI, fetchPrompts, fetchPromptCategories } from '@/services/api';
import { useRoute } from 'vue-router';

const route = useRoute();

// 结构化提示词数据
const promptData = ref({
  project: '',
  style: '',
  material: '',
  environment: '',
  lighting: '',
  composition: '',
  chinesePrompts: { // 新增，用于存储中文提示
    project: [],
    style: [],
    material: [],
    environment: [],
    lighting: [],
    composition: []
  }
});

// 计算完整提示词
const fullPrompt = computed(() => {
  const parts = [];
  
  if (promptData.value.project) parts.push(promptData.value.project);
  if (promptData.value.style) parts.push(promptData.value.style);
  if (promptData.value.material) parts.push(promptData.value.material);
  if (promptData.value.environment) parts.push(promptData.value.environment);
  if (promptData.value.lighting) parts.push(promptData.value.lighting);
  if (promptData.value.composition) parts.push(promptData.value.composition);
  
  // 添加选中的参数
  if (activeParameters.value.length > 0) {
    parts.push(activeParameters.value.join(' '));
  }
  
  return parts.join(', ');
});

const textareas = ref({
  project: null,
  style: null,
  material: null,
  environment: null,
  lighting: null,
  composition: null,
});

const adjustTextareaHeight = (el) => {
  if (el) {
    el.style.height = 'auto';
    el.style.height = `${el.scrollHeight}px`;
  }
};

const adjustAllTextareas = () => {
  nextTick(() => {
    for (const key in textareas.value) {
      adjustTextareaHeight(textareas.value[key]);
    }
  });
};

watch(promptData, adjustAllTextareas, { deep: true });

// 生成状态和结果
const isGenerating = ref(false);
const generatedImages = ref([]);

// Midjourney状态
const midjourneyStatus = ref({
  configured: false,
  available: false
});

// 从路由参数中获取并应用结构化提示词数据
onMounted(async () => {
  // 加载分类和图片库数据
  await loadCategories();
  await loadLibraryImages();
  await loadPromptCategories();

  // 检查Midjourney配置状态
  await checkMidjourneyStatus();
  
  // 检查路由参数中是否有结构化提示词数据
  if (route.query.promptData) {
    try {
      // 解析JSON字符串
      const parsedPromptData = JSON.parse(route.query.promptData);
      
      // 更新结构化提示词数据
      if (parsedPromptData) {
        // 遍历所有可能的字段并更新
        ['project', 'style', 'material', 'environment', 'lighting', 'composition'].forEach(field => {
          if (parsedPromptData[field]) {
            promptData.value[field] = parsedPromptData[field];
          }
        });
      }
    } catch (error) {
      console.error('解析结构化提示词数据时出错:', error);
    }
  } else if (route.query.prompt) {
    // 如果只有完整提示词，则将其放入项目字段
    promptData.value.project = route.query.prompt;
  }
});

// 数据状态
const libraryImages = ref([])
const loading = ref(false)
const error = ref('')

// 加载图片库数据
async function loadLibraryImages() {
  try {
    loading.value = true
    const response = await imageAPI.getImageList()
    libraryImages.value = response.data.items || []
  } catch (err) {
    console.error('加载图片库失败:', err)
    error.value = '加载图片库失败'
    libraryImages.value = [] // Clear images on error
  } finally {
    loading.value = false
  }
}

// 过滤图片库图片
const filteredLibraryImages = computed(() => {
  return libraryImages.value.filter(image => {
    // 分类筛选
    const categoryMatch = selectedCategory.value === 'all' || image.category === selectedCategory.value;
    
    // 搜索筛选
    const searchLower = searchQuery.value.toLowerCase();
    const searchMatch = searchQuery.value === '' || 
                       (image.name && image.name.toLowerCase().includes(searchLower)) || 
                       (image.tags && image.tags.some(tag => tag.toLowerCase().includes(searchLower)));
    
    return categoryMatch && searchMatch;
  });
});

// 模态框控制
const showLibraryModal = ref(false);
const showSaveModal = ref(false);
const selectedImage = ref({});
const saveImageData = ref({
  name: '',
  tags: ''
});

// 图片库筛选
const selectedCategory = ref('all');
const searchQuery = ref('');

// 图片分类（动态从后端获取）
const categories = ref([]);

// 加载分类数据
async function loadCategories() {
  try {
    const response = await imageAPI.getImageCategories();
    categories.value = response.data || [];
  } catch (err) {
    console.error('加载分类失败:', err);
    categories.value = [];
  }
}

// 加载提示词分类
const loadPromptCategories = async () => {
  console.log('开始加载提示词分类...');
  try {
    const response = await fetchPromptCategories();
    console.log('提示词分类API响应:', response);
    if (response.success && Array.isArray(response.data)) {
      promptCategories.value = response.data;
      console.log('提示词分类加载成功:', promptCategories.value);
    } else {
      console.warn('API返回的提示词分类数据格式不正确:', response);
      promptCategories.value = [];
    }
  } catch (error) {
    console.error('加载提示词分类失败:', error);
    promptCategories.value = [];
  }
};

// 生图方法
async function generateImage() {
  if (!fullPrompt.value) {
    alert('请至少填写一项描述内容');
    return;
  }

  isGenerating.value = true;

  try {
    console.log('🎨 开始调用Midjourney生成图片:', fullPrompt.value);

    // 解析Midjourney参数
    const midjourneyOptions = parseMidjourneyParameters();

    // 调用Midjourney API
    const response = await fetch('/api/midjourney/generate', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        prompt: fullPrompt.value,
        options: midjourneyOptions
      })
    });

    const data = await response.json();

    if (data.success) {
      console.log('✅ Midjourney生成任务已开始:', data);

      // 显示生成中状态
      alert(`Midjourney图片生成任务已开始！\n预计时间: ${data.estimatedTime}\n请稍后查看生成历史或Midjourney页面查看结果。`);

      // 开始轮询检查结果
      pollGenerationResult(data.taskId);

    } else {
      throw new Error(data.error || '生成请求失败');
    }

  } catch (error) {
    console.error('❌ 生成图像失败:', error);
    alert('生成图像失败: ' + error.message);
    isGenerating.value = false;
  }
}

// 解析Midjourney参数
function parseMidjourneyParameters() {
  const options = {};

  // 解析宽高比参数
  const aspectRatioParam = activeParameters.value.find(param => param.startsWith('--ar '));
  if (aspectRatioParam) {
    options.aspect_ratio = aspectRatioParam.replace('--ar ', '');
  }

  // 解析质量参数
  const qualityParam = activeParameters.value.find(param => param.startsWith('--q '));
  if (qualityParam) {
    options.quality = qualityParam.replace('--q ', '');
  }

  // 解析风格化参数
  const stylizeParam = activeParameters.value.find(param => param.startsWith('--s '));
  if (stylizeParam) {
    options.stylize = parseInt(stylizeParam.replace('--s ', ''));
  }

  // 解析混乱度参数
  const chaosParam = activeParameters.value.find(param => param.startsWith('--c '));
  if (chaosParam) {
    options.chaos = parseInt(chaosParam.replace('--c ', ''));
  }

  // 解析模型版本参数
  const versionParam = activeParameters.value.find(param => param.startsWith('--v '));
  if (versionParam) {
    options.model = versionParam.replace('--v ', '');
  }

  console.log('📊 解析的Midjourney参数:', options);
  return options;
}

// 轮询检查生成结果
async function pollGenerationResult(taskId) {
  const maxAttempts = 60; // 最多检查5分钟
  let attempts = 0;

  const checkResult = async () => {
    try {
      attempts++;

      // 获取Midjourney生成历史
      const response = await fetch('/api/midjourney/history?limit=10');
      const data = await response.json();

      if (data.success && data.history.length > 0) {
        // 查找最近的成功生成记录
        const recentSuccess = data.history.find(item =>
          item.success !== false &&
          item.result?.image_url &&
          item.prompt === fullPrompt.value
        );

        if (recentSuccess) {
          console.log('✅ 找到生成结果:', recentSuccess);

          // 添加到生成结果
          const newImage = {
            id: Date.now() + Math.random(),
            url: recentSuccess.result.image_url,
            prompt: recentSuccess.prompt,
            timestamp: new Date(recentSuccess.timestamp),
            midjourneyData: recentSuccess
          };

          generatedImages.value = [newImage, ...generatedImages.value];
          isGenerating.value = false;

          alert('🎉 Midjourney图片生成完成！');
          return;
        }
      }

      // 如果还没有结果且未超时，继续检查
      if (attempts < maxAttempts) {
        setTimeout(checkResult, 5000); // 每5秒检查一次
      } else {
        console.log('⏰ 轮询超时，停止检查');
        isGenerating.value = false;
        alert('生成时间较长，请稍后在Midjourney页面或生成历史中查看结果。');
      }

    } catch (error) {
      console.error('检查生成结果失败:', error);
      if (attempts < maxAttempts) {
        setTimeout(checkResult, 5000);
      } else {
        isGenerating.value = false;
      }
    }
  };

  // 开始检查
  setTimeout(checkResult, 10000); // 10秒后开始第一次检查
}

function clearInputs() {
  promptData.value = {
    project: '',
    style: '',
    material: '',
    environment: '',
    lighting: '',
    composition: '',
    chinesePrompts: { // 新增，用于存储中文提示
      project: [],
      style: [],
      material: [],
      environment: [],
      lighting: [],
      composition: []
    }
  };
}

function fillFromLibrary(image) {
  // 清空现有数据
  clearInputs();

  if (image.promptFields && Array.isArray(image.promptFields)) {
    const mapping = {
      '项目': 'project',
      '风格': 'style',
      '材料': 'material',
      '环境': 'environment',
      '光影/氛围': 'lighting',
      '构图/出图指令': 'composition'
    };

    image.promptFields.forEach(field => {
      const key = mapping[field.name];
      if (key && promptData.value.hasOwnProperty(key)) {
        promptData.value[key] = field.value;
      }
    });
  } else if (image.prompt) {
    // Fallback for non-structured prompts
    promptData.value.project = image.prompt;
  }
  
  adjustAllTextareas();
}

function fillFromLibraryAndClose(image) {
  fillFromLibrary(image);
  showLibraryModal.value = false;
}

function saveToLibrary(image) {
  selectedImage.value = image;
  saveImageData.value = {
    name: promptData.value.project || '未命名图像',
    tags: ''
  };
  showSaveModal.value = true;
}

async function confirmSaveToLibrary() {
  try {
    loading.value = true
    const imageData = {
      url: selectedImage.value.url,
      name: saveImageData.value.name,
      tags: saveImageData.value.tags.split(',').map(tag => tag.trim()),
      promptData: { ...promptData.value },
      timestamp: new Date()
    }
    
    await imageAPI.createImage(imageData)
    
    // 重新加载图片库
    await loadLibraryImages()
    
    showSaveModal.value = false
    alert('图像已成功保存到图片库！')
  } catch (err) {
    console.error('保存图片失败:', err)
    alert('保存图片失败: ' + err.message)
  } finally {
    loading.value = false
  }
}

function downloadImage(image) {
  // 实际应用中应该触发真实的下载
  alert('在实际应用中，这将下载图像到您的设备');
}

function regenerateVariation(image) {
  isGenerating.value = true;
  
  // 模拟API调用
  setTimeout(() => {
    const variation = {
      id: Date.now(),
      url: `https://placehold.co/800x600/42b983/white?text=${encodeURIComponent('变体: ' + (promptData.value.project || '生成图像'))}`,
      prompt: fullPrompt.value,
      timestamp: new Date()
    };
    
    generatedImages.value = [variation, ...generatedImages.value];
    isGenerating.value = false;
  }, 2000);
}

function upscaleImage(image) {
  alert('在实际应用中，这将使用Midjourney的放大功能处理图像');
}

const activeParameters = ref([]);

function toggleParameter(param) {
  const paramPrefix = param.split(' ')[0];
  const index = activeParameters.value.findIndex(p => p.startsWith(paramPrefix));

  if (index > -1) {
    // If a parameter with the same prefix exists, remove it
    if (activeParameters.value[index] === param) {
      activeParameters.value.splice(index, 1);
    } else {
      activeParameters.value.splice(index, 1, param);
    }
  } else {
    // Otherwise, add the new parameter
    activeParameters.value.push(param);
  }
  updateComposition();
}

function isParameterActive(param) {
  return activeParameters.value.includes(param);
}

function hasParameterPrefix(prefix) {
  return activeParameters.value.some(p => p.startsWith(prefix));
}

function getParameterValue(prefix, defaultValue) {
  const param = activeParameters.value.find(p => p.startsWith(prefix));
  if (param) {
    const value = param.split(' ')[1];
    return value !== undefined ? value : defaultValue;
  }
  return defaultValue;
}

const showInputModal = ref(false);
const inputModalType = ref('number');
const inputModalTitle = ref('');
const inputModalParam = ref('');
const inputModalValue = ref(0);
const inputModalMin = ref(0);
const inputModalMax = ref(100);

function showValueInputModal(param, defaultValue, min, max) {
  const existingParam = activeParameters.value.find(p => p.startsWith(param));
  if (existingParam) {
    // If parameter exists, remove it and close modal logic
    const index = activeParameters.value.indexOf(existingParam);
    activeParameters.value.splice(index, 1);
    updateComposition();
    return;
  }
  
  inputModalType.value = 'number';
  inputModalTitle.value = `设置 ${param} 参数`;
  inputModalParam.value = param;
  inputModalValue.value = defaultValue;
  inputModalMin.value = min;
  inputModalMax.value = max;
  showInputModal.value = true;
}

function showUrlInputModal(param) {
    const existingParam = activeParameters.value.find(p => p.startsWith(param));
    if (existingParam) {
        const index = activeParameters.value.indexOf(existingParam);
        activeParameters.value.splice(index, 1);
        updateComposition();
        return;
    }
    inputModalType.value = 'url';
    inputModalTitle.value = `设置 ${param} 图片URL`;
    inputModalParam.value = param;
    inputModalValue.value = '';
    showInputModal.value = true;
}

function confirmParameterValue() {
  const param = inputModalParam.value;
  const value = inputModalValue.value;
  
  const newParam = `${param} ${value}`;
  
  const paramPrefix = param.split(' ')[0];
  const index = activeParameters.value.findIndex(p => p.startsWith(paramPrefix));

  if (index > -1) {
    activeParameters.value.splice(index, 1, newParam);
  } else {
    activeParameters.value.push(newParam);
  }
  
  showInputModal.value = false;
  updateComposition();
}

function copyAllParameters() {
  navigator.clipboard.writeText(activeParameters.value.join(' '));
}

function clearAllParameters() {
  activeParameters.value = [];
  updateComposition();
}

function updateComposition() {
  const existingParams = promptData.value.composition.split(' ').filter(p => !p.startsWith('--'));
  promptData.value.composition = [...existingParams, ...activeParameters.value].join(' ');
}

const showPromptSelector = ref(false);
const currentField = ref('');
const promptCategories = ref([]);
const fieldPrompts = ref({});

async function openPromptSelector(field) {
  currentField.value = field;
  showPromptSelector.value = true; // 立即打开弹窗以提供即时反馈

  console.log(`[Debug] 打开提示词选择器 for field: "${field}"`);

  // 确保分类已加载
  if (!promptCategories.value || promptCategories.value.length === 0) {
    console.log('[Debug] 提示词分类为空, 尝试重新加载...');
    await loadPromptCategories();
  }
  console.log('[Debug] 当前提示词分类:', JSON.parse(JSON.stringify(promptCategories.value)));

  // 初始化字段的提示词为空数组
  fieldPrompts.value[field] = [];

  try {
    const fieldMapping = {
      project: '项目',
      style: '风格',
      material: '材料',
      environment: '环境',
      lighting: '光影氛围',
      composition: '构图出图'
    };
    
    const categoryName = fieldMapping[field];
    console.log(`[Debug] 字段 "${field}" 映射到分类名: "${categoryName}"`);

    if (categoryName) {
      const category = promptCategories.value.find(c => c.name && c.name.trim() === categoryName.trim());
      
      console.log(`[Debug] 查找到的分类:`, category ? JSON.parse(JSON.stringify(category)) : '未找到');

      if (category && category.id) {
        console.log(`[Debug] 正在为分类ID "${category.id}" 获取提示词...`);
        const response = await fetchPrompts(category.id);
        console.log(`[Debug] 获取提示词API响应 for "${categoryName}":`, response);

        if (response.success && Array.isArray(response.data)) {
          fieldPrompts.value[field] = response.data;
          console.log(`[Debug] 成功为字段 "${field}" 设置了 ${response.data.length} 个提示词.`);
        } else {
          console.warn(`[Debug] API调用成功但未返回有效的提示词数据 for field "${field}". Response:`, response);
        }
      } else {
        console.warn(`[Debug] 未能为字段 "${field}" 找到匹配的分类或分类ID.`);
      }
    } else {
      console.warn(`[Debug] 字段 "${field}" 没有定义分类映射.`);
    }
  } catch (error) {
    console.error(`[Debug] 加载字段 "${field}" 的提示词时发生错误:`, error);
  }
  
  console.log(`[Debug] 最终为字段 "${field}" 准备的提示词:`, JSON.parse(JSON.stringify(getPromptsForField(field))));
}

// 获取默认提示词
async function getDefaultPrompts(field) {
  try {
    // 从接口获取提示词数据
    const fieldMapping = {
      project: '项目',
      style: '风格',
      material: '材料',
      environment: '环境',
      lighting: '光影氛围',
      composition: '构图出图'
    };
    
    const categoryName = fieldMapping[field];
    if (categoryName) {
      // 查找匹配的分类
      const category = promptCategories.value.find(c => 
        c.name.includes(categoryName) || 
        c.name.includes('项目') && field === 'project' ||
        c.name.includes('风格') && field === 'style' ||
        c.name.includes('材料') && field === 'material' ||
        c.name.includes('环境') && field === 'environment' ||
        c.name.includes('光影') && field === 'lighting' ||
        c.name.includes('构图') && field === 'composition'
      );
      
      if (category) {
        const response = await fetchPrompts(category.id);
        if (response.success) {
          return response.data;
        }
      }
    }
    
    // 如果接口获取失败，返回空数组
    return [];
  } catch (error) {
    console.error('获取默认提示词失败:', error);
    return [];
  }
}

function getFieldName(field) {
  const names = {
    project: '项目',
    style: '风格',
    material: '材料',
    environment: '环境',
    lighting: '光影/氛围',
    composition: '构图/出图指令'
  };
  return names[field] || '';
}

function getPromptsForField(field) {
  return fieldPrompts.value[field] || [];
}

function selectPrompt(prompt) {
  const currentValue = promptData.value[currentField.value];
  const separator = currentValue && !currentValue.endsWith(',') ? ', ' : '';
  promptData.value[currentField.value] = currentValue + separator + prompt.content;
  showPromptSelector.value = false;
}

// 检查Midjourney配置状态
async function checkMidjourneyStatus() {
  try {
    const response = await fetch('/api/midjourney/status');
    const data = await response.json();

    if (data.success) {
      midjourneyStatus.value = data.status;
      console.log('🎨 Midjourney状态:', data.status);
    } else {
      console.error('检查Midjourney状态失败:', data.error);
    }
  } catch (error) {
    console.error('检查Midjourney状态失败:', error);
    midjourneyStatus.value = {
      configured: false,
      available: false
    };
  }
}
</script>

<style scoped>
.image-generation-view {
  display: flex;
  flex-direction: column;
  height: 100%;
  position: relative;
}

.page-title {
  font-size: var(--font-size-xl);
  color: var(--primary-color);
  margin-bottom: var(--spacing-xs);
}

.page-description {
  color: var(--secondary-color);
  margin-bottom: var(--spacing-lg);
  opacity: 0.8;
}

.generation-container {
  display: flex;
  gap: var(--spacing-lg);
  flex: 1;
  overflow: hidden;
}

.input-section {
  flex: 1;
  background-color: white;
  border-radius: var(--border-radius);
  padding: var(--spacing-lg);
  box-shadow: var(--shadow);
  overflow-y: auto;
}

.result-section {
  flex: 1;
  background-color: white;
  border-radius: var(--border-radius);
  padding: var(--spacing-lg);
  box-shadow: var(--shadow);
  overflow-y: auto;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--spacing-md);
  padding-bottom: var(--spacing-xs);
  border-bottom: 1px solid var(--border-color);
}

.section-title {
  color: var(--secondary-color);
  margin: 0;
  font-size: var(--font-size-lg);
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--spacing-md);
  padding-bottom: var(--spacing-xs);
  border-bottom: 1px solid var(--border-color);
}

.midjourney-link {
  display: inline-flex;
  align-items: center;
  padding: 8px 16px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  text-decoration: none;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 500;
  transition: all 0.3s ease;
}

.midjourney-link:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);
  color: white;
  text-decoration: none;
}

.midjourney-warning {
  margin: 15px 0;
  padding: 15px;
  background: #fff3cd;
  border: 1px solid #ffeaa7;
  border-radius: 8px;
  border-left: 4px solid #f39c12;
}

.warning-content {
  display: flex;
  align-items: flex-start;
  gap: 12px;
}

.warning-icon {
  font-size: 1.2rem;
  margin-top: 2px;
}

.warning-text {
  flex: 1;
}

.warning-text p {
  margin: 0 0 5px 0;
  color: #856404;
  font-size: 14px;
}

.warning-text p:last-child {
  margin-bottom: 0;
}

.warning-text a {
  color: #856404;
  font-weight: 500;
  text-decoration: underline;
}

.warning-text a:hover {
  color: #533f03;
}

.library-btn {
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
  background-color: var(--primary-color);
  color: white;
  border: none;
  border-radius: var(--border-radius);
  padding: 6px 12px;
  font-size: var(--font-size-sm);
  cursor: pointer;
  transition: all var(--transition-speed);
  position: relative;
  z-index: 1;
}

.library-btn:hover {
  background-color: #2c3e50; /* 深蓝灰色，确保不是白色 */
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.btn-icon {
  font-size: 1.2em;
}

.structured-inputs {
  display: grid;
  gap: var(--spacing-md);
}

.field-hint {
  font-size: var(--font-size-sm);
  color: #888;
  font-weight: normal;
}

.preview-prompt {
  margin-top: var(--spacing-lg);
  margin-bottom: var(--spacing-lg);
}

.prompt-box {
  background-color: var(--bg-light);
  padding: var(--spacing-md);
  border-radius: var(--border-radius);
  border: 1px solid var(--border-color);
  min-height: 60px;
  white-space: pre-wrap;
  word-break: break-word;
  font-family: monospace;
  line-height: 1.5;
}

.action-buttons {
  display: flex;
  gap: var(--spacing-md);
  margin-top: var(--spacing-md);
}

.generating-indicator {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 200px;
}

.spinner {
  width: 40px;
  height: 40px;
  border: 4px solid rgba(0, 0, 0, 0.1);
  border-radius: 50%;
  border-top-color: var(--primary-color);
  animation: spin 1s ease-in-out infinite;
  margin-bottom: var(--spacing-md);
}

@keyframes spin {
  to { transform: rotate(360deg); }
}

.generation-tip {
  color: #888;
  font-size: var(--font-size-sm);
  margin-top: var(--spacing-sm);
}

.image-results {
  display: grid;
  gap: var(--spacing-md);
}

.image-result-item {
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius);
  overflow: hidden;
}

.result-image {
  width: 100%;
  height: auto;
  display: block;
}

.image-actions {
  display: flex;
  flex-wrap: wrap;
  gap: var(--spacing-xs);
  padding: var(--spacing-sm);
  background-color: var(--bg-light);
}

.action-btn {
  padding: var(--spacing-xs) var(--spacing-sm);
  background-color: white;
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius);
  font-size: var(--font-size-sm);
  cursor: pointer;
  transition: background-color var(--transition-speed);
}

.action-btn:hover {
  background-color: var(--bg-light);
}

.empty-result {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 200px;
  color: #888;
}

.empty-icon {
  font-size: 3rem;
  margin-bottom: var(--spacing-md);
}

/* 左侧边栏样式 */
.sidebar-left {
  width: 280px;
  background-color: white;
  border-radius: var(--border-radius);
  padding: var(--spacing-md);
  box-shadow: var(--shadow);
  overflow-y: auto;
  max-height: 100%;
}

.parameter-group {
  margin-bottom: var(--spacing-md);
}

.parameter-group-title {
  font-size: var(--font-size-md);
  color: var(--secondary-color);
  margin-bottom: var(--spacing-sm);
  padding-bottom: var(--spacing-xs);
  border-bottom: 1px solid var(--border-color);
}

.parameter-list {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-xs);
}

.parameter-actions {
  display: flex;
  gap: var(--spacing-sm);
  margin-bottom: var(--spacing-md);
}

.param-action-btn {
  flex: 1;
  padding: var(--spacing-xs) var(--spacing-sm);
  background-color: white;
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius);
  font-size: var(--font-size-sm);
  cursor: pointer;
  transition: background-color var(--transition-speed);
  display: flex;
  align-items: center;
  justify-content: center;
}

.param-action-btn:hover {
  background-color: var(--bg-light);
}

.action-icon {
  margin-right: 4px;
}

.parameter-item {
  padding: var(--spacing-xs);
  border-radius: var(--border-radius);
  background-color: var(--bg-light);
  cursor: pointer;
  transition: all var(--transition-speed);
  display: flex;
  flex-wrap: wrap;
  position: relative;
}

.parameter-item:hover {
  background-color: var(--primary-color-light);
}

.parameter-active {
  background-color: var(--primary-color-light);
  border-left: 3px solid var(--primary-color);
}

.parameter-name {
  display: block;
  font-weight: bold;
  font-family: monospace;
  color: var(--primary-color);
  flex: 1;
}

.parameter-desc {
  display: block;
  font-size: var(--font-size-sm);
  color: var(--secondary-color);
  margin-top: 2px;
  width: 100%;
}

.parameter-status {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 24px;
  height: 24px;
  border-radius: 50%;
  background-color: var(--bg-light);
  color: var(--primary-color);
  font-weight: bold;
}

/* 右侧边栏样式 */
.sidebar-right {
  position: absolute;
  right: 0;
  top: 0;
  width: 250px;
  height: 100%;
  background-color: white;
  border-left: 1px solid var(--border-color);
  padding: var(--spacing-md);
  overflow-y: auto;
  box-shadow: -2px 0 5px rgba(0, 0, 0, 0.05);
}

.sidebar-title {
  font-size: var(--font-size-lg);
  color: var(--secondary-color);
  margin-bottom: var(--spacing-xs);
}

.sidebar-description {
  font-size: var(--font-size-sm);
  color: #888;
  margin-bottom: var(--spacing-md);
}

.library-images {
  display: grid;
  gap: var(--spacing-md);
}

.library-image-item {
  position: relative;
  border-radius: var(--border-radius);
  overflow: hidden;
  cursor: pointer;
}

.library-image {
  width: 100%;
  height: auto;
  display: block;
  transition: transform var(--transition-speed);
}

.image-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transition: opacity var(--transition-speed);
}

.overlay-text {
  color: white;
  font-weight: bold;
}

.library-image-item:hover .image-overlay {
  opacity: 1;
}

.library-image-item:hover .library-image {
  transform: scale(1.05);
}

/* 图片库网格样式 */
.modal-description {
  margin-bottom: var(--spacing-md);
  color: #666;
}

/* 图片库筛选样式 */
.library-filter {
  margin-bottom: var(--spacing-lg); /* 增加与图片之间的间距 */
}

.filter-row {
  display: flex;
  gap: var(--spacing-md);
  align-items: center;
}

.filter-row .form-input {
  flex: 1;
}

.select-input {
  min-width: 120px;
}

/* 图片库网格样式 */
.library-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: var(--spacing-md);
  margin-bottom: var(--spacing-md);
}

.library-grid-item {
  position: relative;
  border-radius: var(--border-radius);
  overflow: hidden;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  cursor: pointer;
  transition: transform var(--transition-speed);
}

.library-grid-image {
  width: 100%;
  aspect-ratio: 1 / 1;
  object-fit: cover;
  display: block;
}

.library-grid-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transition: opacity var(--transition-speed);
}

.library-grid-item:hover .library-grid-overlay {
  opacity: 1;
}

.library-grid-item:hover {
  transform: translateY(-3px);
}

.library-grid-info {
  padding: var(--spacing-sm);
  background-color: white;
}

.library-image-name {
  margin: 0 0 var(--spacing-xs) 0;
  font-size: var(--font-size-sm);
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.library-image-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
}

.tag {
  background-color: var(--bg-light);
  color: var(--text-color);
  font-size: 0.7rem;
  padding: 2px 6px;
  border-radius: 12px;
}

/* 模态框样式 */
.modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.modal-content {
  background-color: white;
  border-radius: var(--border-radius);
  width: 600px;
  max-width: 90%;
  max-height: 90vh;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--spacing-md);
  border-bottom: 1px solid var(--border-color);
}

.modal-header h3 {
  margin: 0;
  color: var(--secondary-color);
}

.close-btn {
  background: none;
  border: none;
  font-size: 1.5rem;
  cursor: pointer;
  color: #888;
}

.modal-body {
  padding: var(--spacing-md);
  overflow-y: auto;
}

.selected-image {
  margin-bottom: var(--spacing-md);
  text-align: center;
}

.preview-image {
  max-width: 100%;
  max-height: 300px;
  border-radius: var(--border-radius);
}

.structured-data-preview {
  margin-top: var(--spacing-md);
  background-color: var(--bg-light);
  padding: var(--spacing-md);
  border-radius: var(--border-radius);
}

.structured-data-preview h4 {
  margin-top: 0;
  margin-bottom: var(--spacing-sm);
  color: var(--secondary-color);
}

.data-preview p {
  margin: var(--spacing-xs) 0;
}

.modal-footer {
  padding: var(--spacing-md);
  border-top: 1px solid var(--border-color);
  display: flex;
  justify-content: flex-end;
  gap: var(--spacing-sm);
}

@media (max-width: 1200px) {
  .sidebar-right {
    display: none;
  }
}

@media (max-width: 992px) {
  .sidebar-left {
    width: 220px;
  }
}

@media (max-width: 768px) {
  .generation-container {
    flex-direction: column;
  }
  
  .sidebar-left {
    width: 100%;
    margin-bottom: var(--spacing-md);
  }
  
  .input-section,
  .result-section {
    width: 100%;
  }
  
  .parameter-groups {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
    gap: var(--spacing-md);
  }
}

/* 输入框与按钮组合样式 */
.input-with-button {
  display: flex;
  gap: var(--spacing-xs);
  align-items: center;
}

.input-with-button .form-input {
  flex: 1;
}

.prompt-btn {
  background-color: var(--primary-color-light);
  color: var(--primary-color);
  border: 1px solid var(--primary-color);
  border-radius: var(--border-radius);
  padding: 0.5rem 0.75rem;
  font-size: var(--font-size-sm);
  cursor: pointer;
  white-space: nowrap;
  transition: all var(--transition-speed);
}

.prompt-btn:hover {
  background-color: var(--primary-color);
  color: white;
}

/* 提示词选择器样式 */
.prompt-selector-list {
  max-height: 300px;
  overflow-y: auto;
  display: grid;
  gap: var(--spacing-xs);
}

.prompt-selector-item {
  padding: var(--spacing-sm);
  border-radius: var(--border-radius);
  background-color: var(--bg-light);
  cursor: pointer;
  transition: all var(--transition-speed);
}

.prompt-selector-item:hover {
  background-color: var(--primary-color-light);
}

.prompt-selector-text {
  font-size: var(--font-size-md);
}

/* 中文提示词样式 */
.chinese-prompts {
  margin-top: 4px;
  display: flex;
  flex-wrap: wrap;
  gap: 6px;
}

.chinese-prompt-item {
  color: var(--primary-color);
  font-size: var(--font-size-sm);
  background-color: var(--primary-color-light);
  padding: 2px 6px;
  border-radius: 4px;
  display: inline-block;
}
</style>