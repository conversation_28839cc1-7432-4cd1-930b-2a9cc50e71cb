# AIDE 产品功能说明书

## 项目概述

**AIDE (AI-Integrated Design Environment)** 是一个专为建筑、室内、景观及城市规划设计师打造的一站式 Web 应用。通过深度集成 Deepseek、豆包 (Doubao)、Midjourney 等AI服务，并结合结构化的专业知识库、大师库、图片库和话术库，构建一个集创意激发、知识查询、方案推演和素材管理于一体的综合性设计辅助平台。

### 技术架构
- **前端框架**: Vue 3 + Vue Router 4
- **构建工具**: Vite
- **开发语言**: JavaScript
- **UI风格**: 现代化响应式设计

### 核心价值
1. **集成化工作流**: 在单一平台内完成从AI对话、知识检索到AI图像生成的完整设计辅助流程，大幅提升效率
2. **专业化AI**: 通过可定制的"知识库"和"大师库"，将通用大模型调教为具备深度行业背景的专业顾问
3. **结构化创意**: 独创的结构化"生图"和"图片库"系统，将感性的图像创意转化为可复用、可迭代的结构化数据，实现精准的二次创作

---

## 功能模块详述

### 1. 用户认证系统

#### 1.1 登录功能 (`LoginView.vue`)
- **功能描述**: 用户身份验证和系统访问控制
- **主要特性**:
  - 用户名/密码登录验证
  - 管理员账号特殊处理 (admin/123456)
  - 表单验证和错误提示
  - 登录状态持久化 (localStorage)
  - 自动重定向机制
- **用户体验**: 简洁的登录界面，支持回车键快速登录

#### 1.2 注册功能 (`RegisterView.vue`)
- **功能描述**: 新用户账号创建
- **主要特性**:
  - 用户名、密码、确认密码、显示名称输入
  - 实时表单验证
  - 密码强度检查
  - 用户名重复检查
  - 注册成功后自动登录

#### 1.3 个人资料管理 (`ProfileView.vue`)
- **功能描述**: 用户个人信息管理
- **主要特性**:
  - 头像上传和预览
  - 显示名称修改
  - 密码修改功能
  - 表单验证和错误处理
  - 实时保存状态反馈

### 2. 主界面和导航系统 (`App.vue`)

#### 2.1 应用布局
- **侧边栏导航**: 包含所有主要功能模块的快速访问
- **响应式设计**: 适配不同屏幕尺寸
- **用户信息展示**: 头像、用户名、角色显示
- **用户菜单**: 个人资料编辑、退出登录

#### 2.2 导航菜单
- 🏠 首页 - 功能概览和快速入口
- 💬 聊天 - AI对话中心
- 🖼️ 生图 - AI图像生成
- 📚 知识库 - 个人知识管理
- 👨‍🎨 大师库 - 设计大师理念库
- 🏞️ 图片库 - 图像资源管理
- 💭 提示词库 - 生图提示词管理
- 📝 话术库 - 常用问题模板
- ⚙️ 设置 - 系统配置

### 3. 首页功能 (`HomeView.vue`)

#### 3.1 功能概览
- **欢迎界面**: AIDE品牌展示和产品介绍
- **功能卡片**: 6个主要功能模块的快速访问入口
- **产品介绍**: 详细的关于AIDE和核心价值说明

#### 3.2 快速导航
每个功能卡片包含:
- 功能图标和名称
- 功能描述
- "开始使用"按钮直接跳转

### 4. AI聊天系统 (`ChatView.vue`)

#### 4.1 核心功能
- **多模型支持**: 集成多种大语言模型选择
- **会话管理**: 创建、重命名、删除历史会话
- **消息选择**: 支持选择特定消息参与后续对话
- **联网搜索**: 可选的网络搜索增强功能

#### 4.2 增强工具
- 📎 **文件上传**: 支持文档上传和分析
- 📚 **知识库引用**: 引用个人知识库内容
- 👨‍🎨 **大师观点**: 引用设计大师理念
- 📝 **话术库**: 使用预设问题模板
- 🧹 **上下文清理**: 清空对话上下文

#### 4.3 用户体验
- 实时消息显示
- 消息时间戳
- 消息格式化显示
- 侧边栏历史会话快速切换

### 5. AI图像生成系统 (`ImageGenerationView.vue`)

#### 5.1 Midjourney参数控制
- **宽高比设置**: 16:9, 4:5, 1:1, 9:16等多种比例
- **风格控制**: 
  - 原始风格 (--style raw)
  - 风格化程度 (--stylize 0-1000)
  - 变化性控制 (--chaos 0-100)
  - 奇异度设置 (--weird 0-3000)
- **特殊效果**:
  - 平铺图案生成 (--tile)
  - 版本控制 (--v 7)
  - 内容排除 (--no people)
  - 生成停止点 (--stop)

#### 5.2 参数管理
- 点击参数添加到指令
- 参数状态可视化
- 批量复制和清除功能
- 数值参数弹窗输入

#### 5.3 结构化生图
- 专业级图像生成引擎
- 参数化控制确保结果可重现
- 与图片库无缝集成

### 6. 知识库管理 (`KnowledgeBaseView.vue`)

#### 6.1 文件管理
- **文件上传**: 支持PDF、TXT、DOC、DOCX格式
- **元数据管理**: 标题、描述、标签系统
- **文件预览**: 文件类型图标和基本信息显示
- **搜索功能**: 全文搜索知识库内容

#### 6.2 组织结构
- 标签分类系统
- 文件大小和上传日期显示
- 批量操作支持
- 详情查看和编辑功能

#### 6.3 AI集成
- 知识库内容可在聊天中引用
- 为AI提供专业背景知识
- 提升AI回答的专业性和准确性

### 7. 大师库系统 (`MasterLibraryView.vue`)

#### 7.1 分类管理
- 建筑、室内、景观、规划等专业分类
- 分类标签页快速切换
- 搜索和筛选功能

#### 7.2 大师信息管理
- **基本信息**: 大师名称、简介、代表理念
- **视觉展示**: 大师照片或头像
- **标签系统**: 多维度标签分类
- **详情查看**: 完整的大师信息展示

#### 7.3 设计理念库
- 结构化存储设计思想
- 可在AI对话中引用大师观点
- 为设计决策提供理论支撑

### 8. 图片库管理 (`ImageLibraryView.vue`)

#### 8.1 图像组织
- **分类管理**: 自定义分类系统
- **视图模式**: 网格视图和列表视图切换
- **排序选项**: 按时间、名称等多种排序方式
- **搜索过滤**: 快速定位目标图像

#### 8.2 图像管理
- **上传功能**: 支持多种图像格式
- **元数据**: 名称、分类、标签、创建时间
- **预览功能**: 图像缩略图和详情查看
- **批量操作**: 多选和批量处理

#### 8.3 设计复用
- 可复用的个人灵感数据库
- 结构化存储设计参考
- 与生图功能联动

### 9. 提示词库 (`PromptLibraryView.vue`)

#### 9.1 分类体系
- 🏗️ **项目类型**: 不同建筑项目的提示词
- 🎨 **风格类型**: 各种设计风格描述
- 🧱 **材料类型**: 建筑材料相关词汇
- 🌍 **环境类型**: 环境和场景描述
- 💡 **光影氛围**: 光照和氛围效果
- 📐 **构图指令**: 构图和出图技术参数

#### 9.2 提示词管理
- **双语支持**: 中英文提示词对照
- **分类归档**: 按类型组织提示词
- **快速添加**: 简化的添加流程
- **编辑功能**: 提示词内容修改

#### 9.3 生图集成
- 为图像生成提供结构化内容
- 提高生图指令的专业性
- 确保生图结果的一致性

### 10. 话术库管理 (`ScriptLibraryView.vue`)

#### 10.1 模板管理
- **分类系统**: 按用途分类的问题模板
- **模板内容**: 名称、描述、标签
- **搜索功能**: 快速定位所需模板
- **使用统计**: 模板使用频率跟踪

#### 10.2 效率提升
- **一键使用**: 快速应用到聊天中
- **模板编辑**: 根据需要调整模板
- **批量管理**: 多个模板的组织和管理

#### 10.3 AI对话增强
- 提高与AI聊天的效率
- 标准化常用问题格式
- 确保问题表达的准确性

### 11. 系统设置 (`SettingsView.vue`)

#### 11.1 界面设置
- **主题选择**: 浅色、深色、跟随系统
- **主色调**: 多种颜色主题选择
- **字体大小**: 12px-20px可调节
- **动画效果**: 界面动画开关

#### 11.2 语言与区域
- **多语言支持**: 简体中文、English、日本語、한국어
- **日期格式**: 多种日期显示格式
- **时区设置**: 本地时区配置

#### 11.3 功能配置
- AI模型参数设置
- 文件上传限制配置
- 缓存和存储管理
- 隐私和安全设置

### 12. 用户管理系统 (`UserManagementView.vue`)

#### 12.1 用户列表
- **用户信息**: 头像、用户名、显示名称
- **角色管理**: 管理员和普通用户权限
- **登录状态**: 最后登录时间显示
- **批量操作**: 多用户管理功能

#### 12.2 权限控制
- **角色切换**: 管理员权限授予/撤销
- **用户编辑**: 用户信息修改
- **用户删除**: 用户账号删除确认
- **头像管理**: 用户头像上传和管理

#### 12.3 系统安全
- 管理员专用功能
- 用户操作日志
- 权限验证机制

---

## 路由系统 (`router/index.js`)

### 路由配置
- `/login` - 登录页面
- `/register` - 注册页面
- `/` - 首页
- `/chat` - 聊天页面
- `/image-generation` - 图像生成
- `/knowledge-base` - 知识库
- `/master-library` - 大师库
- `/image-library` - 图片库
- `/prompt-library` - 提示词库
- `/script-library` - 话术库
- `/settings` - 设置页面
- `/profile` - 个人资料

### 路由守卫
- **登录验证**: 未登录用户自动重定向到登录页
- **已登录重定向**: 已登录用户访问登录页重定向到首页
- **权限控制**: 基于用户状态的页面访问控制

---

## 数据管理

### 本地存储
- **用户状态**: localStorage存储登录状态
- **设置配置**: 用户偏好设置持久化
- **会话数据**: 聊天历史和临时数据

### 状态管理
- **脚本存储**: Pinia状态管理 (`store/scriptStore.js`)
- **响应式数据**: Vue 3 Composition API
- **组件通信**: 事件总线和props传递

---

## 用户体验设计

### 界面设计
- **现代化UI**: 简洁、直观的用户界面
- **响应式布局**: 适配各种设备屏幕
- **一致性**: 统一的设计语言和交互模式
- **可访问性**: 良好的键盘导航和屏幕阅读器支持

### 交互体验
- **即时反馈**: 操作结果的实时显示
- **加载状态**: 异步操作的进度指示
- **错误处理**: 友好的错误提示和恢复机制
- **快捷操作**: 键盘快捷键和右键菜单

### 性能优化
- **懒加载**: 路由级别的代码分割
- **缓存策略**: 静态资源和API响应缓存
- **虚拟滚动**: 大列表的性能优化
- **图片优化**: 图片压缩和懒加载

---

## 技术特性

### 前端技术栈
- **Vue 3**: 最新的Vue.js框架
- **Composition API**: 更好的逻辑复用和类型推导
- **Vue Router 4**: 现代化的路由管理
- **Vite**: 快速的构建工具
- **ES6+**: 现代JavaScript语法

### 开发工具
- **ESLint**: 代码质量检查
- **热重载**: 开发时的实时更新
- **模块化**: 组件化和模块化开发
- **TypeScript支持**: 可选的类型检查

### 浏览器兼容性
- 现代浏览器支持
- ES6+特性使用
- 响应式设计适配
- 渐进式Web应用特性

---

## 部署和维护

### 构建配置
- **开发环境**: `npm run dev` - 本地开发服务器
- **生产构建**: `npm run build` - 生产环境构建
- **预览模式**: `npm run preview` - 构建结果预览
- **代码检查**: `npm run lint` - ESLint代码检查

### 项目结构
```
src/
├── App.vue              # 主应用组件
├── main.js              # 应用入口文件
├── assets/              # 静态资源
│   └── main.css         # 全局样式
├── router/              # 路由配置
│   └── index.js         # 路由定义
├── store/               # 状态管理
│   └── scriptStore.js   # 脚本状态存储
└── views/               # 页面组件
    ├── HomeView.vue     # 首页
    ├── ChatView.vue     # 聊天页面
    ├── ImageGenerationView.vue  # 图像生成
    ├── KnowledgeBaseView.vue    # 知识库
    ├── MasterLibraryView.vue    # 大师库
    ├── ImageLibraryView.vue     # 图片库
    ├── PromptLibraryView.vue    # 提示词库
    ├── ScriptLibraryView.vue    # 话术库
    ├── SettingsView.vue         # 设置
    ├── LoginView.vue            # 登录
    ├── RegisterView.vue         # 注册
    ├── ProfileView.vue          # 个人资料
    └── UserManagementView.vue   # 用户管理
```

---

## 总结

AIDE是一个功能完整、设计精良的AI辅助设计平台，为设计师提供了从创意激发到方案实现的全流程支持。通过集成多种AI服务和结构化的知识管理系统，AIDE不仅提高了设计效率，还为设计师提供了专业的知识支撑和创意灵感来源。

### 主要优势
1. **一站式解决方案**: 集成聊天、生图、知识管理等多种功能
2. **专业化定制**: 针对设计行业的特殊需求进行优化
3. **结构化管理**: 将创意和知识进行结构化存储和管理
4. **用户友好**: 直观的界面设计和流畅的用户体验
5. **可扩展性**: 模块化设计便于功能扩展和维护

### 应用场景
- 建筑设计方案构思和优化
- 室内设计灵感收集和整理
- 景观规划概念设计
- 城市规划方案讨论
- 设计团队协作和知识共享
- 设计教育和学习辅助

AIDE代表了AI技术在设计领域应用的新方向，为设计师提供了强大而实用的数字化工具，助力设计创新和效率提升。