const express = require('express');
const router = express.Router();
const midjourneyService = require('../services/midjourneyService');

// 检查Midjourney服务状态
router.get('/status', async (req, res) => {
  try {
    const status = await midjourneyService.checkMidjourneyStatus();
    res.json({
      success: true,
      status,
      service: 'midjourney'
    });
  } catch (error) {
    console.error('❌ 检查Midjourney状态失败:', error);
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

// 生成图片
router.post('/generate', async (req, res) => {
  try {
    const { prompt, options = {} } = req.body;
    
    if (!prompt) {
      return res.status(400).json({
        success: false,
        error: '提示词不能为空'
      });
    }

    console.log('🎨 开始Midjourney图片生成:', prompt);
    
    // 异步生成图片
    midjourneyService.generateImage(prompt, options)
      .then(async (result) => {
        console.log('✅ Midjourney图片生成完成');
        
        // 保存生成记录
        await midjourneyService.saveGenerationRecord({
          prompt,
          options,
          result,
          type: 'generate'
        });
        
        // 这里可以通过WebSocket或其他方式通知前端
        // 暂时先记录日志
        console.log('📊 生成结果:', result);
      })
      .catch(async (error) => {
        console.error('❌ Midjourney图片生成失败:', error);
        
        // 保存失败记录
        await midjourneyService.saveGenerationRecord({
          prompt,
          options,
          error: error.message,
          type: 'generate',
          success: false
        });
      });

    // 立即返回响应，表示任务已开始
    res.json({
      success: true,
      message: 'Midjourney图片生成任务已开始',
      prompt,
      options,
      estimatedTime: '2-5分钟',
      taskId: Date.now().toString()
    });

  } catch (error) {
    console.error('❌ Midjourney生成请求失败:', error);
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

// 图片变换 (upscale, variation)
router.post('/transform', async (req, res) => {
  try {
    const { messageId, action, index, options = {} } = req.body;
    
    if (!messageId || !action || index === undefined) {
      return res.status(400).json({
        success: false,
        error: '缺少必要参数: messageId, action, index'
      });
    }

    if (!['upscale', 'variation'].includes(action)) {
      return res.status(400).json({
        success: false,
        error: '无效的动作，支持: upscale, variation'
      });
    }

    console.log(`🔄 开始Midjourney图片变换: ${action} ${index}`);
    
    // 异步执行变换
    midjourneyService.transformImage(messageId, action, index, options)
      .then(async (result) => {
        console.log('✅ Midjourney图片变换完成');
        
        // 保存变换记录
        await midjourneyService.saveGenerationRecord({
          messageId,
          action,
          index,
          options,
          result,
          type: 'transform'
        });
      })
      .catch(async (error) => {
        console.error('❌ Midjourney图片变换失败:', error);
        
        // 保存失败记录
        await midjourneyService.saveGenerationRecord({
          messageId,
          action,
          index,
          options,
          error: error.message,
          type: 'transform',
          success: false
        });
      });

    // 立即返回响应
    res.json({
      success: true,
      message: `Midjourney图片${action}任务已开始`,
      messageId,
      action,
      index,
      estimatedTime: '1-3分钟',
      taskId: Date.now().toString()
    });

  } catch (error) {
    console.error('❌ Midjourney变换请求失败:', error);
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

// 获取生成历史
router.get('/history', async (req, res) => {
  try {
    const { limit = 20 } = req.query;
    const history = await midjourneyService.getGenerationHistory(parseInt(limit));
    
    res.json({
      success: true,
      history,
      total: history.length
    });
  } catch (error) {
    console.error('❌ 获取Midjourney历史失败:', error);
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

// 获取统计信息
router.get('/stats', async (req, res) => {
  try {
    const stats = await midjourneyService.getStats();
    
    res.json({
      success: true,
      stats
    });
  } catch (error) {
    console.error('❌ 获取Midjourney统计失败:', error);
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

// 获取配置信息
router.get('/config', async (req, res) => {
  try {
    const status = await midjourneyService.checkMidjourneyStatus();
    
    res.json({
      success: true,
      config: {
        service: 'Midjourney',
        description: '通过Discord API与Midjourney Bot交互生成AI图片',
        features: [
          '🎨 AI图片生成',
          '🔄 图片放大和变体',
          '⚙️ 多种生成参数',
          '📊 生成历史记录',
          '🎯 高质量艺术风格',
          '🚀 异步任务处理'
        ],
        parameters: {
          prompt: '图片描述提示词',
          aspect_ratio: '宽高比 (如: 16:9, 1:1, 9:16)',
          quality: '质量等级 (0.25, 0.5, 1, 2)',
          stylize: '风格化程度 (0-1000)',
          chaos: '随机性 (0-100)',
          model: '模型版本 (5.2, 6等)'
        },
        status: status.configured ? '已配置' : '需要配置',
        requirements: [
          'Discord Bot Token',
          'Midjourney服务器频道ID',
          'Midjourney Bot权限'
        ]
      }
    });
  } catch (error) {
    console.error('❌ 获取Midjourney配置失败:', error);
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

// 下载图片
router.post('/download', async (req, res) => {
  try {
    const { imageUrl, filename } = req.body;
    
    if (!imageUrl) {
      return res.status(400).json({
        success: false,
        error: '图片URL不能为空'
      });
    }

    const savedPath = await midjourneyService.downloadImage(
      imageUrl, 
      filename || `midjourney_${Date.now()}.png`
    );
    
    res.json({
      success: true,
      message: '图片下载成功',
      path: savedPath,
      filename: filename || `midjourney_${Date.now()}.png`
    });

  } catch (error) {
    console.error('❌ Midjourney图片下载失败:', error);
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

// 测试连接
router.post('/test', async (req, res) => {
  try {
    const { token, channelId, serverId } = req.body;
    
    if (!token || !channelId) {
      return res.status(400).json({
        success: false,
        error: '缺少必要的配置参数'
      });
    }

    // 这里可以添加测试连接的逻辑
    // 暂时返回成功响应
    res.json({
      success: true,
      message: 'Midjourney连接测试成功',
      config: {
        hasToken: !!token,
        hasChannelId: !!channelId,
        hasServerId: !!serverId
      }
    });

  } catch (error) {
    console.error('❌ Midjourney连接测试失败:', error);
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

module.exports = router;
