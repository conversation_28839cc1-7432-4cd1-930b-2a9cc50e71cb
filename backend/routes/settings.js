/**
 * 设置管理路由
 * 
 * 功能说明：
 * - 用户个人设置管理
 * - 系统设置管理（管理员）
 */

const express = require('express');
const router = express.Router();

const db = require('../config/database');
const { validate, userSettingsSchema } = require('../middleware/validation');
const { authenticateToken, requireAdmin } = require('../middleware/auth');
const { asyncHandler, ValidationError, NotFoundError, createSuccessResponse } = require('../middleware/errorHandler');

// ==================== 用户设置管理 ====================

/**
 * @route GET /api/settings/user
 * @desc 获取当前用户设置
 * @access Private
 */
router.get('/user', authenticateToken, asyncHandler(async (req, res) => {
  const userId = req.user.id;
  
  // 获取用户设置
  const settings = await db.execute(
    'SELECT * FROM user_settings WHERE user_id = ?',
    [userId]
  );
  
  if (settings.length === 0) {
    // 如果没有设置记录，创建默认设置
    const defaultSettings = {
      theme: 'light',
      language: 'zh-CN',
      font_size: 14,
      font_family: 'system',
      enable_notifications: true,
      enable_sound: true,
      auto_save: true,
      show_line_numbers: true,
      word_wrap: true,
      tab_size: 2,
      enable_ai_suggestions: true,
      default_ai_model: 'gpt-4',
      max_tokens: 2048,
      temperature: 0.7,
      enable_web_search: false
    };
    
    await db.execute(
      `INSERT INTO user_settings (
        user_id, theme, language, font_size, font_family, enable_notifications, enable_sound,
        auto_save, show_line_numbers, word_wrap, tab_size, enable_ai_suggestions,
        default_ai_model, max_tokens, temperature, enable_web_search, created_at, updated_at
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, NOW(), NOW())`,
      [
        userId, defaultSettings.theme, defaultSettings.language, defaultSettings.font_size,
        defaultSettings.font_family, defaultSettings.enable_notifications, defaultSettings.enable_sound,
        defaultSettings.auto_save, defaultSettings.show_line_numbers, defaultSettings.word_wrap,
        defaultSettings.tab_size, defaultSettings.enable_ai_suggestions, defaultSettings.default_ai_model,
        defaultSettings.max_tokens, defaultSettings.temperature, defaultSettings.enable_web_search
      ]
    );
    
    // 重新获取设置
    const newSettings = await db.execute(
      'SELECT * FROM user_settings WHERE user_id = ?',
      [userId]
    );
    
    res.json(createSuccessResponse(newSettings[0]));
  } else {
    res.json(createSuccessResponse(settings[0]));
  }
}));

/**
 * @route PUT /api/settings/user
 * @desc 更新当前用户设置
 * @access Private
 */
router.put('/user', authenticateToken, validate(userSettingsSchema), asyncHandler(async (req, res) => {
  const userId = req.user.id;
  const {
    theme, language, fontSize, fontFamily, enableNotifications, enableSound,
    autoSave, showLineNumbers, wordWrap, tabSize, enableAiSuggestions,
    defaultAiModel, maxTokens, temperature, enableWebSearch
  } = req.body;
  
  // 检查用户设置是否存在
  const existing = await db.execute(
    'SELECT id FROM user_settings WHERE user_id = ?',
    [userId]
  );
  
  if (existing.length === 0) {
    throw new NotFoundError('用户设置不存在，请先获取设置以创建默认配置');
  }
  
  // 构建更新语句
  const updateFields = [];
  const updateValues = [];
  
  if (theme !== undefined) {
    updateFields.push('theme = ?');
    updateValues.push(theme);
  }
  
  if (language !== undefined) {
    updateFields.push('language = ?');
    updateValues.push(language);
  }
  
  if (fontSize !== undefined) {
    updateFields.push('font_size = ?');
    updateValues.push(fontSize);
  }
  
  if (fontFamily !== undefined) {
    updateFields.push('font_family = ?');
    updateValues.push(fontFamily);
  }
  
  if (enableNotifications !== undefined) {
    updateFields.push('enable_notifications = ?');
    updateValues.push(enableNotifications);
  }
  
  if (enableSound !== undefined) {
    updateFields.push('enable_sound = ?');
    updateValues.push(enableSound);
  }
  
  if (autoSave !== undefined) {
    updateFields.push('auto_save = ?');
    updateValues.push(autoSave);
  }
  
  if (showLineNumbers !== undefined) {
    updateFields.push('show_line_numbers = ?');
    updateValues.push(showLineNumbers);
  }
  
  if (wordWrap !== undefined) {
    updateFields.push('word_wrap = ?');
    updateValues.push(wordWrap);
  }
  
  if (tabSize !== undefined) {
    updateFields.push('tab_size = ?');
    updateValues.push(tabSize);
  }
  
  if (enableAiSuggestions !== undefined) {
    updateFields.push('enable_ai_suggestions = ?');
    updateValues.push(enableAiSuggestions);
  }
  
  if (defaultAiModel !== undefined) {
    updateFields.push('default_ai_model = ?');
    updateValues.push(defaultAiModel);
  }
  
  if (maxTokens !== undefined) {
    updateFields.push('max_tokens = ?');
    updateValues.push(maxTokens);
  }
  
  if (temperature !== undefined) {
    updateFields.push('temperature = ?');
    updateValues.push(temperature);
  }
  
  if (enableWebSearch !== undefined) {
    updateFields.push('enable_web_search = ?');
    updateValues.push(enableWebSearch);
  }
  
  if (updateFields.length > 0) {
    updateFields.push('updated_at = NOW()');
    updateValues.push(userId);
    
    await db.execute(
      `UPDATE user_settings SET ${updateFields.join(', ')} WHERE user_id = ?`,
      updateValues
    );
  }
  
  res.json(createSuccessResponse(null, '设置更新成功'));
}));

/**
 * @route POST /api/settings/user/reset
 * @desc 重置用户设置为默认值
 * @access Private
 */
router.post('/user/reset', authenticateToken, asyncHandler(async (req, res) => {
  const userId = req.user.id;
  
  // 删除现有设置
  await db.execute(
    'DELETE FROM user_settings WHERE user_id = ?',
    [userId]
  );
  
  // 创建默认设置
  const defaultSettings = {
    theme: 'light',
    language: 'zh-CN',
    font_size: 14,
    font_family: 'system',
    enable_notifications: true,
    enable_sound: true,
    auto_save: true,
    show_line_numbers: true,
    word_wrap: true,
    tab_size: 2,
    enable_ai_suggestions: true,
    default_ai_model: 'gpt-4',
    max_tokens: 2048,
    temperature: 0.7,
    enable_web_search: false
  };
  
  await db.execute(
    `INSERT INTO user_settings (
      user_id, theme, language, font_size, font_family, enable_notifications, enable_sound,
      auto_save, show_line_numbers, word_wrap, tab_size, enable_ai_suggestions,
      default_ai_model, max_tokens, temperature, enable_web_search, created_at, updated_at
    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, NOW(), NOW())`,
    [
      userId, defaultSettings.theme, defaultSettings.language, defaultSettings.font_size,
      defaultSettings.font_family, defaultSettings.enable_notifications, defaultSettings.enable_sound,
      defaultSettings.auto_save, defaultSettings.show_line_numbers, defaultSettings.word_wrap,
      defaultSettings.tab_size, defaultSettings.enable_ai_suggestions, defaultSettings.default_ai_model,
      defaultSettings.max_tokens, defaultSettings.temperature, defaultSettings.enable_web_search
    ]
  );
  
  // 获取新设置
  const newSettings = await db.execute(
    'SELECT * FROM user_settings WHERE user_id = ?',
    [userId]
  );
  
  res.json(createSuccessResponse(newSettings[0], '设置已重置为默认值'));
}));

// ==================== 系统设置管理（管理员） ====================

/**
 * @route GET /api/settings/system
 * @desc 获取系统设置
 * @access Private (Admin only)
 */
router.get('/system', authenticateToken, requireAdmin, asyncHandler(async (req, res) => {
  // 获取系统统计信息
  const stats = await db.execute(
    `SELECT 
       (SELECT COUNT(*) FROM users) as total_users,
       (SELECT COUNT(*) FROM users WHERE status = 'active') as active_users,
       (SELECT COUNT(*) FROM chat_sessions) as total_sessions,
       (SELECT COUNT(*) FROM messages) as total_messages,
       (SELECT COUNT(*) FROM knowledge_items) as total_knowledge_items,
       (SELECT COUNT(*) FROM master_items) as total_master_items,
       (SELECT COUNT(*) FROM image_items) as total_image_items,
       (SELECT COUNT(*) FROM prompt_items) as total_prompt_items,
       (SELECT COUNT(*) FROM scripts) as total_scripts,
       (SELECT SUM(file_size) FROM knowledge_items WHERE file_path IS NOT NULL) as total_knowledge_size,
       (SELECT SUM(file_size) FROM image_items) as total_image_size`
  );
  
  // 获取AI模型列表
  const aiModels = await db.execute(
    'SELECT * FROM ai_models ORDER BY name'
  );
  
  // 获取用户活动统计（最近7天）
  const userActivity = await db.execute(
    `SELECT DATE(created_at) as date, COUNT(*) as count
     FROM user_activity_logs
     WHERE created_at >= DATE_SUB(NOW(), INTERVAL 7 DAY)
     GROUP BY DATE(created_at)
     ORDER BY date`
  );
  
  // 获取消息统计（最近7天）
  const messageActivity = await db.execute(
    `SELECT DATE(created_at) as date, COUNT(*) as count
     FROM messages
     WHERE created_at >= DATE_SUB(NOW(), INTERVAL 7 DAY)
     GROUP BY DATE(created_at)
     ORDER BY date`
  );
  
  // 获取模型使用统计
  const modelUsage = await db.execute(
    `SELECT ai_model, COUNT(*) as usage_count
     FROM chat_sessions
     WHERE ai_model IS NOT NULL
     GROUP BY ai_model
     ORDER BY usage_count DESC`
  );
  
  res.json(createSuccessResponse({
    overview: stats[0],
    aiModels,
    userActivity,
    messageActivity,
    modelUsage
  }));
}));

/**
 * @route GET /api/settings/system/users
 * @desc 获取用户管理统计
 * @access Private (Admin only)
 */
router.get('/system/users', authenticateToken, requireAdmin, asyncHandler(async (req, res) => {
  // 获取用户统计
  const userStats = await db.execute(
    `SELECT 
       COUNT(*) as total_users,
       COUNT(CASE WHEN status = 'active' THEN 1 END) as active_users,
       COUNT(CASE WHEN status = 'inactive' THEN 1 END) as inactive_users,
       COUNT(CASE WHEN role = 'admin' THEN 1 END) as admin_users,
       COUNT(CASE WHEN created_at >= DATE_SUB(NOW(), INTERVAL 7 DAY) THEN 1 END) as new_users_week,
       COUNT(CASE WHEN created_at >= DATE_SUB(NOW(), INTERVAL 30 DAY) THEN 1 END) as new_users_month
     FROM users`
  );
  
  // 获取用户注册趋势（最近30天）
  const registrationTrend = await db.execute(
    `SELECT DATE(created_at) as date, COUNT(*) as count
     FROM users
     WHERE created_at >= DATE_SUB(NOW(), INTERVAL 30 DAY)
     GROUP BY DATE(created_at)
     ORDER BY date`
  );
  
  // 获取活跃用户（最近7天有活动）
  const activeUsers = await db.execute(
    `SELECT u.id, u.username, u.display_name, u.email, u.last_login_at,
            COUNT(ual.id) as activity_count
     FROM users u
     LEFT JOIN user_activity_logs ual ON u.id = ual.user_id AND ual.created_at >= DATE_SUB(NOW(), INTERVAL 7 DAY)
     WHERE u.status = 'active'
     GROUP BY u.id
     HAVING activity_count > 0
     ORDER BY activity_count DESC
     LIMIT 10`
  );
  
  res.json(createSuccessResponse({
    overview: userStats[0],
    registrationTrend,
    activeUsers
  }));
}));

/**
 * @route GET /api/settings/system/storage
 * @desc 获取存储使用统计
 * @access Private (Admin only)
 */
router.get('/system/storage', authenticateToken, requireAdmin, asyncHandler(async (req, res) => {
  // 获取存储统计
  const storageStats = await db.execute(
    `SELECT 
       (SELECT COUNT(*) FROM knowledge_items WHERE file_path IS NOT NULL) as knowledge_files,
       (SELECT SUM(file_size) FROM knowledge_items WHERE file_path IS NOT NULL) as knowledge_size,
       (SELECT COUNT(*) FROM image_items) as image_files,
       (SELECT SUM(file_size) FROM image_items) as image_size,
       (SELECT COUNT(*) FROM users WHERE avatar_url IS NOT NULL) as avatar_files`
  );
  
  // 获取文件类型分布
  const fileTypes = await db.execute(
    `SELECT 
       SUBSTRING_INDEX(file_name, '.', -1) as file_type,
       COUNT(*) as file_count,
       SUM(file_size) as total_size
     FROM knowledge_items
     WHERE file_path IS NOT NULL AND file_name IS NOT NULL
     GROUP BY file_type
     ORDER BY total_size DESC
     LIMIT 10`
  );
  
  // 获取大文件列表
  const largeFiles = await db.execute(
    `SELECT ki.file_name, ki.file_size, ki.created_at, u.display_name as uploader
     FROM knowledge_items ki
     LEFT JOIN users u ON ki.created_by = u.id
     WHERE ki.file_size IS NOT NULL
     ORDER BY ki.file_size DESC
     LIMIT 10`
  );
  
  res.json(createSuccessResponse({
    overview: storageStats[0],
    fileTypes,
    largeFiles
  }));
}));

/**
 * @route POST /api/settings/system/cleanup
 * @desc 执行系统清理
 * @access Private (Admin only)
 */
router.post('/system/cleanup', authenticateToken, requireAdmin, asyncHandler(async (req, res) => {
  const { type } = req.body;
  
  let cleanupResult = {
    type,
    deletedCount: 0,
    freedSpace: 0,
    message: ''
  };
  
  switch (type) {
    case 'old_logs':
      // 清理30天前的用户活动日志
      const logResult = await db.execute(
        'DELETE FROM user_activity_logs WHERE created_at < DATE_SUB(NOW(), INTERVAL 30 DAY)'
      );
      cleanupResult.deletedCount = logResult.affectedRows;
      cleanupResult.message = `已清理 ${logResult.affectedRows} 条旧日志记录`;
      break;
      
    case 'orphaned_files':
      // 这里可以添加清理孤立文件的逻辑
      cleanupResult.message = '孤立文件清理功能待实现';
      break;
      
    case 'temp_files':
      // 这里可以添加清理临时文件的逻辑
      cleanupResult.message = '临时文件清理功能待实现';
      break;
      
    default:
      throw new ValidationError('不支持的清理类型');
  }
  
  res.json(createSuccessResponse(cleanupResult, '清理操作完成'));
}));

/**
 * @route GET /api/settings/system/backup
 * @desc 获取备份信息
 * @access Private (Admin only)
 */
router.get('/system/backup', authenticateToken, requireAdmin, asyncHandler(async (req, res) => {
  // 这里可以添加获取备份信息的逻辑
  // 目前返回模拟数据
  const backupInfo = {
    lastBackup: null,
    backupSize: 0,
    backupLocation: process.env.BACKUP_DIR || './backups',
    autoBackupEnabled: false,
    backupFrequency: 'daily'
  };
  
  res.json(createSuccessResponse(backupInfo));
}));

/**
 * @route POST /api/settings/system/backup
 * @desc 创建系统备份
 * @access Private (Admin only)
 */
router.post('/system/backup', authenticateToken, requireAdmin, asyncHandler(async (req, res) => {
  // 这里可以添加创建备份的逻辑
  // 目前返回模拟响应
  const backupResult = {
    success: true,
    backupFile: `backup_${Date.now()}.sql`,
    backupSize: 0,
    createdAt: new Date().toISOString()
  };
  
  res.json(createSuccessResponse(backupResult, '备份创建成功'));
}));

// ==================== 提示词分类管理 ====================

/**
 * @route GET /api/settings/prompt-categories
 * @desc 获取提示词分类列表
 * @access Private
 */
router.get('/prompt-categories', authenticateToken, asyncHandler(async (req, res) => {
  const categories = await db.execute(
    `SELECT id, name, list_order as display_order,description, created_at, updated_at
     FROM prompt_categories 
     ORDER BY display_order ASC, name ASC`
  );
  
  res.json(createSuccessResponse(categories));
}));

/**
 * @route POST /api/settings/prompt-categories
 * @desc 创建提示词分类
 * @access Private
 */
router.post('/prompt-categories', authenticateToken, asyncHandler(async (req, res) => {
  const { name } = req.body;
  
  if (!name || name.trim().length === 0) {
    throw new ValidationError('分类名称不能为空');
  }
  
  // 检查分类名称是否已存在
  const [existing] = await db.execute(
    'SELECT id FROM prompt_categories WHERE name = ?',
    [name.trim()]
  );
  
  if (existing.length > 0) {
    throw new ValidationError('分类名称已存在');
  }
  
  // 获取最大排序值
  const [maxOrder] = await db.execute(
    'SELECT COALESCE(MAX(display_order), 0) as max_order FROM prompt_categories'
  );
  
  const newOrder = maxOrder[0].max_order + 1;
  
  // 创建分类
  const [result] = await db.execute(
    'INSERT INTO prompt_categories (name, display_order, created_at, updated_at) VALUES (?, ?, NOW(), NOW())',
    [name.trim(), newOrder]
  );
  
  const categoryId = result.insertId;
  
  // 获取创建的分类信息
  const [categories] = await db.execute(
    'SELECT id, name, display_order, created_at, updated_at FROM prompt_categories WHERE id = ?',
    [categoryId]
  );
  
  res.status(201).json(createSuccessResponse(categories[0], '分类创建成功'));
}));

/**
 * @route PUT /api/settings/prompt-categories/:id
 * @desc 更新提示词分类
 * @access Private
 */
router.put('/prompt-categories/:id', authenticateToken, asyncHandler(async (req, res) => {
  const categoryId = req.params.id;
  const { name } = req.body;
  
  // 检查分类是否存在
  const [categories] = await db.execute(
    'SELECT id FROM prompt_categories WHERE id = ?',
    [categoryId]
  );
  
  if (categories.length === 0) {
    throw new NotFoundError('分类不存在');
  }
  
  // 检查名称是否与其他分类冲突
  if (name) {
    const [existing] = await db.execute(
      'SELECT id FROM prompt_categories WHERE name = ? AND id != ?',
      [name.trim(), categoryId]
    );
    
    if (existing.length > 0) {
      throw new ValidationError('分类名称已存在');
    }
  }
  
  // 更新分类
  await db.execute(
    'UPDATE prompt_categories SET name = ?, updated_at = NOW() WHERE id = ?',
    [name.trim(), categoryId]
  );
  
  // 获取更新后的分类信息
  const [updatedCategories] = await db.execute(
    'SELECT id, name, display_order, created_at, updated_at FROM prompt_categories WHERE id = ?',
    [categoryId]
  );
  
  res.json(createSuccessResponse(updatedCategories[0], '分类更新成功'));
}));

/**
 * @route DELETE /api/settings/prompt-categories/:id
 * @desc 删除提示词分类
 * @access Private
 */
router.delete('/prompt-categories/:id', authenticateToken, asyncHandler(async (req, res) => {
  const categoryId = req.params.id;
  
  // 检查分类是否存在
  const [categories] = await db.execute(
    'SELECT id FROM prompt_categories WHERE id = ?',
    [categoryId]
  );
  
  if (categories.length === 0) {
    throw new NotFoundError('分类不存在');
  }
  
  // 检查是否有关联的提示词
  const [prompts] = await db.execute(
    'SELECT COUNT(*) as count FROM prompts WHERE category_id = ?',
    [categoryId]
  );
  
  if (prompts[0].count > 0) {
    throw new ValidationError('该分类下还有提示词，无法删除');
  }
  
  // 删除分类
  await db.execute(
    'DELETE FROM prompt_categories WHERE id = ?',
    [categoryId]
  );
  
  res.json(createSuccessResponse(null, '分类删除成功'));
}));

/**
 * @route PUT /api/settings/prompt-categories/order
 * @desc 更新提示词分类排序
 * @access Private
 */
router.put('/prompt-categories/order', authenticateToken, asyncHandler(async (req, res) => {
  const { orderedIds } = req.body;
  
  if (!Array.isArray(orderedIds)) {
    throw new ValidationError('排序ID列表格式错误');
  }
  
  // 开始事务
  const connection = await db.getConnection();
  await db.beginTransaction(connection);
  
  try {
    // 更新每个分类的排序值
    for (let i = 0; i < orderedIds.length; i++) {
      await db.execute(
        'UPDATE prompt_categories SET display_order = ? WHERE id = ?',
        [i + 1, orderedIds[i]],
        connection
      );
    }
    
    await db.commitTransaction(connection);
    res.json(createSuccessResponse(null, '排序更新成功'));
  } catch (error) {
    await db.rollbackTransaction(connection);
    throw error;
  }
}));

module.exports = router;