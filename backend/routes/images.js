/**
 * 图片库管理路由
 * 
 * 功能说明：
 * - 图片信息管理
 * - 图片分类管理
 * - 图片标签管理
 * - 提示词字段管理
 */

const express = require('express');
const router = express.Router();
const multer = require('multer');
const path = require('path');
const fs = require('fs').promises;

const db = require('../config/database');
const { validate, imageItemSchema, paginationSchema } = require('../middleware/validation');
const { authenticateToken, requireAdmin } = require('../middleware/auth');
const { asyncHandler, ValidationError, NotFoundError, AuthorizationError, createSuccessResponse } = require('../middleware/errorHandler');

// 配置文件上传
const storage = multer.diskStorage({
  destination: async (req, file, cb) => {
    const uploadDir = path.join(process.env.UPLOAD_DIR || '../frontend/public/uploads', 'images');
    try {
      await fs.mkdir(uploadDir, { recursive: true });
      cb(null, uploadDir);
    } catch (error) {
      cb(error);
    }
  },
  filename: (req, file, cb) => {
    const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
    cb(null, file.fieldname + '-' + uniqueSuffix + path.extname(file.originalname));
  }
});

const upload = multer({
  storage: storage,
  limits: {
    fileSize: parseInt(process.env.MAX_FILE_SIZE) || 10 * 1024 * 1024 // 10MB
  },
  fileFilter: (req, file, cb) => {
    const allowedTypes = (process.env.ALLOWED_FILE_TYPES || 'jpg,jpeg,png,gif,webp').split(',');
    const fileExt = path.extname(file.originalname).toLowerCase().slice(1);
    
    if (allowedTypes.includes(fileExt)) {
      cb(null, true);
    } else {
      cb(new ValidationError(`不支持的文件类型: ${fileExt}`));
    }
  }
});

// ==================== 分类管理 ====================

/**
 * @route GET /api/images/categories
 * @desc 获取图片库分类列表
 * @access Private
 */
router.get('/categories', authenticateToken, asyncHandler(async (req, res) => {
  const categories = await db.execute(
    `SELECT ic.id, ic.name, ic.description, ic.created_at, ic.updated_at,
            COUNT(ii.id) as image_count
     FROM image_categories ic
     LEFT JOIN image_items ii ON ic.id = ii.category_id
     GROUP BY ic.id
     ORDER BY ic.name`
  );

  
  // 确保返回的是数组格式
  const categoriesArray = Array.isArray(categories) ? categories : [categories];
  
  // 添加调试日志
  console.log('数据库查询结果:', categories);
  console.log('处理后返回的数据:', categoriesArray);
  console.log('是否为数组:', Array.isArray(categoriesArray));
  
  res.json(createSuccessResponse(categoriesArray));
}));

/**
 * @route POST /api/images/categories
 * @desc 创建图片库分类
 * @access Private (Admin only)
 */
router.post('/categories', authenticateToken, requireAdmin, asyncHandler(async (req, res) => {
  const { name, description } = req.body;
  
  if (!name || name.trim().length === 0) {
    throw new ValidationError('分类名称不能为空');
  }
  
  // 检查分类名称是否已存在
  const [existing] = await db.execute(
    'SELECT id FROM image_categories WHERE name = ?',
    [name.trim()]
  );
  
  if (existing.length > 0) {
    throw new ValidationError('分类名称已存在');
  }
  
  // 创建分类
  const [result] = await db.execute(
    'INSERT INTO image_categories (id, name, description, created_at, updated_at) VALUES (?, ?, ?, NOW(), NOW())',
    [name.toLowerCase().replace(/\s+/g, '_'), name.trim(), description || '']
  );
  
  // 获取创建的分类信息
  const [categories] = await db.execute(
    'SELECT id, name, description, created_at, updated_at FROM image_categories WHERE id = ?',
    [name.toLowerCase().replace(/\s+/g, '_')]
  );
  
  res.status(201).json(createSuccessResponse(categories[0], '分类创建成功'));
}));

/**
 * @route PUT /api/images/categories/:id
 * @desc 更新图片库分类
 * @access Private (Admin only)
 */
router.put('/categories/:id', authenticateToken, requireAdmin, asyncHandler(async (req, res) => {
  const categoryId = req.params.id;
  const { name, description } = req.body;
  
  // 检查分类是否存在
  const [categories] = await db.execute(
    'SELECT id FROM image_categories WHERE id = ?',
    [categoryId]
  );
  
  if (categories.length === 0) {
    throw new NotFoundError('分类不存在');
  }
  
  // 构建更新语句
  const updateFields = [];
  const updateValues = [];
  
  if (name !== undefined) {
    updateFields.push('name = ?');
    updateValues.push(name.trim());
  }
  
  if (description !== undefined) {
    updateFields.push('description = ?');
    updateValues.push(description);
  }
  
  if (updateFields.length > 0) {
    updateFields.push('updated_at = NOW()');
    updateValues.push(categoryId);
    
    await db.execute(
      `UPDATE image_categories SET ${updateFields.join(', ')} WHERE id = ?`,
      updateValues
    );
  }
  
  res.json(createSuccessResponse(null, '分类更新成功'));
}));

/**
 * @route DELETE /api/images/categories/:id
 * @desc 删除图片库分类
 * @access Private (Admin only)
 */
router.delete('/categories/:id', authenticateToken, requireAdmin, asyncHandler(async (req, res) => {
  const categoryId = req.params.id;
  
  // 检查分类是否存在
  const [categories] = await db.execute(
    'SELECT id FROM image_categories WHERE id = ?',
    [categoryId]
  );
  
  if (categories.length === 0) {
    throw new NotFoundError('分类不存在');
  }
  
  // 检查是否有关联的图片
  const [images] = await db.execute(
    'SELECT COUNT(*) as count FROM image_items WHERE category_id = ?',
    [categoryId]
  );
  
  if (images[0].count > 0) {
    throw new ValidationError('无法删除包含图片的分类');
  }
  
  // 删除分类
  await db.execute(
    'DELETE FROM image_categories WHERE id = ?',
    [categoryId]
  );
  
  res.json(createSuccessResponse(null, '分类删除成功'));
}));

// ==================== 图片信息管理 ====================

/**
 * @route GET /api/images
 * @desc 获取图片库列表
 * @access Private
 */
router.get('/', authenticateToken, asyncHandler(async (req, res) => {
  const userId = req.user.id;
  const { category, sort, search } = req.query;

  let query = `
    SELECT 
      i.id, i.title as name, i.description as prompt, i.file_path as url, i.file_size,
      i.created_at as createdAt, i.updated_at as updatedAt,
      c.name as category,
      u.username as createdBy
    FROM image_items i
    LEFT JOIN image_categories c ON i.category_id = c.id
    LEFT JOIN users u ON i.created_by = u.id
  `;
  
  const params = [];

  // 构建WHERE子句
  let whereClauses = [];
  if (category && category !== 'all') {
    whereClauses.push('i.category_id = ?');
    params.push(category);
  }
  if (search) {
    whereClauses.push('(i.title LIKE ? OR i.description LIKE ?)');
    params.push(`%${search}%`);
    params.push(`%${search}%`);
  }

  if (whereClauses.length > 0) {
    query += ' WHERE ' + whereClauses.join(' AND ');
  }

  // 构建ORDER BY子句
  let orderBy = ' ORDER BY i.created_at DESC';
  if (sort) {
    switch(sort) {
      case 'oldest': orderBy = ' ORDER BY i.created_at ASC'; break;
      case 'name_asc': orderBy = ' ORDER BY i.title ASC'; break;
      case 'name_desc': orderBy = ' ORDER BY i.title DESC'; break;
    }
  }
  query += orderBy;

  const images = await db.execute(query, params);

  // 对于每张图片，获取其标签和结构化提示词
  for (const image of images) {
    // 获取标签
    const tags = await db.execute(`
      SELECT t.name 
      FROM image_tags t
      JOIN image_item_tags iit ON t.id = iit.tag_id
      WHERE iit.item_id = ?
    `, [image.id]);
    image.tags = tags.map(t => t.name);

    // 获取结构化提示词
    const promptFields = await db.execute(`
      SELECT prompt_type as name, prompt_text as value 
      FROM image_prompts 
      WHERE image_id = ?
    `, [image.id]);
    image.promptFields = promptFields;
  }

  res.json(createSuccessResponse({ items: images }));
}));

/**
 * @route POST /api/images/upload
 * @desc 上传新图片
 * @access Private
 */
router.post('/upload', authenticateToken, upload.single('image'), asyncHandler(async (req, res) => {
  if (!req.file) {
    throw new ValidationError('没有上传文件');
  }
  
  const { title, description, categoryId, tags } = req.body;
  const imageUrl = `/uploads/images/${req.file.filename}`;

  // 插入图片信息到数据库
  const [result] = await db.execute(
    'INSERT INTO image_items (title, prompt, file_path, file_size, category_id, created_by, created_at, updated_at) VALUES (?, ?, ?, ?, ?, ?, NOW(), NOW())',
    [title || '未命名', description || '', imageUrl, req.file.size, categoryId || null, req.user.id]
  );
  const imageId = result.insertId;

  // 处理标签
  if (tags) {
    const tagList = tags.split(',').map(tag => tag.trim()).filter(tag => tag);
    if (tagList.length > 0) {
      const tagIds = [];
      for (const tagName of tagList) {
        let [tagRows] = await db.execute('SELECT id FROM image_tags WHERE name = ?', [tagName]);
        let tagId;
        if (tagRows.length > 0) {
          tagId = tagRows[0].id;
        } else {
          const [newTagResult] = await db.execute('INSERT INTO image_tags (name) VALUES (?)', [tagName]);
          tagId = newTagResult.insertId;
        }
        tagIds.push(tagId);
      }
      
      // 插入关联表
      const itemTagValues = tagIds.map(tagId => [imageId, tagId]);
      await db.query('INSERT INTO image_item_tags (item_id, tag_id) VALUES ?', [itemTagValues]);
    }
  }

  res.status(201).json(createSuccessResponse({ id: imageId, url: imageUrl }, '图片上传成功'));
}));

/**
 * @route PUT /api/images/:id
 * @desc 更新图片信息
 * @access Private
 */
router.put('/:id', authenticateToken, asyncHandler(async (req, res) => {
  const imageId = req.params.id;
  const { title, description, categoryId, tags, promptData, promptFields } = req.body;

  // 检查图片是否存在和权限
  const images = await db.execute('SELECT id, created_by FROM image_items WHERE id = ?', [imageId]);
  if (images.length === 0) {
    throw new NotFoundError('图片不存在');
  }
  if (images[0].created_by !== req.user.id && req.user.role !== 'admin') {
    throw new AuthorizationError('无权修改此图片');
  }

  // 根据 promptFields 生成新的 description
  const newDescription = promptFields
    .filter(field => field.value && field.value.trim() !== '')
    .map(field => `${field.name}: ${field.value.trim()}`)
    .join(', ');

  const updateFields = [];
  const updateValues = [];

  if (title !== undefined) {
    updateFields.push('title = ?');
    updateValues.push(title);
  }
  
  // 始终使用新生成的 description
  updateFields.push('description = ?');
  updateValues.push(newDescription);

  if (categoryId !== undefined) {
    updateFields.push('category_id = ?');
    updateValues.push(categoryId);
  }

  // 开始事务
  const connection = await db.getConnection();
  await connection.beginTransaction();

  try {
    // 更新基本信息
    if (updateFields.length > 0) {
      updateFields.push('updated_at = NOW()');
      updateValues.push(imageId);
      await connection.execute(`UPDATE image_items SET ${updateFields.join(', ')} WHERE id = ?`, updateValues);
    }

    // 更新标签
    if (tags !== undefined) {
      // 先删除旧的标签关联
      await connection.execute('DELETE FROM image_item_tags WHERE item_id = ?', [imageId]);

      const tagList = tags.map(tag => tag.trim()).filter(tag => tag);
      if (tagList.length > 0) {
        // 找到或创建新标签
        const tagIds = [];
        for (const tagName of tagList) {
          let [tagRows] = await connection.execute('SELECT id FROM image_tags WHERE name = ?', [tagName]);
          let tagId;
          if (tagRows.length > 0) {
            tagId = tagRows[0].id;
          } else {
            const [newTagResult] = await connection.execute('INSERT INTO image_tags (name) VALUES (?)', [tagName]);
            tagId = newTagResult.insertId;
          }
          tagIds.push(tagId);
        }
        
        // 插入新的关联
        const itemTagValues = tagIds.map(tagId => [imageId, tagId]);
        if (itemTagValues.length > 0) {
          await connection.query('INSERT INTO image_item_tags (item_id, tag_id) VALUES ?', [itemTagValues]);
        }
      }
    }

    // 更新结构化提示词
    if (promptFields !== undefined && Array.isArray(promptFields)) {
      // 1. 删除旧的提示词
      await connection.execute('DELETE FROM image_prompts WHERE image_id = ?', [imageId]);

      // 2. 插入新的提示词
      const promptValues = promptFields
        .filter(field => field.value && field.value.trim() !== '')
        .map(field => [imageId, field.name, field.value.trim()]);

      if (promptValues.length > 0) {
        await connection.query('INSERT INTO image_prompts (image_id, prompt_type, prompt_text) VALUES ?', [promptValues]);
      }
    }

    await connection.commit();
    res.json(createSuccessResponse(null, '图片信息更新成功'));
  } catch (error) {
    await connection.rollback();
    throw error;
  } finally {
    connection.release();
  }
}));

/**
 * @route DELETE /api/images/:id
 * @desc 删除图片
 * @access Private
 */
router.delete('/:id', authenticateToken, asyncHandler(async (req, res) => {
  const imageId = req.params.id;

  // 检查图片是否存在和权限
  const [images] = await db.execute('SELECT created_by, file_path as image_url FROM image_items WHERE id = ?', [imageId]);
  if (images.length === 0) {
    throw new NotFoundError('图片不存在');
  }
  const image = images[0];
  if (image.created_by !== req.user.id && req.user.role !== 'admin') {
    throw new AuthorizationError('无权删除此图片');
  }

  // 开始事务
  const connection = await db.getConnection();
  await db.beginTransaction(connection);

  try {
    // 从文件系统删除图片文件
    if (image.image_url) {
      const imagePath = path.join(process.cwd(), image.image_url);
      try {
        await fs.unlink(imagePath);
      } catch (fileError) {
        // 如果文件不存在，也继续执行，因为目标是删除记录
        if (fileError.code !== 'ENOENT') {
          throw fileError;
        }
      }
    }

    // 删除标签关联
    await db.execute('DELETE FROM image_item_tags WHERE item_id = ?', [imageId], connection);
    
    // 删除图片记录
    await db.execute('DELETE FROM image_items WHERE id = ?', [imageId], connection);

    await db.commitTransaction(connection);

    res.json(createSuccessResponse(null, '图片删除成功'));
  } catch (error) {
    await db.rollbackTransaction(connection);
    throw error;
  }
}));

// ==================== 标签管理 ====================

/**
 * @route GET /api/images/tags
 * @desc 获取图片库标签列表
 * @access Private
 */
router.get('/tags', authenticateToken, asyncHandler(async (req, res) => {
  const [tags] = await db.execute(
    `SELECT it.id, it.name, it.created_at,
            COUNT(iit.item_id) as usage_count
     FROM image_tags it
     LEFT JOIN image_item_tags iit ON it.id = iit.tag_id
     GROUP BY it.id
     ORDER BY usage_count DESC, it.name`
  );
  
  res.json(createSuccessResponse(tags));
}));

/**
 * @route GET /api/images/stats
 * @desc 获取图片库统计信息
 * @access Private
 */
router.get('/stats', authenticateToken, asyncHandler(async (req, res) => {
  const userId = req.user.id;
  
  // 获取统计信息
  const [stats] = await db.execute(
    `SELECT 
       (SELECT COUNT(*) FROM image_items) as total_images,
       (SELECT COUNT(*) FROM image_items WHERE created_by = ?) as my_images,
       (SELECT COUNT(*) FROM image_categories) as total_categories,
       (SELECT COUNT(*) FROM image_tags) as total_tags,
       (SELECT SUM(file_size) FROM image_items) as total_file_size`,
    [userId]
  );
  
  // 获取分类统计
  const [categoryStats] = await db.execute(
    `SELECT ic.name, COUNT(ii.id) as image_count
     FROM image_categories ic
     LEFT JOIN image_items ii ON ic.id = ii.category_id
     GROUP BY ic.id
     ORDER BY image_count DESC
     LIMIT 10`
  );
  
  res.json(createSuccessResponse({
    stats: stats[0],
    categoryStats: categoryStats
  }));
}));

module.exports = router;