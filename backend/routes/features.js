/**
 * 应用功能特性管理路由
 * 
 * 功能说明：
 * - 应用功能特性管理
 * - 首页功能展示
 */

const express = require('express');
const router = express.Router();

const db = require('../config/database');
const { authenticateToken, requireAdmin } = require('../middleware/auth');
const { asyncHandler, ValidationError, NotFoundError, createSuccessResponse } = require('../middleware/errorHandler');

// ==================== 功能特性管理 ====================

/**
 * @route GET /api/features
 * @desc 获取应用功能特性列表
 * @access Public
 */
router.get('/', asyncHandler(async (req, res) => {
  const features = await db.execute(
    `SELECT id, icon, title, description, link, sort_order, is_active
     FROM app_features
     WHERE is_active = 1
     ORDER BY sort_order ASC, id ASC`
  );
  
  res.json(createSuccessResponse(features));
}));

/**
 * @route GET /api/features/:id
 * @desc 获取单个功能特性详情
 * @access Public
 */
router.get('/:id', asyncHandler(async (req, res) => {
  const { id } = req.params;
  
  const [feature] = await db.execute(
    `SELECT id, icon, title, description, link, sort_order, is_active, created_at, updated_at
     FROM app_features
     WHERE id = ?`,
    [id]
  );
  
  if (!feature) {
    throw new NotFoundError('功能特性不存在');
  }
  
  res.json(createSuccessResponse(feature));
}));

/**
 * @route POST /api/features
 * @desc 创建功能特性
 * @access Private (Admin only)
 */
router.post('/', authenticateToken, requireAdmin, asyncHandler(async (req, res) => {
  const { icon, title, description, link, sort_order = 0 } = req.body;
  
  if (!icon || !title || !description || !link) {
    throw new ValidationError('图标、标题、描述和链接不能为空');
  }
  
  const result = await db.execute(
    `INSERT INTO app_features (icon, title, description, link, sort_order)
     VALUES (?, ?, ?, ?, ?)`,
    [icon, title, description, link, sort_order]
  );
  
  const [newFeature] = await db.execute(
    `SELECT id, icon, title, description, link, sort_order, is_active, created_at, updated_at
     FROM app_features
     WHERE id = ?`,
    [result.insertId]
  );
  
  res.status(201).json(createSuccessResponse(newFeature, '功能特性创建成功'));
}));

/**
 * @route PUT /api/features/:id
 * @desc 更新功能特性
 * @access Private (Admin only)
 */
router.put('/:id', authenticateToken, requireAdmin, asyncHandler(async (req, res) => {
  const { id } = req.params;
  const { icon, title, description, link, sort_order, is_active } = req.body;
  
  // 检查功能特性是否存在
  const [existing] = await db.execute(
    'SELECT id FROM app_features WHERE id = ?',
    [id]
  );
  
  if (!existing) {
    throw new NotFoundError('功能特性不存在');
  }
  
  if (!icon || !title || !description || !link) {
    throw new ValidationError('图标、标题、描述和链接不能为空');
  }
  
  await db.execute(
    `UPDATE app_features 
     SET icon = ?, title = ?, description = ?, link = ?, sort_order = ?, is_active = ?
     WHERE id = ?`,
    [icon, title, description, link, sort_order || 0, is_active !== undefined ? is_active : 1, id]
  );
  
  const [updatedFeature] = await db.execute(
    `SELECT id, icon, title, description, link, sort_order, is_active, created_at, updated_at
     FROM app_features
     WHERE id = ?`,
    [id]
  );
  
  res.json(createSuccessResponse(updatedFeature, '功能特性更新成功'));
}));

/**
 * @route DELETE /api/features/:id
 * @desc 删除功能特性
 * @access Private (Admin only)
 */
router.delete('/:id', authenticateToken, requireAdmin, asyncHandler(async (req, res) => {
  const { id } = req.params;
  
  // 检查功能特性是否存在
  const [existing] = await db.execute(
    'SELECT id FROM app_features WHERE id = ?',
    [id]
  );
  
  if (!existing) {
    throw new NotFoundError('功能特性不存在');
  }
  
  await db.execute('DELETE FROM app_features WHERE id = ?', [id]);
  
  res.json(createSuccessResponse(null, '功能特性删除成功'));
}));

/**
 * @route PUT /api/features/:id/toggle
 * @desc 切换功能特性状态
 * @access Private (Admin only)
 */
router.put('/:id/toggle', authenticateToken, requireAdmin, asyncHandler(async (req, res) => {
  const { id } = req.params;
  
  // 检查功能特性是否存在
  const [existing] = await db.execute(
    'SELECT id, is_active FROM app_features WHERE id = ?',
    [id]
  );
  
  if (!existing) {
    throw new NotFoundError('功能特性不存在');
  }
  
  const newStatus = existing.is_active ? 0 : 1;
  
  await db.execute(
    'UPDATE app_features SET is_active = ? WHERE id = ?',
    [newStatus, id]
  );
  
  const [updatedFeature] = await db.execute(
    `SELECT id, icon, title, description, link, sort_order, is_active, created_at, updated_at
     FROM app_features
     WHERE id = ?`,
    [id]
  );
  
  res.json(createSuccessResponse(updatedFeature, `功能特性已${newStatus ? '启用' : '禁用'}`));
}));

module.exports = router;