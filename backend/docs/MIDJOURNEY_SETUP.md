# Midjourney集成配置指南

## 概述

本项目集成了Midjourney AI图片生成功能，通过Discord API与Midjourney Bot交互，实现高质量的AI艺术作品生成。

## 功能特性

- 🎨 **AI图片生成** - 使用Midjourney强大的AI模型生成高质量图片
- 🔄 **图片变换** - 支持图片放大(Upscale)和变体(Variation)功能
- ⚙️ **丰富参数** - 支持宽高比、质量、风格化程度等多种参数
- 📊 **历史记录** - 完整的生成历史记录和统计信息
- 🎯 **多种风格** - 支持各种艺术风格和创作模式
- 🚀 **异步处理** - 异步任务处理，不阻塞用户界面

## 配置步骤

### 1. 创建Discord应用

1. 访问 [Discord Developer Portal](https://discord.com/developers/applications)
2. 点击 "New Application" 创建新应用
3. 给应用起一个名字，例如 "SCAIA-Midjourney"
4. 在左侧菜单选择 "Bot"
5. 点击 "Add Bot" 创建机器人
6. 复制 "Token"，这就是你的 `DISCORD_TOKEN`

### 2. 配置Bot权限

在Bot设置页面：
1. 启用 "MESSAGE CONTENT INTENT"
2. 在 "OAuth2" > "URL Generator" 中：
   - 选择 "bot" scope
   - 选择以下权限：
     - Send Messages
     - Read Message History
     - View Channels
     - Use Slash Commands

### 3. 添加Bot到服务器

1. 使用上一步生成的URL邀请Bot到你的Discord服务器
2. 确保服务器中有Midjourney Bot
3. 创建一个专门的频道用于图片生成

### 4. 获取ID信息

#### 获取频道ID：
1. 在Discord中右键点击频道
2. 选择 "Copy Channel ID"
3. 这就是你的 `MIDJOURNEY_CHANNEL_ID`

#### 获取服务器ID：
1. 在Discord中右键点击服务器名称
2. 选择 "Copy Server ID"
3. 这就是你的 `MIDJOURNEY_SERVER_ID`

### 5. 配置环境变量

在项目根目录创建或编辑 `.env` 文件：

```bash
# Midjourney配置
DISCORD_TOKEN=your_discord_bot_token_here
MIDJOURNEY_CHANNEL_ID=your_channel_id_here
MIDJOURNEY_SERVER_ID=your_server_id_here
```

### 6. 在设置页面配置

1. 启动项目后访问设置页面
2. 在 "Midjourney配置" 部分输入：
   - Discord Bot Token
   - 频道ID
   - 服务器ID
3. 点击 "测试连接" 验证配置

## 使用方法

### 基本生成

1. 访问 Midjourney 页面
2. 在提示词输入框中描述你想要的图片
3. 选择生成参数（可选）
4. 点击 "生成图片"
5. 等待2-5分钟完成生成

### 提示词示例

```
a beautiful sunset over mountains, digital art style
a futuristic city with flying cars, cyberpunk style
a cute cat wearing a wizard hat, cartoon style
abstract geometric patterns, vibrant colors
```

### 参数说明

- **宽高比 (aspect_ratio)**: 控制图片的宽高比例
  - `1:1` - 正方形
  - `16:9` - 宽屏
  - `9:16` - 竖屏
  
- **质量等级 (quality)**: 控制生成质量和速度
  - `0.25` - 快速生成，较低质量
  - `1` - 标准质量（默认）
  - `2` - 高质量，较慢

- **风格化程度 (stylize)**: 控制AI的创意程度
  - `0-100` - 更接近提示词
  - `100-1000` - 更多AI创意

- **随机性 (chaos)**: 控制结果的随机性
  - `0` - 一致性结果
  - `100` - 最大随机性

## API接口

### 检查状态
```bash
GET /api/midjourney/status
```

### 生成图片
```bash
POST /api/midjourney/generate
Content-Type: application/json

{
  "prompt": "a beautiful landscape",
  "options": {
    "aspect_ratio": "16:9",
    "quality": "1",
    "stylize": 100
  }
}
```

### 获取历史
```bash
GET /api/midjourney/history?limit=20
```

### 下载图片
```bash
POST /api/midjourney/download
Content-Type: application/json

{
  "imageUrl": "https://cdn.discordapp.com/...",
  "filename": "my_image.png"
}
```

## 故障排除

### 常见问题

1. **"Midjourney未配置"**
   - 检查环境变量是否正确设置
   - 确认Discord Token有效

2. **"连接测试失败"**
   - 检查Bot是否在正确的服务器中
   - 确认频道ID和服务器ID正确

3. **"生成超时"**
   - Midjourney服务器可能繁忙
   - 检查Discord频道是否可访问

4. **"权限不足"**
   - 确认Bot有发送消息权限
   - 检查频道权限设置

### 调试模式

启用调试日志：
```bash
DEBUG=midjourney npm start
```

## 注意事项

1. **使用限制**: 遵守Midjourney的使用条款和限制
2. **API配额**: 注意Discord API的速率限制
3. **内容政策**: 确保生成内容符合平台政策
4. **成本考虑**: Midjourney可能需要付费订阅

## 更新日志

- v1.0.0 - 初始版本，支持基本图片生成
- 计划功能：
  - 图片放大和变体
  - 批量生成
  - 风格预设
  - 更多参数支持

## 技术支持

如有问题，请：
1. 检查配置是否正确
2. 查看服务器日志
3. 参考Discord API文档
4. 联系技术支持团队
