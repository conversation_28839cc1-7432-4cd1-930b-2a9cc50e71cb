/**
 * SCAIA 后端服务器启动文件
 * 
 * 功能说明：
 * - 服务器启动入口
 * - 环境配置检查
 * - 数据库连接测试
 * - 优雅关闭处理
 */

const app = require('./app');
const db = require('./config/database');
const net = require('net');
require('dotenv').config();

const START_PORT = parseInt(process.env.PORT || '3000', 10);

function findAvailablePort(startPort) {
  return new Promise((resolve, reject) => {
    if (startPort > 65535) {
      return reject(new Error('没有可用的端口'));
    }
    const server = net.createServer();
    server.unref();
    server.on('error', (err) => {
      if (err.code === 'EADDRINUSE') {
        console.log(`端口 ${startPort} 已被占用，正在尝试下一个端口...`);
        resolve(findAvailablePort(startPort + 1));
      } else {
        reject(err);
      }
    });
    server.listen(startPort, () => {
      server.close(() => {
        resolve(startPort);
      });
    });
  });
}

/**
 * 启动服务器
 */
async function startServer() {
  try {
    console.log('🚀 正在启动 SCAIA 后端服务器...');
    
    // 检查必要的环境变量
    const requiredEnvVars = [
      'DB_HOST',
      'DB_USER', 
      'DB_PASSWORD',
      'DB_NAME',
      'JWT_SECRET'
    ];
    
    const missingEnvVars = requiredEnvVars.filter(varName => !process.env[varName]);
    
    if (missingEnvVars.length > 0) {
      console.error('❌ 缺少必要的环境变量:', missingEnvVars.join(', '));
      console.error('请检查 .env 文件配置');
      process.exit(1);
    }
    
    // 测试数据库连接
    console.log('🔗 正在测试数据库连接...');
    await db.execute('SELECT 1 as test');
    console.log('✅ 数据库连接成功');
    
    const port = await findAvailablePort(START_PORT);

    // 启动 HTTP 服务器
    const server = app.listen(port,'0.0.0.0', () => {
      console.log('\n🎉 SCAIA 后端服务器启动成功!');
      console.log(`📍 服务地址: http://localhost:${port}`);
      console.log(`🔗 健康检查: http://localhost:${port}/api/health`);
      console.log(`🌍 运行环境: ${process.env.NODE_ENV || 'development'}`);
      console.log(`📊 数据库: ${process.env.DB_HOST}:${process.env.DB_PORT || 3306}/${process.env.DB_NAME}`);
      console.log('\n📚 可用的 API 端点:');
      console.log('  - /api/auth          用户认证');
      console.log('  - /api/users         用户管理');
      console.log('  - /api/chat          聊天功能');
      console.log('  - /api/knowledge     知识库');
      console.log('  - /api/masters       大师库');
      console.log('  - /api/images        图片库');
      console.log('  - /api/prompts       提示词库');
      console.log('  - /api/scripts       话术库');
      console.log('  - /api/settings      设置管理');
      console.log('\n🔧 服务器已就绪，等待请求...');
    });
    
    // 设置服务器超时
    server.timeout = 30000; // 30秒

    // 优雅关闭处理
    const gracefulShutdown = (signal) => {
      console.log(`
👋 收到信号 ${signal}，开始优雅关闭...`);
      server.close(() => {
        console.log('✅ HTTP 服务器已关闭');
        db.close().then(() => {
          process.exit(0);
        }).catch(err => {
          console.error('❌ 关闭数据库连接时出错:', err.message);
          process.exit(1);
        });
      });
    };

    // nodemon 重启信号
    process.once('SIGUSR2', () => {
      console.log(`
👋 收到信号 SIGUSR2，开始优雅关闭以进行重启...`);
      server.close(() => {
        console.log('✅ HTTP 服务器已关闭');
        db.close().catch(err => {
          console.error('❌ 关闭数据库连接时出错:', err.message);
        }).finally(() => {
          process.kill(process.pid, 'SIGUSR2');
        });
      });
    });

    process.on('SIGINT', () => gracefulShutdown('SIGINT'));
    process.on('SIGTERM', () => gracefulShutdown('SIGTERM'));
    

    
  } catch (error) {
    console.error('❌ 服务器启动失败:');
    console.error('错误信息:', error.message);
    
    if (error.code === 'ECONNREFUSED') {
      console.error('💡 可能的解决方案:');
      console.error('  1. 检查数据库服务是否正在运行');
      console.error('  2. 验证数据库连接配置');
      console.error('  3. 确认网络连接正常');
    } else if (error.code === 'ER_ACCESS_DENIED_ERROR') {
      console.error('💡 可能的解决方案:');
      console.error('  1. 检查数据库用户名和密码');
      console.error('  2. 确认数据库用户权限');
    } else if (error.code === 'ER_BAD_DB_ERROR') {
      console.error('💡 可能的解决方案:');
      console.error('  1. 检查数据库名称是否正确');
      console.error('  2. 确认数据库是否存在');
    }
    
    console.error('\n详细错误信息:', error);
    process.exit(1);
  }
}

// 处理未捕获的异常
process.on('uncaughtException', (err) => {
  console.error('❌ 未捕获的异常:', err);
  console.error('堆栈跟踪:', err.stack);
  process.exit(1);
});

// 处理未处理的 Promise 拒绝
process.on('unhandledRejection', (reason, promise) => {
  console.error('❌ 未处理的 Promise 拒绝:');
  console.error('原因:', reason);
  console.error('Promise:', promise);
  process.exit(1);
});

// 启动服务器
if (require.main === module) {
  startServer();
}

module.exports = { startServer };