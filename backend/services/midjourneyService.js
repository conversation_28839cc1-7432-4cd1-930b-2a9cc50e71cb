const { spawn } = require('child_process');
const path = require('path');
const fs = require('fs').promises;
const axios = require('axios');

class MidjourneyService {
  constructor() {
    this.workingDir = path.join(__dirname, '../midjourney_storage');
    this.uploadsDir = path.join(__dirname, '../uploads/midjourney');
    this.pythonScriptPath = path.join(__dirname, '../scripts/midjourney_client.py');
    this.demoScriptPath = path.join(__dirname, '../scripts/midjourney_client_demo.py');
    this.demoMode = false;
    this.demoScriptPath = path.join(__dirname, '../scripts/midjourney_client_demo.py');
    this.demoMode = false;
    
    // Midjourney配置
    this.config = {
      discordToken: process.env.DISCORD_TOKEN || '',
      channelId: process.env.MIDJOURNEY_CHANNEL_ID || '',
      serverId: process.env.MIDJOURNEY_SERVER_ID || '',
      botId: '936929561302675456', // Midjourney Bot ID
      timeout: 300000, // 5分钟超时
    };
    
    this.ensureDirectories();
  }

  async ensureDirectories() {
    try {
      await fs.mkdir(this.workingDir, { recursive: true });
      await fs.mkdir(this.uploadsDir, { recursive: true });
      console.log('📁 Midjourney目录创建成功');
    } catch (error) {
      console.error('❌ 创建Midjourney目录失败:', error);
    }
  }

  // 检查Midjourney配置状态
  async checkMidjourneyStatus() {
    try {
      const hasToken = !!this.config.discordToken;
      const hasChannelId = !!this.config.channelId;
      const hasServerId = !!this.config.serverId;
      
      const isConfigured = hasToken && hasChannelId && hasServerId;
      
      return {
        available: isConfigured,
        configured: isConfigured,
        config: {
          hasToken,
          hasChannelId,
          hasServerId,
          botId: this.config.botId
        },
        message: isConfigured ? 'Midjourney配置完整' : '需要配置Discord Token和频道信息'
      };
    } catch (error) {
      return {
        available: false,
        configured: false,
        error: error.message
      };
    }
  }

  // 检测网络连接并决定是否使用演示模式
  async checkNetworkAndSetMode() {
    try {
      const response = await axios.get('https://discord.com/api/v10', { timeout: 5000 });
      this.demoMode = false;
      console.log('🌐 Discord API连接正常，使用真实模式');
    } catch (error) {
      this.demoMode = true;
      console.log('🎭 Discord API无法连接，切换到演示模式');
    }
  }

  // 生成图片
  async generateImage(prompt, options = {}) {
    return new Promise(async (resolve, reject) => {
      console.log('🎨 开始生成Midjourney图片:', prompt);

      if (!this.config.discordToken || !this.config.channelId) {
        // 如果配置不完整，启用演示模式
        this.demoMode = true;
        console.log('⚠️ Midjourney配置不完整，使用演示模式');
      } else {
        // 检测网络连接
        await this.checkNetworkAndSetMode();
      }

      // 选择使用真实API还是演示模式
      const scriptPath = this.demoMode ? this.demoScriptPath : this.pythonScriptPath;
      const args = [
        scriptPath,
        'generate',
        prompt,
        this.config.discordToken,
        this.config.channelId,
        this.config.serverId,
        JSON.stringify(options)
      ];

      const pythonProcess = spawn('python3', args, {
        stdio: ['pipe', 'pipe', 'pipe']
      });

      let stdout = '';
      let stderr = '';

      pythonProcess.stdout.on('data', (data) => {
        stdout += data.toString();
        const message = data.toString().trim();
        if (this.demoMode) {
          console.log('🎭 Midjourney演示模式:', message);
        } else {
          console.log('🎨 Midjourney生成进度:', message);
        }
      });

      pythonProcess.stderr.on('data', (data) => {
        stderr += data.toString();
        console.error('⚠️ Midjourney警告:', data.toString().trim());
      });

      pythonProcess.on('close', (code) => {
        if (code === 0) {
          const lines = stdout.split('\n');
          const resultLine = lines.find(line => line.startsWith('MIDJOURNEY_RESULT:'));
          
          if (resultLine) {
            try {
              const result = JSON.parse(resultLine.replace('MIDJOURNEY_RESULT:', '').trim());
              console.log('✅ Midjourney图片生成成功');
              resolve({
                success: true,
                ...result,
                prompt: prompt,
                timestamp: new Date().toISOString(),
                mode: this.demoMode ? 'demo' : 'real'
              });
            } catch (parseError) {
              reject(new Error(`解析结果失败: ${parseError.message}`));
            }
          } else {
            reject(new Error('未找到生成结果'));
          }
        } else {
          const errorLine = stdout.split('\n').find(line => line.startsWith('MIDJOURNEY_ERROR:'));
          const errorMsg = errorLine ? errorLine.replace('MIDJOURNEY_ERROR:', '').trim() : stderr;
          reject(new Error(`Midjourney生成失败: ${errorMsg}`));
        }
      });

      // 设置超时
      setTimeout(() => {
        pythonProcess.kill();
        reject(new Error('Midjourney生成超时'));
      }, this.config.timeout);
    });
  }

  // 变换图片 (upscale, variation)
  async transformImage(messageId, action, index, options = {}) {
    return new Promise((resolve, reject) => {
      console.log(`🔄 执行Midjourney变换: ${action} ${index}`);
      
      const args = [
        this.pythonScriptPath,
        'transform',
        messageId,
        action,
        index.toString(),
        this.config.discordToken,
        this.config.channelId,
        JSON.stringify(options)
      ];

      const pythonProcess = spawn('python3', args, {
        stdio: ['pipe', 'pipe', 'pipe']
      });

      let stdout = '';
      let stderr = '';

      pythonProcess.stdout.on('data', (data) => {
        stdout += data.toString();
      });

      pythonProcess.stderr.on('data', (data) => {
        stderr += data.toString();
      });

      pythonProcess.on('close', (code) => {
        if (code === 0) {
          const lines = stdout.split('\n');
          const resultLine = lines.find(line => line.startsWith('MIDJOURNEY_RESULT:'));
          
          if (resultLine) {
            try {
              const result = JSON.parse(resultLine.replace('MIDJOURNEY_RESULT:', '').trim());
              resolve({
                success: true,
                ...result,
                action: action,
                index: index,
                timestamp: new Date().toISOString()
              });
            } catch (parseError) {
              reject(new Error(`解析结果失败: ${parseError.message}`));
            }
          } else {
            reject(new Error('未找到变换结果'));
          }
        } else {
          const errorLine = stdout.split('\n').find(line => line.startsWith('MIDJOURNEY_ERROR:'));
          const errorMsg = errorLine ? errorLine.replace('MIDJOURNEY_ERROR:', '').trim() : stderr;
          reject(new Error(`Midjourney变换失败: ${errorMsg}`));
        }
      });
    });
  }

  // 获取生成历史
  async getGenerationHistory(limit = 20) {
    try {
      const historyFile = path.join(this.workingDir, 'generation_history.json');
      
      try {
        const data = await fs.readFile(historyFile, 'utf8');
        const history = JSON.parse(data);
        return history.slice(-limit).reverse(); // 最新的在前面
      } catch (error) {
        return []; // 文件不存在或为空
      }
    } catch (error) {
      console.error('❌ 获取生成历史失败:', error);
      return [];
    }
  }

  // 保存生成记录
  async saveGenerationRecord(record) {
    try {
      const historyFile = path.join(this.workingDir, 'generation_history.json');
      
      let history = [];
      try {
        const data = await fs.readFile(historyFile, 'utf8');
        history = JSON.parse(data);
      } catch (error) {
        // 文件不存在，创建新的历史记录
      }
      
      history.push({
        id: Date.now().toString(),
        timestamp: new Date().toISOString(),
        ...record
      });
      
      // 只保留最近1000条记录
      if (history.length > 1000) {
        history = history.slice(-1000);
      }
      
      await fs.writeFile(historyFile, JSON.stringify(history, null, 2));
      console.log('💾 生成记录保存成功');
    } catch (error) {
      console.error('❌ 保存生成记录失败:', error);
    }
  }

  // 下载图片
  async downloadImage(imageUrl, filename) {
    try {
      const response = await axios({
        method: 'GET',
        url: imageUrl,
        responseType: 'stream'
      });

      const filePath = path.join(this.uploadsDir, filename);
      const writer = fs.createWriteStream(filePath);

      response.data.pipe(writer);

      return new Promise((resolve, reject) => {
        writer.on('finish', () => {
          console.log('📥 图片下载成功:', filename);
          resolve(filePath);
        });
        writer.on('error', reject);
      });
    } catch (error) {
      console.error('❌ 图片下载失败:', error);
      throw error;
    }
  }

  // 获取Midjourney统计信息
  async getStats() {
    try {
      const history = await this.getGenerationHistory(1000);
      
      const stats = {
        totalGenerations: history.length,
        todayGenerations: history.filter(record => {
          const today = new Date().toDateString();
          const recordDate = new Date(record.timestamp).toDateString();
          return today === recordDate;
        }).length,
        averageGenerationTime: 0,
        popularPrompts: {},
        recentActivity: history.slice(0, 10)
      };

      // 计算流行提示词
      history.forEach(record => {
        if (record.prompt) {
          const words = record.prompt.toLowerCase().split(' ');
          words.forEach(word => {
            if (word.length > 3) {
              stats.popularPrompts[word] = (stats.popularPrompts[word] || 0) + 1;
            }
          });
        }
      });

      return stats;
    } catch (error) {
      console.error('❌ 获取统计信息失败:', error);
      return {
        totalGenerations: 0,
        todayGenerations: 0,
        averageGenerationTime: 0,
        popularPrompts: {},
        recentActivity: []
      };
    }
  }
}

module.exports = new MidjourneyService();
