<template>
  <div class="home">
    <h1 class="page-title">欢迎使用 AIDE</h1>
    <p class="page-subtitle">AI-Integrated Design Environment</p>
    
    <div class="feature-grid">
      <div class="feature-card" v-for="(feature, index) in features" :key="index">
        <div class="feature-icon">{{ feature.icon }}</div>
        <h3 class="feature-title">{{ feature.title }}</h3>
        <p class="feature-description">{{ feature.description }}</p>
        <router-link :to="feature.link" class="btn btn-primary">开始使用</router-link>
      </div>
    </div>
    
    <div class="card mt-5">
      <h2 class="card-title">关于 AIDE</h2>
      <p>AIDE (AI-Integrated Design Environment) 是一个专为建筑、室内、景观及城市规划设计师打造的一站式 Web 应用。通过深度集成 Deepseek、豆包 (Doubao)、Midjourney 等AI服务，并结合结构化的专业知识库、大师库、图片库和话术库，构建一个集创意激发、知识查询、方案推演和素材管理于一体的综合性设计辅助平台。</p>
      
      <h3 class="mt-4">核心价值</h3>
      <ul class="value-list">
        <li><strong>集成化工作流</strong>: 在单一平台内完成从AI对话、知识检索到AI图像生成的完整设计辅助流程，大幅提升效率。</li>
        <li><strong>专业化AI</strong>: 通过可定制的"知识库"和"大师库"，将通用大模型调教为具备深度行业背景的专业顾问。</li>
        <li><strong>结构化创意</strong>: 独创的结构化"生图"和"图片库"系统，将感性的图像创意转化为可复用、可迭代的结构化数据，实现精准的二次创作。</li>
      </ul>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue';

const features = ref([
  {
    icon: '💬',
    title: '聊天',
    description: '智能对话中心，集成多模型和专业知识，支持多种大语言模型。',
    link: '/chat'
  },
  {
    icon: '🖼️',
    title: '生图',
    description: '基于 Midjourney 的专业级、结构化图像生成引擎，提供精准的创意表达。',
    link: '/image-generation'
  },
  {
    icon: '📚',
    title: '知识库',
    description: '用户的私人设计知识大脑，上传并管理设计规范、项目资料、技术文档等。',
    link: '/knowledge-base'
  },
  {
    icon: '👨‍🎨',
    title: '大师库',
    description: '结构化的设计思想与灵感来源，添加设计大师及其核心设计观点。',
    link: '/master-library'
  },
  {
    icon: '🏞️',
    title: '图片库',
    description: '可复用的个人灵感数据库，结构化存储和管理设计灵感图片。',
    link: '/image-library'
  },
  {
    icon: '📝',
    title: '话术库',
    description: '高效管理和调用常用文本，创建分类并添加、编辑和管理常用的文本片段。',
    link: '/script-library'
  }
]);
</script>

<style scoped>
.home {
  max-width: 1200px;
  margin: 0 auto;
}

.page-title {
  font-size: var(--font-size-xxl);
  color: var(--primary-color);
  margin-bottom: var(--spacing-sm);
}

.page-subtitle {
  font-size: var(--font-size-lg);
  color: var(--secondary-color);
  margin-bottom: var(--spacing-xl);
  opacity: 0.8;
}

.feature-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: var(--spacing-lg);
  margin-bottom: var(--spacing-xl);
}

.feature-card {
  background-color: white;
  border-radius: var(--border-radius);
  box-shadow: var(--shadow);
  padding: var(--spacing-lg);
  transition: transform var(--transition-speed), box-shadow var(--transition-speed);
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
}

.feature-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.feature-icon {
  font-size: 2.5rem;
  margin-bottom: var(--spacing-md);
}

.feature-title {
  font-size: var(--font-size-lg);
  color: var(--secondary-color);
  margin-bottom: var(--spacing-sm);
}

.feature-description {
  color: #666;
  margin-bottom: var(--spacing-lg);
  flex-grow: 1;
}

.value-list {
  padding-left: var(--spacing-lg);
  line-height: 1.6;
}

.value-list li {
  margin-bottom: var(--spacing-sm);
}

@media (max-width: 768px) {
  .feature-grid {
    grid-template-columns: 1fr;
  }
}
</style>