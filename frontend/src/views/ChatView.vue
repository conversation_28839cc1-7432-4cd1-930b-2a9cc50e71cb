<template>
  <div class="chat-view">
    <div class="chat-header">
      <h1 class="page-title">聊天</h1>
      <div class="model-selector">
        <label for="model">选择模型：</label>
        <select v-model="selectedModel" class="model-select">
              <option v-for="model in availableModels" :key="model.id" :value="model.id">
                {{ model.name }}
              </option>
            </select>
      </div>
      <div class="chat-controls">
        <button class="btn btn-secondary" @click="createNewChat">
          <span class="icon">➕</span> 新建会话
        </button>
        <label class="toggle-container">
          <input type="checkbox" v-model="allowWebSearch">
          <span class="toggle-label">允许联网搜索</span>
        </label>
      </div>
    </div>
    
    <div class="chat-container">
      <div class="chat-sidebar">
        <div class="chat-history">
          <h3>历史会话</h3>
          <ul class="chat-list">
            <li 
              v-for="chat in paginatedChatHistory" 
              :key="chat.id" 
              :class="{'active': currentChat && currentChat.id === chat.id}"
              @click="selectChat(chat)"
            >
              <span class="chat-title">{{ chat.title }}</span>
              <div class="chat-actions">
                <button class="action-btn" @click.stop="renameChat(chat)" title="重命名">✏️</button>
                <button class="action-btn" @click.stop="deleteChat(chat)" title="删除">🗑️</button>
              </div>
            </li>
          </ul>
          <div v-if="chatHistory.length > 10" class="history-controls">
            <button @click="showAllHistory = !showAllHistory" class="btn btn-link">
              {{ showAllHistory ? '收起' : '查看更多' }}
            </button>
          </div>
        </div>
      </div>
      
      <div v-if="currentChat" class="chat-main">
        <div class="messages-container" ref="messagesContainer">
          <div v-if="currentChat.messages.length === 0" class="empty-state">
            <div class="empty-icon">💬</div>
            <h3>开始一个新的对话</h3>
            <p>选择一个AI模型并开始提问，或者使用下方的特殊功能增强对话。</p>
          </div>
          
          <div v-else class="message-list">
            <div 
              v-for="(message, index) in currentChat.messages" 
              :key="index"
              :class="['message', message.role === 'user' ? 'user-message' : 'ai-message', message.isDebug ? 'debug-message' : '', message.isThinking ? 'thinking' : '']"
            >
              <div class="message-avatar">
                {{ message.role === 'user' ? '👤' : (message.isDebug ? '🔧' : '🤖') }}
              </div>
              <div class="message-content" :class="{ 'user-right': message.role === 'user' }">
                <!-- 显示思考过程（仅对assistant消息且有reasoning内容） -->
                <div v-if="message.role === 'assistant' && message.reasoning" class="reasoning-section">
                  <div class="reasoning-header">
                    <span class="reasoning-icon">🤔</span>
                    <span class="reasoning-title">思考过程</span>
                    <button class="reasoning-toggle" @click="toggleReasoning(index)">
                      {{ showReasoning[index] ? '收起' : '展开' }}
                    </button>
                  </div>
                  <div v-show="showReasoning[index]" class="reasoning-content">
                    {{ message.reasoning }}
                  </div>
                </div>

                <!-- 显示主要回复内容 -->
                <div class="message-text"
                  v-if="message.isThinking"
                  v-html="typingStates[index] || '<div class=&quot;loading-dots&quot;>...</div>'"
                ></div>
                <div class="message-text"
                  v-else-if="message.role === 'assistant'"
                  v-html="typingStates[index] || renderMarkdown(message.content)"
                ></div>
                <div class="message-text"
                  v-else
                  v-html="message.content"
                ></div>
                <div class="message-time">{{ formatTime(message.timestamp || message.created_at) }}</div>
                <div class="message-actions bottom-actions" v-if="message.role === 'assistant' && !message.isDebug && !message.isThinking && !message.isError">
                  <button class="action-btn-icon" @click="copyMessage(message, index)" title="复制">📋</button>
                  <button class="action-btn-icon" @click="retryMessage(message, index)" title="重答">🔄</button>
                  <button class="action-btn-icon" @click="exportMessage(message, index)" title="导出">📤</button>
                  <button
                    class="action-btn-icon"
                    @click="generateOutlineForMessage(message, index)"
                    :disabled="isGeneratingOutline"
                    title="生成大纲"
                  >
                    <span v-if="isGeneratingOutline" class="loading-spinner">⏳</span>
                    <span v-else>📊</span>
                  </button>
                  <button
                    class="action-btn-icon"
                    @click="generatePPTForMessage(message, index)"
                    :disabled="isGeneratingDirectPPT"
                    title="导出PPT"
                  >
                    <span v-if="isGeneratingDirectPPT" class="loading-spinner">⏳</span>
                    <span v-else>🎯</span>
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
        
        <div class="input-container">
          <div class="input-tools">
            <button class="tool-btn" title="上传文件" @click="showFileUpload = true">
              📎
            </button>
            <button class="tool-btn" title="引用知识库" @click="showKnowledgeBase = true">
              📚
            </button>
            <button class="tool-btn" title="引用大师观点" @click="showMasterLibrary = true">
              👨‍🎨
            </button>
            <button class="tool-btn" title="使用话述库" @click="showScriptLibrary = true">
              📝
            </button>
            <button class="tool-btn" title="清空上下文" @click="clearContext">
              🧹
            </button>
          </div>
          
          <!-- RAG文件上传区域 -->
          <div v-if="showRAGUpload" class="rag-upload-area">
            <div class="upload-header">
              <h4>📁 上传文档到知识库</h4>
              <button @click="showRAGUpload = false" class="close-btn">×</button>
            </div>
            <div class="upload-zone" @drop="handleFileDrop" @dragover.prevent @dragenter.prevent>
              <input
                type="file"
                ref="fileInput"
                @change="handleFileSelect"
                multiple
                accept=".pdf,.doc,.docx,.txt,.jpg,.png,.gif,.xls,.xlsx"
                style="display: none"
              >
              <div class="upload-content" @click="$refs.fileInput.click()">
                <i class="fas fa-cloud-upload-alt"></i>
                <p>点击或拖拽文件到此处上传</p>
                <small>支持 PDF、Word、图片、Excel 等格式</small>
              </div>
            </div>
            <div v-if="uploadedFiles.length > 0" class="uploaded-files">
              <h5>已上传文件：</h5>
              <div v-for="file in uploadedFiles" :key="file.filename" class="file-item">
                <i class="fas fa-file"></i>
                <span>{{ file.originalName }}</span>
                <span class="file-size">({{ formatFileSize(file.size) }})</span>
              </div>
            </div>
          </div>

          <div class="input-field">
            <div class="input-actions-left">
              <button @click="toggleRAGUpload" class="rag-button" title="RAG知识库">
                <i class="fas fa-database"></i>
              </button>
            </div>
            <textarea
              v-model="userInput"
              class="chat-input"
              placeholder="输入您的问题或指令..."
              @keydown.enter.ctrl="sendMessage"
              ref="inputField"
            ></textarea>
            <button
              class="send-btn"
              :class="{ 'streaming': isStreaming }"
              @click="isStreaming ? stopStream() : sendMessage()"
              :disabled="userInput.trim() === '' && !isStreaming"
            >
              {{ isStreaming ? '停止' : '发送' }}
            </button>
          </div>
          
          <div class="input-hint">
            按 Ctrl + Enter 发送消息
          </div>
        </div>
      </div>
      <div v-else class="chat-main chat-main-loading">
        <div class="loading-spinner"></div>
        <span>正在加载会话...</span>
      </div>
    </div>
    
    <!-- 模态框组件 -->
    <div v-if="showFileUpload" class="modal">
      <div class="modal-content">
        <div class="modal-header">
          <h3>上传文件</h3>
          <button class="close-btn" @click="showFileUpload = false">×</button>
        </div>
        <div class="modal-body">
          <p>选择要上传的文件（支持 .pdf, .txt 等格式）</p>
          <input type="file" class="file-input" accept=".pdf,.txt,.doc,.docx">
        </div>
        <div class="modal-footer">
          <button class="btn btn-secondary" @click="showFileUpload = false">取消</button>
          <button class="btn btn-primary" @click="uploadFile">上传</button>
        </div>
      </div>
    </div>
    
    <div v-if="showKnowledgeBase" class="modal">
      <div class="modal-content">
        <div class="modal-header">
          <h3>知识库</h3>
          <button class="close-btn" @click="showKnowledgeBase = false">×</button>
        </div>
        <div class="modal-body">
          <p>选择要引用的知识库文件</p>
          <ul class="selection-list knowledge-list">
            <li 
              v-for="(item, index) in knowledgeBaseItems" 
              :key="index" 
              @click="selectKnowledgeItem(item)"
              :class="{'active': selectedKnowledgeItem && selectedKnowledgeItem.id === item.id}"
            >
              {{ item.title }}
            </li>
          </ul>
        </div>
        <div class="modal-footer">
          <button class="btn btn-secondary" @click="showKnowledgeBase = false">取消</button>
          <button class="btn btn-primary" @click="insertKnowledgeReference">插入引用</button>
        </div>
      </div>
    </div>
    
    <div v-if="showMasterLibrary" class="modal">
      <div class="modal-content">
        <div class="modal-header">
          <h3>大师库</h3>
          <button class="close-btn" @click="showMasterLibrary = false">×</button>
        </div>
        <div class="modal-body">
          <p>选择要引用的建筑、室内、景观、规划领域的大师观点</p>
          <ul class="selection-list master-list">
            <li 
              v-for="(item, index) in masterLibraryItems" 
              :key="index" 
              @click="selectMasterItem(item)"
              :class="{'active': selectedMasterItem && selectedMasterItem.id === item.id}"
            >
              {{ item.name }} - {{ item.concept }}
            </li>
          </ul>
        </div>
        <div class="modal-footer">
          <button class="btn btn-secondary" @click="showMasterLibrary = false">取消</button>
          <button class="btn btn-primary" @click="insertMasterReference">插入引用</button>
        </div>
      </div>
    </div>
    
    <div v-if="showScriptLibrary" class="modal">
      <div class="modal-content">
        <div class="modal-header">
          <h3>话述库</h3>
          <button class="close-btn" @click="showScriptLibrary = false">×</button>
        </div>
        <div class="modal-body">
          <p>选择要使用的话述</p>
          <div class="script-categories">
            <button 
              v-for="(category, index) in scriptCategories" 
              :key="index"
              :class="['category-btn', selectedScriptCategory === category ? 'active' : '']"
              @click="selectedScriptCategory = category"
            >
              {{ category }}
            </button>
          </div>
          <ul class="selection-list script-list">
            <li 
              v-for="(item, index) in filteredScriptItems" 
              :key="index" 
              @click="selectScriptItem(item)"
              :class="{'active': selectedScriptItem && selectedScriptItem.id === item.id}"
            >
              {{ item.title }}
            </li>
          </ul>
        </div>
        <div class="modal-footer">
          <button class="btn btn-secondary" @click="showScriptLibrary = false">取消</button>
          <button class="btn btn-primary" @click="insertScript">插入话述</button>
        </div>
      </div>
    </div>

    <!-- 大纲预览模态框 -->
    <div v-if="showOutlineModal" class="modal">
      <div class="modal-content outline-modal">
        <div class="modal-header">
          <h3>📊 PPT大纲预览</h3>
          <button class="close-btn" @click="showOutlineModal = false">×</button>
        </div>
        <div class="modal-body">
          <div class="outline-editor">
            <div class="outline-title">
              <label>演示文稿标题:</label>
              <input
                v-model="currentOutline.title"
                type="text"
                class="title-input"
                placeholder="请输入标题"
              />
            </div>

            <div class="slides-container">
              <div
                v-for="(slide, slideIndex) in currentOutline.slides"
                :key="slideIndex"
                class="slide-editor"
              >
                <div class="slide-header">
                  <span class="slide-number">幻灯片 {{ slideIndex + 1 }}</span>
                  <button
                    class="delete-slide-btn"
                    @click="deleteSlide(slideIndex)"
                    title="删除幻灯片"
                  >
                    🗑️
                  </button>
                </div>

                <div class="slide-title">
                  <label>标题:</label>
                  <input
                    v-model="slide.title"
                    type="text"
                    class="slide-title-input"
                    placeholder="幻灯片标题"
                  />
                </div>

              <div class="slide-keywords">
                <label>标题关键词:</label>
                <input
                  v-model="slide.keywordsText"
                  type="text"
                  class="keywords-input"
                  placeholder="默认建议关键词，您可以完全自定义（用逗号分隔）"
                  @input="updateSlideKeywords(slideIndex)"
                />
              </div>

                <div class="slide-points">
                  <label>要点:</label>
                  <div
                    v-for="(point, pointIndex) in slide.points"
                    :key="pointIndex"
                    class="point-item-container"
                  >
                    <div class="point-item">
                      <input
                        v-model="point.text"
                        type="text"
                        class="point-input"
                        placeholder="要点内容"
                      />
                      <button
                        class="delete-point-btn"
                        @click="deletePoint(slideIndex, pointIndex)"
                        title="删除要点"
                      >
                        ×
                      </button>
                    </div>
                    <div class="point-keywords">
                      <label class="keywords-label">关键词:</label>
                      <input
                        v-model="point.keywordsText"
                        type="text"
                        class="keywords-input small"
                        placeholder="默认建议关键词，您可以完全自定义（用逗号分隔）"
                        @input="updatePointKeywords(slideIndex, pointIndex)"
                      />
                    </div>
                  </div>
                  <button
                    class="add-point-btn"
                    @click="addPoint(slideIndex)"
                  >
                    + 添加要点
                  </button>
                </div>
              </div>
            </div>

            <button class="add-slide-btn" @click="addSlide">
              + 添加幻灯片
            </button>
          </div>
        </div>
        <div class="modal-footer">
          <button class="btn btn-secondary" @click="showOutlineModal = false">取消</button>
          <button class="btn btn-primary" @click="exportToPPT" :disabled="isGeneratingPPT">
            {{ isGeneratingPPT ? '生成中...' : '🎯 导出PPT' }}
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, nextTick } from 'vue';
import {
  modelsAPI,
  sendChatMessageStream,
  createChatSession,
  getChatSessions,
  getChatSessionMessages,
  renameChatSession,
  deleteChatSession,
  generateOutline,
  generatePPT
} from '../services/api.js';
import { marked } from 'marked';
import hljs from 'highlight.js';

// 导入常用语言支持
import 'highlight.js/styles/github-dark.css';
import javascript from 'highlight.js/lib/languages/javascript';
import typescript from 'highlight.js/lib/languages/typescript';
import python from 'highlight.js/lib/languages/python';
import java from 'highlight.js/lib/languages/java';
import cpp from 'highlight.js/lib/languages/cpp';
import csharp from 'highlight.js/lib/languages/csharp';
import php from 'highlight.js/lib/languages/php';
import ruby from 'highlight.js/lib/languages/ruby';
import go from 'highlight.js/lib/languages/go';
import rust from 'highlight.js/lib/languages/rust';
import swift from 'highlight.js/lib/languages/swift';
import kotlin from 'highlight.js/lib/languages/kotlin';
import scala from 'highlight.js/lib/languages/scala';
import html from 'highlight.js/lib/languages/xml';
import css from 'highlight.js/lib/languages/css';
import scss from 'highlight.js/lib/languages/scss';
import less from 'highlight.js/lib/languages/less';
import sql from 'highlight.js/lib/languages/sql';
import json from 'highlight.js/lib/languages/json';
import yaml from 'highlight.js/lib/languages/yaml';
import xml from 'highlight.js/lib/languages/xml';
import bash from 'highlight.js/lib/languages/bash';
import shell from 'highlight.js/lib/languages/shell';
import dockerfile from 'highlight.js/lib/languages/dockerfile';
import markdown from 'highlight.js/lib/languages/markdown';
import diff from 'highlight.js/lib/languages/diff';
import ini from 'highlight.js/lib/languages/ini';

// 注册语言
hljs.registerLanguage('javascript', javascript);
hljs.registerLanguage('typescript', typescript);
hljs.registerLanguage('python', python);
hljs.registerLanguage('java', java);
hljs.registerLanguage('cpp', cpp);
hljs.registerLanguage('csharp', csharp);
hljs.registerLanguage('php', php);
hljs.registerLanguage('ruby', ruby);
hljs.registerLanguage('go', go);
hljs.registerLanguage('rust', rust);
hljs.registerLanguage('swift', swift);
hljs.registerLanguage('kotlin', kotlin);
hljs.registerLanguage('scala', scala);
hljs.registerLanguage('html', html);
hljs.registerLanguage('css', css);
hljs.registerLanguage('scss', scss);
hljs.registerLanguage('less', less);
hljs.registerLanguage('sql', sql);
hljs.registerLanguage('json', json);
hljs.registerLanguage('yaml', yaml);
hljs.registerLanguage('xml', xml);
hljs.registerLanguage('bash', bash);
hljs.registerLanguage('shell', shell);
hljs.registerLanguage('dockerfile', dockerfile);
hljs.registerLanguage('markdown', markdown);
hljs.registerLanguage('diff', diff);
hljs.registerLanguage('ini', ini);

// 配置 highlight.js
hljs.configure({
  ignoreUnescapedHTML: true,
  languages: [
    'javascript', 'typescript', 'python', 'java', 'cpp', 'csharp', 'php', 'ruby',
    'go', 'rust', 'swift', 'kotlin', 'scala', 'html', 'css', 'scss', 'less',
    'sql', 'json', 'yaml', 'xml', 'bash', 'shell', 'dockerfile', 'markdown',
    'diff', 'ini'
  ]
});

// 可用的AI模型（从设置中的聊天类型模型获取）
const availableModels = ref([]);

// 选中的模型（默认使用设置中的默认聊天模型）
const selectedModel = ref('');
const allowWebSearch = ref(false);

// 聊天历史
const chatHistory = ref([]);
const showAllHistory = ref(false);
const paginatedChatHistory = computed(() => {
  if (showAllHistory.value) {
    return chatHistory.value;
  }
  return chatHistory.value.slice(0, 10);
});


const currentChat = ref(null);
const userInput = ref('');
const messagesContainer = ref(null);
const inputField = ref(null);

// 选中的消息（用于上下文控制）
const selectedMessages = ref([]);

// 模态框状态
const showFileUpload = ref(false);
const showKnowledgeBase = ref(false);
const showMasterLibrary = ref(false);
const showScriptLibrary = ref(false);
const showOutlineModal = ref(false);

// RAG相关状态
const showRAGUpload = ref(false);
const uploadedFiles = ref([]);
const isUploadingFiles = ref(false);

// 大纲相关状态
const currentOutline = ref({
  title: '',
  slides: []
});
const isGeneratingPPT = ref(false);
const isGeneratingOutline = ref(false);
const isGeneratingDirectPPT = ref(false);

// 示例数据
const knowledgeBaseItems = ref([
  { id: 1, title: '建筑设计规范2023版' },
  { id: 2, title: '室内设计手册' },
  { id: 3, title: '景观设计指南' },
  { id: 4, title: '城市规划基础理论' }
]);

const masterLibraryItems = ref([
  { id: 1, name: '贝聿铭', concept: '简洁的几何形体与光影的精妙运用' },
  { id: 2, name: '扎哈·哈迪德', concept: '流动的空间与参数化设计' },
  { id: 3, name: '安藤忠雄', concept: '混凝土的诗意与光影的对话' },
  { id: 4, name: '隈研吾', concept: '材料的诗意表达与地域性设计' },
  { id: 5, name: '凯利·韦斯特勒', concept: '现代设计与古典元素的融合' },
  { id: 6, name: '劳伦斯·哈普林', concept: '工业遗址改造与可持续景观设计' },
  { id: 7, name: '简·雅各布斯', concept: '城市街区活力与混合使用原则' },
  { id: 8, name: '克里斯托弗·亚历山大', concept: '模式语言与有机秩序的建筑理论' }
]);

// 从scriptStore导入脚本数据
import { scripts as scriptLibraryData, getCategoryName } from '../store/scriptStore';

// 使用话术库的数据
const scriptItems = computed(() => {
  return scriptLibraryData.value.map(script => ({
    id: script.id,
    category: getCategoryName(script.categoryId),
    title: script.name,
    content: script.content
  }));
});

// 加载可用的聊天模型
async function loadChatModels() {
  try {
    const response = await modelsAPI.getAllModels();
    if (response.success) {
      // 只获取聊天类型的模型
      const chatModels = response.data.filter(model => model.type === 'chat');
      availableModels.value = chatModels;
      
      // 如果没有选中的模型，选择第一个可用模型
      if (!selectedModel.value && chatModels.length > 0) {
        selectedModel.value = chatModels[0].id;
      }
    } else {
      console.error('加载聊天模型失败:', response.message);
    }
  } catch (error) {
    console.error('加载聊天模型时出错:', error);
    // 如果加载失败，使用默认模型
    availableModels.value = [
      { id: 'deepseek', name: 'Deepseek', type: 'chat' },
      { id: 'doubao', name: '豆包 (Doubao)', type: 'chat' }
    ];
    selectedModel.value = 'deepseek';
  }
}

const scriptCategories = computed(() => {
  const categories = new Set();
  scriptItems.value.forEach(item => categories.add(item.category));
  return Array.from(categories);
});

const selectedScriptCategory = ref(scriptCategories.value[0]);

const filteredScriptItems = computed(() => {
  return scriptItems.value.filter(item => item.category === selectedScriptCategory.value);
});

// 选中的引用项
const selectedKnowledgeItem = ref(null);
const selectedMasterItem = ref(null);
const selectedScriptItem = ref(null);

const typingStates = ref({}); // 记录每条AI消息的打字机内容，key为消息索引
const showReasoning = ref({}); // 记录每条消息的思考过程是否展开，key为消息索引

// 流式聊天状态管理
const isStreaming = ref(false);
const currentStreamController = ref(null);

let timeoutTimer = null;

// 方法
const createNewChat = async () => {
  try {
    const response = await createChatSession({ 
      title: '新的对话', 
      modelId: selectedModel.value 
    });
    const newChatSession = response.data;
    // 后端返回的会话对象可能不包含 messages 数组，在客户端手动添加
    newChatSession.messages = [];
    chatHistory.value.unshift(newChatSession);
    currentChat.value = newChatSession;
  } catch (error) {
    console.error('创建新会话失败:', error);
    // 这里可以添加用户友好的错误提示
  }
};

async function selectChat(chat) {
  if (!chat) return;
  currentChat.value = chat;
  if (chat && !chat.messages) {
    chat.messages = [];
  }
  
  // 如果消息为空，则从服务器获取
  if (chat.messages.length === 0 && chat.id) {
    try {
      const response = await getChatSessionMessages(chat.id);
      if (response.success) {
        chat.messages = response.data;
      } else {
        console.error('获取聊天消息失败:', response.message);
      }
    } catch (error) {
      console.error('获取聊天消息时出错:', error);
    }
  }
  selectedMessages.value = [];
}

const renameChat = async (chat) => {
  const newTitle = prompt('请输入新的会话标题：', chat.title);
  if (newTitle && newTitle.trim() !== '' && newTitle !== chat.title) {
    try {
      const response = await renameChatSession(chat.id, newTitle.trim());
      if (response.success) {
        chat.title = newTitle.trim();
        // 更新 chatHistory 中的对应项
        const index = chatHistory.value.findIndex(c => c.id === chat.id);
        if (index !== -1) {
          chatHistory.value[index].title = newTitle.trim();
        }
      } else {
        alert('重命名失败: ' + response.message);
      }
    } catch (error) {
      console.error('重命名会话失败:', error);
      alert('重命名会话失败');
    }
  }
};

const deleteChat = async (chat) => {
  if (confirm(`确定要删除会话 "${chat.title}" 吗？此操作不可恢复。`)) {
    try {
      const response = await deleteChatSession(chat.id);
      if (response.success) {
        const index = chatHistory.value.findIndex(c => c.id === chat.id);
        if (index !== -1) {
          chatHistory.value.splice(index, 1);
        }
        // 如果删除的是当前会话，则选择一个新会话
        if (currentChat.value && currentChat.value.id === chat.id) {
          if (chatHistory.value.length > 0) {
            selectChat(chatHistory.value[0]);
          } else {
            createNewChat();
          }
        }
      } else {
        alert('删除失败: ' + response.message);
      }
    } catch (error) {
      console.error('删除会话失败:', error);
      alert('删除会话失败');
    }
  }
};


// 确保当前会话在后端是存在的
async function ensureValidSession() {
  if (currentChat.value && currentChat.value.id) {
    return currentChat.value;
  }

  // 如果没有当前会话，创建一个新的
  await createNewChat();
  return currentChat.value;
}

function getDisplayContent(message, index) {
  if (message.role === 'assistant' && typingStates.value[index] !== undefined) {
    return formatMessage(typingStates.value[index]);
  }
  return formatMessage(message.content);
}

async function sendMessage() {
  if (userInput.value.trim() === '') return;
  if (!selectedModel.value) {
    alert('请先选择一个AI模型');
    return;
  }

  // 如果正在流式传输，不允许发送新消息
  if (isStreaming.value) {
    return;
  }

  const input = userInput.value;

  // 检测PPT生成指令
  if (detectPPTCommand(input)) {
    userInput.value = '';
    handlePPTCommand();
    return;
  }

  userInput.value = '';
  
  const session = await ensureValidSession();
  if (!session) {
    userInput.value = input;
    return;
  }
  
  const userMessage = {
    role: 'user',
    content: input,
    timestamp: new Date(),
  };
  session.messages.push(userMessage);
  scrollToBottom();
  
  // 添加一个临时的AI消息用于流式显示
  const aiMessage = {
    role: 'assistant',
    content: '',
    reasoning: '', // 添加推理过程字段
    timestamp: new Date(),
    isThinking: true,
  };
  session.messages.push(aiMessage);
  scrollToBottom();

  let fullContent = '';
  let fullReasoning = '';
  let isDone = false;
  const aiIndex = session.messages.length - 1; // AI消息的索引
  typingStates.value[aiIndex] = '';
  
  // 设置流式状态
  isStreaming.value = true;
  
  function updateAIContent(delta) {
    if (!isStreaming.value) return; // 如果已停止，不再更新内容
    fullContent += delta;
    aiMessage.content = fullContent;
    typingStates.value[aiIndex] = renderMarkdown(fullContent);
    // 强制更新消息内容以触发高亮渲染
    session.messages = [...session.messages];
    nextTick(() => {
      scrollToBottom();
      // 重新应用代码高亮
      applyCodeHighlighting();
    });
  }

  function updateAIReasoning(delta) {
    if (!isStreaming.value) return; // 如果已停止，不再更新内容
    fullReasoning += delta;
    aiMessage.reasoning = fullReasoning;
    // 强制更新消息内容
    session.messages = [...session.messages];
    nextTick(() => {
      scrollToBottom();
    });
  }
  
  async function finishAI() {
    aiMessage.isThinking = false;
    aiMessage.timestamp = new Date();
    isDone = true;
    isStreaming.value = false; // 重置流式状态
    currentStreamController.value = null;
    typingStates.value[aiIndex] = renderMarkdown(fullContent);
    session.messages = [...session.messages];

    // 如果是新对话，自动生成标题
    if (session.title === '新的对话' && input.trim().length > 0) {
      const newTitle = input.trim().substring(0, 20);
      try {
        const response = await renameChatSession(session.id, newTitle);
        if (response.success) {
          const chatInHistory = chatHistory.value.find(c => c.id === session.id);
          if (chatInHistory) {
            chatInHistory.title = newTitle;
          }
          if (currentChat.value && currentChat.value.id === session.id) {
            currentChat.value.title = newTitle;
          }
        }
      } catch (error) {
        console.error("自动重命名会话失败:", error);
      }
    }
    
    nextTick(() => {
      scrollToBottom();
      // 重新应用代码高亮
      applyCodeHighlighting();
    });
  }
  
  function errorAI(errMsg) {
    aiMessage.content = '出错了: ' + (errMsg.includes('超时') ? 'AI服务繁忙，请稍后重试' : errMsg);
    aiMessage.isThinking = false;
    aiMessage.isError = true;
    isStreaming.value = false; // 重置流式状态
    currentStreamController.value = null;
    typingStates.value[aiIndex] = aiMessage.content;
    session.messages = [...session.messages];
    isDone = true;
    nextTick(() => scrollToBottom());
  }
  
  const messageData = { 
    modelId: selectedModel.value, 
    prompt: input, 
    message: session.messages.slice(0, -2).map(m => ({ role: m.role, content: m.content })),
    sessionId: session.id 
  };
  
  try {
    const closeStream = sendChatMessageStream(
      messageData,
      updateAIContent,
      (newSessionId) => {
        if (!session.id) {
            session.id = newSessionId;
        }
      },
      finishAI,
      errorAI,
      updateAIReasoning
    );
    
    // 保存流控制器以便停止
    currentStreamController.value = closeStream;
    
    // 设置超时处理
    timeoutTimer = setTimeout(() => {
      if (!isDone && isStreaming.value) {
        errorAI('AI回复超时，请重试');
        if (closeStream) closeStream();
      }
    }, 60000);
    
  } catch (error) {
    errorAI('调用AI失败: ' + error.message);
  }
}

// 停止流式传输
function stopStream() {
  if (isStreaming.value && currentStreamController.value) {
    currentStreamController.value();
    isStreaming.value = false;
    currentStreamController.value = null;
    isDone = true;
    if (timeoutTimer) {
      clearTimeout(timeoutTimer);
      timeoutTimer = null;
    }
    // 找到当前正在思考的AI消息并标记为已停止
    if (currentChat.value && currentChat.value.messages) {
      const thinkingMessage = currentChat.value.messages.find(msg => msg.isThinking);
      if (thinkingMessage) {
        thinkingMessage.isThinking = false;
        thinkingMessage.content += '\n\n[用户停止了AI回复]';
        thinkingMessage.timestamp = new Date();
      }
    }
  }
}

function scrollToBottom() {
  nextTick(() => {
    if (messagesContainer.value) {
      messagesContainer.value.scrollTop = messagesContainer.value.scrollHeight;
    }
  });
}

function formatMessage(content) {
  // 简单的文本格式化，实际应用中可能需要更复杂的处理
  return content.replace(/\n/g, '<br>');
}

function formatTime(timestamp) {
  if (!timestamp) return '';
  return new Intl.DateTimeFormat('zh-CN', {
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit'
  }).format(new Date(timestamp));
}

function toggleMessageSelection(message) {
  const index = selectedMessages.value.indexOf(message);
  if (index === -1) {
    selectedMessages.value.push(message);
  } else {
    selectedMessages.value.splice(index, 1);
  }
}

function clearContext() {
  selectedMessages.value = [];
  if (confirm('是否同时清空当前会话的所有消息？')) {
    currentChat.value.messages = [];
  }
}

function uploadFile() {
  // 模拟文件上传
  alert('文件上传功能在实际应用中需要与后端API集成');
  showFileUpload.value = false;
}

function selectKnowledgeItem(item) {
  selectedKnowledgeItem.value = item;
}

function insertKnowledgeReference() {
  if (selectedKnowledgeItem.value) {
    userInput.value += `@${selectedKnowledgeItem.value.title} `;
    showKnowledgeBase.value = false;
    selectedKnowledgeItem.value = null;
    focusInput();
  }
}

function selectMasterItem(item) {
  selectedMasterItem.value = item;
}

function insertMasterReference() {
  if (selectedMasterItem.value) {
    userInput.value += `#${selectedMasterItem.value.name}:${selectedMasterItem.value.concept} `;
    showMasterLibrary.value = false;
    selectedMasterItem.value = null;
    focusInput();
  }
}

function selectScriptItem(item) {
  // 如果点击的是当前已选中的项目，则不做任何操作
  // 否则设置为新选中的项目
  selectedScriptItem.value = item;
  console.log('Selected script item:', item.title); // 添加日志以便调试
}

function insertScript() {
  if (selectedScriptItem.value) {
    userInput.value += selectedScriptItem.value.content;
    showScriptLibrary.value = false;
    selectedScriptItem.value = null;
    focusInput();
  }
}

function focusInput() {
  nextTick(() => {
    if (inputField.value) {
      inputField.value.focus();
    }
  });
}

async function loadChatHistory() {
  try {
    const response = await getChatSessions();
    if (response.success && response.data.length > 0) {
      chatHistory.value = response.data;
      // 按更新时间降序排序
      chatHistory.value.sort((a, b) => new Date(b.updated_at) - new Date(a.updated_at));
      selectChat(chatHistory.value[0]);
    } else {
      createNewChat();
    }
  } catch (error) {
    console.error('加载历史会话失败:', error);
    createNewChat(); // 出错时也创建一个新会话
  }
}

// markdown 渲染方法
function renderMarkdown(content) {
  if (!content) return '';
  
  // 配置 marked 选项
  marked.setOptions({
    highlight: function(code, lang) {
      if (lang && hljs.getLanguage(lang)) {
        try {
          const result = hljs.highlight(code, { language: lang });
          return `<pre class="hljs" data-language="${lang}"><code class="hljs ${lang}">${result.value}</code></pre>`;
        } catch (err) {
          console.warn('代码高亮失败:', err);
        }
      }
      // 自动检测语言
      try {
        const result = hljs.highlightAuto(code);
        return `<pre class="hljs"><code class="hljs">${result.value}</code></pre>`;
      } catch (err) {
        console.warn('自动代码高亮失败:', err);
        return `<pre><code>${hljs.escapeHtml(code)}</code></pre>`;
      }
    },
    langPrefix: 'hljs language-',
    gfm: true,
    breaks: true,
    headerIds: false,
    mangle: false
  });
  
  return marked.parse(String(content));
}

// 应用代码高亮
function applyCodeHighlighting() {
  nextTick(() => {
    // 处理所有代码块
    document.querySelectorAll('.message-text pre code').forEach((block) => {
      if (!block.classList.contains('hljs')) {
        hljs.highlightElement(block);
      }
    });
    
    // 处理行内代码
    document.querySelectorAll('.message-text code:not(pre code)').forEach((inlineCode) => {
      if (!inlineCode.classList.contains('hljs')) {
        hljs.highlightElement(inlineCode);
      }
    });
  });
}


// 复制
function copyMessage(message, index) {
  const text = message.content || '';
  navigator.clipboard.writeText(text).then(() => {
    // 可加提示
  });
}

// 重答
function retryMessage(message, index) {
  // 找到这条AI消息之前的用户消息
  const userMessageIndex = currentChat.value.messages.slice(0, index).findLastIndex(m => m.role === 'user');
  if (userMessageIndex !== -1) {
    const originalUserMessage = currentChat.value.messages[userMessageIndex].content;
    userInput.value = originalUserMessage;
    
    // 移除从那条用户消息之后的所有消息
    currentChat.value.messages.splice(userMessageIndex + 1);
    
    // 确保有选中的模型
    if (!selectedModel.value) {
      alert('请先选择一个AI模型');
      return;
    }
    
    // 延迟发送，确保DOM更新完成
    nextTick(() => {
      sendMessage();
    });
  } else {
    alert("找不到可以重试的用户提问。");
  }
}

// 导出
function exportMessage(message, index) {
  const blob = new Blob([message.content || ''], { type: 'text/markdown' });
  const url = URL.createObjectURL(blob);
  const a = document.createElement('a');
  a.href = url;
  a.download = `AI回复_${new Date().toISOString().replace(/[:.]/g, '-')}.md`;
  document.body.appendChild(a);
  a.click();
  document.body.removeChild(a);
  URL.revokeObjectURL(url);
}

// 切换思考过程显示
function toggleReasoning(index) {
  showReasoning.value[index] = !showReasoning.value[index];
}

// 生成大纲
async function generateOutlineForMessage(message, index) {
  console.log('🔥 生成大纲按钮被点击！', { message, index });
  try {
    isGeneratingOutline.value = true;
    console.log('开始生成大纲:', message.content.substring(0, 100) + '...');

    const outline = await generateOutline(message.content, message.id);
    console.log('大纲生成成功:', outline);

    // 显示大纲预览对话框
    showOutlinePreview(outline);

  } catch (error) {
    console.error('生成大纲失败:', error);
    alert('生成大纲失败: ' + error.message);
  } finally {
    isGeneratingOutline.value = false;
  }
}

// 直接生成PPT
async function generatePPTForMessage(message, index) {
  try {
    isGeneratingDirectPPT.value = true;
    console.log('开始生成PPT:', message.content.substring(0, 100) + '...');

    // 先生成大纲
    const outline = await generateOutline(message.content, message.id);
    console.log('大纲生成成功，开始生成PPT');

    // 生成PPT
    const pptBlob = await generatePPT(outline);

    // 下载PPT文件
    const url = URL.createObjectURL(pptBlob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `${outline.title || 'AI演示文稿'}_${new Date().toISOString().slice(0, 10)}.pptx`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);

    console.log('PPT下载成功');

  } catch (error) {
    console.error('生成PPT失败:', error);
    alert('生成PPT失败: ' + error.message);
  } finally {
    isGeneratingDirectPPT.value = false;
  }
}

// 这个函数已经在下面重新定义了

// 大纲编辑方法
function addSlide() {
  currentOutline.value.slides.push({
    title: '新幻灯片',
    keywords: [],
    keywordsText: '',
    points: [{ text: '要点1', keywords: [], keywordsText: '' }]
  });
}

function deleteSlide(slideIndex) {
  if (currentOutline.value.slides.length > 1) {
    currentOutline.value.slides.splice(slideIndex, 1);
  } else {
    alert('至少需要保留一张幻灯片');
  }
}

function addPoint(slideIndex) {
  currentOutline.value.slides[slideIndex].points.push({
    text: '新要点',
    keywords: [],
    keywordsText: ''
  });
}

function deletePoint(slideIndex, pointIndex) {
  const slide = currentOutline.value.slides[slideIndex];
  if (slide.points.length > 1) {
    slide.points.splice(pointIndex, 1);
  } else {
    alert('至少需要保留一个要点');
  }
}

// 导出PPT
async function exportToPPT() {
  try {
    isGeneratingPPT.value = true;

    // 在导出前，确保所有关键词都是最新的用户输入
    currentOutline.value.slides.forEach((slide, slideIndex) => {
      // 更新幻灯片关键词
      updateSlideKeywords(slideIndex);

      // 更新要点关键词
      slide.points.forEach((point, pointIndex) => {
        updatePointKeywords(slideIndex, pointIndex);
      });
    });

    // 确保数据格式正确，只使用用户确认的关键词
    const pptOutline = {
      title: currentOutline.value.title,
      slides: currentOutline.value.slides.map(slide => ({
        title: slide.title,
        keywords: slide.keywords || [], // 只使用用户输入的关键词
        points: slide.points.map(point => ({
          text: point.text,
          keywords: point.keywords || [] // 只使用用户输入的关键词
        }))
      }))
    };

    console.log('🎯 发送到后端的大纲数据（仅用户关键词）:', pptOutline);

    const pptBlob = await generatePPT(pptOutline);

    // 下载PPT文件
    const url = URL.createObjectURL(pptBlob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `${currentOutline.value.title || 'AI演示文稿'}_${new Date().toISOString().slice(0, 10)}.pptx`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);

    console.log('PPT下载成功');
    showOutlineModal.value = false;

  } catch (error) {
    console.error('生成PPT失败:', error);
    alert('生成PPT失败: ' + error.message);
  } finally {
    isGeneratingPPT.value = false;
  }
}

// 关键词处理方法 - 严格按用户输入，不添加默认关键词
function updateSlideKeywords(slideIndex) {
  const slide = currentOutline.value.slides[slideIndex];
  // 只使用用户输入的关键词，去除空白和空值
  slide.keywords = slide.keywordsText ?
    slide.keywordsText.split(',').map(k => k.trim()).filter(k => k) : [];

  console.log(`📝 用户更新幻灯片 ${slideIndex + 1} 关键词:`, slide.keywords);
}

function updatePointKeywords(slideIndex, pointIndex) {
  const point = currentOutline.value.slides[slideIndex].points[pointIndex];
  // 只使用用户输入的关键词，去除空白和空值
  point.keywords = point.keywordsText ?
    point.keywordsText.split(',').map(k => k.trim()).filter(k => k) : [];

  console.log(`📝 用户更新要点 ${slideIndex + 1}-${pointIndex + 1} 关键词:`, point.keywords);
}

// 自动生成关键词
function generateKeywordsForText(text) {
  const keywords = [];
  const keywordPatterns = [
    /(AI|人工智能|智能|技术|创新|发展|未来|数字化|自动化)/gi,
    /(重要|关键|核心|主要|首要|优势|特色|亮点|突出|显著)/gi,
    /(解决方案|策略|方法|方式|模式|系统|平台|框架|工具|服务)/gi,
    /(提升|优化|改进|增强|加强|推进|促进|实现|达成|完成|支持)/gi,
    /(成功|完成|就绪|可用|启动|运行|生效|确认)/gi,
    /(功能|特性|能力|性能|效果|结果|成果|价值)/gi,
    /(\d+%|\d+倍|\d+年|\d+个|\d+项|\d+种|\d+万|\d+亿)/g
  ];

  keywordPatterns.forEach(pattern => {
    let match;
    while ((match = pattern.exec(text)) !== null) {
      const keyword = match[0];
      if (!keywords.includes(keyword)) {
        keywords.push(keyword);
      }
    }
  });

  return keywords;
}

// 显示大纲预览模态框
function showOutlinePreview(outline) {
  console.log('🔍 原始大纲数据:', outline);

  // 转换大纲格式以支持关键词编辑
  const convertedOutline = {
    title: outline.title,
    slides: outline.slides.map((slide, slideIndex) => {
      console.log(`📄 处理幻灯片 ${slideIndex + 1}:`, slide);

      // 使用AI生成的关键词作为初始值，如果没有则生成默认关键词
      const titleKeywords = slide.keywords || generateKeywordsForText(slide.title);

      const convertedSlide = {
        title: slide.title,
        keywords: titleKeywords, // 保存关键词数组
        keywordsText: titleKeywords.join(', '), // 显示在输入框中，用户可编辑
        points: slide.points.map((point, pointIndex) => {
          console.log(`  📝 处理要点 ${pointIndex + 1}:`, point, typeof point);

          let pointText, pointKeywords;

          if (typeof point === 'string') {
            pointText = point;
            pointKeywords = []; // 字符串类型的要点，初始无关键词
          } else if (point && typeof point === 'object') {
            pointText = point.text || String(point);
            pointKeywords = point.keywords || []; // 使用AI生成的关键词，如果没有则为空
          } else {
            pointText = String(point);
            pointKeywords = [];
          }

          // 如果没有AI生成的关键词，提供默认建议（但用户可以完全修改）
          if (pointKeywords.length === 0) {
            pointKeywords = generateKeywordsForText(pointText);
          }

          const convertedPoint = {
            text: pointText,
            keywords: pointKeywords, // 保存关键词数组
            keywordsText: pointKeywords.join(', ') // 显示在输入框中，用户可编辑
          };

          console.log(`  ✅ 转换后的要点:`, convertedPoint);
          return convertedPoint;
        })
      };

      console.log(`✅ 转换后的幻灯片:`, convertedSlide);
      return convertedSlide;
    })
  };

  console.log('🎯 最终转换后的大纲数据（含默认关键词建议）:', convertedOutline);
  console.log('⚠️  注意：用户编辑后将完全以用户输入为准');

  // 确保响应式更新
  currentOutline.value = JSON.parse(JSON.stringify(convertedOutline));

  // 延迟显示模态框，确保数据已更新
  nextTick(() => {
    showOutlineModal.value = true;
    console.log('📱 模态框已显示，当前大纲数据:', currentOutline.value);
  });
}

// 检测PPT生成指令
function detectPPTCommand(input) {
  const normalizedInput = input.trim().toLowerCase();

  // 精确匹配的指令
  const exactCommands = [
    '生成ppt', '生成PPT', '制作ppt', '制作PPT',
    '导出ppt', '导出PPT', '创建ppt', '创建PPT',
    'ppt', 'PPT', '幻灯片', '演示文稿', '大纲'
  ];

  // 包含关键词的指令模式
  const keywordPatterns = [
    /^(请|帮我|帮忙)?(生成|制作|创建|导出).*(ppt|PPT|幻灯片|演示文稿|大纲)$/,
    /^(ppt|PPT|幻灯片|演示文稿|大纲)$/,
    /^.*(生成|制作|创建|导出).*(ppt|PPT|幻灯片|演示文稿).*$/
  ];

  // 检查精确匹配
  if (exactCommands.some(command => normalizedInput === command.toLowerCase())) {
    return true;
  }

  // 检查模式匹配
  return keywordPatterns.some(pattern => pattern.test(normalizedInput));
}

// 处理PPT生成指令
function handlePPTCommand() {
  console.log('🎯 检测到PPT生成指令，准备弹出大纲对话框');

  // 检查是否有最近的AI回答
  const session = currentSession.value;
  if (!session || !session.messages || session.messages.length === 0) {
    alert('💡 请先与AI进行对话，获得回答内容后再生成PPT大纲');
    return;
  }

  // 找到最后一条AI回答
  const lastAIMessage = [...session.messages].reverse().find(msg =>
    msg.role === 'assistant' &&
    msg.content &&
    msg.content.trim() &&
    !msg.isError &&
    !msg.isThinking
  );

  if (!lastAIMessage) {
    alert('💡 没有找到可用的AI回答内容，请先与AI对话获得有效回答');
    return;
  }

  console.log('📝 找到最后一条AI回答，开始生成大纲');
  console.log('📄 回答内容预览:', lastAIMessage.content.substring(0, 100) + '...');

  // 显示提示信息
  const notification = document.createElement('div');
  notification.style.cssText = `
    position: fixed;
    top: 20px;
    right: 20px;
    background: #4CAF50;
    color: white;
    padding: 12px 20px;
    border-radius: 8px;
    z-index: 10000;
    font-size: 14px;
    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
  `;
  notification.textContent = '🎯 正在基于最新AI回答生成PPT大纲...';
  document.body.appendChild(notification);

  // 3秒后移除提示
  setTimeout(() => {
    if (notification.parentNode) {
      notification.parentNode.removeChild(notification);
    }
  }, 3000);

  // 自动触发生成大纲
  generateOutlineForMessage(lastAIMessage, session.messages.indexOf(lastAIMessage));
}

// 从大纲生成PPT（保留兼容性）
async function generatePPTFromOutline(outline) {
  try {
    const pptBlob = await generatePPT(outline);

    // 下载PPT文件
    const url = URL.createObjectURL(pptBlob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `${outline.title || 'AI演示文稿'}_${new Date().toISOString().slice(0, 10)}.pptx`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);

    console.log('PPT下载成功');

  } catch (error) {
    console.error('生成PPT失败:', error);
    alert('生成PPT失败: ' + error.message);
  }
}

// ==================== RAG功能 ====================

// 切换RAG上传界面
function toggleRAGUpload() {
  showRAGUpload.value = !showRAGUpload.value;
  if (showRAGUpload.value) {
    loadUploadedFiles();
  }
}

// 处理文件选择
function handleFileSelect(event) {
  const files = Array.from(event.target.files);
  uploadFiles(files);
}

// 处理文件拖拽
function handleFileDrop(event) {
  event.preventDefault();
  const files = Array.from(event.dataTransfer.files);
  uploadFiles(files);
}

// 上传文件到RAG知识库
async function uploadFiles(files) {
  if (!files || files.length === 0) return;

  isUploadingFiles.value = true;

  try {
    const formData = new FormData();
    files.forEach(file => {
      formData.append('files', file);
    });

    // 添加API密钥 - 从设置中获取
    const apiKey = getApiKeyFromSettings();
    const baseUrl = getApiBaseUrlFromSettings();

    if (!apiKey) {
      alert('❌ 请先在设置页面配置OpenAI API密钥\n\n步骤：\n1. 点击右上角设置按钮\n2. 进入"AI设置"页面\n3. 填写API密钥和端点地址\n4. 保存设置后重试');
      return;
    }

    formData.append('apiKey', apiKey);
    if (baseUrl) {
      formData.append('baseUrl', baseUrl);
    }

    console.log('🔄 开始上传文件到RAG知识库...');

    const response = await fetch('/api/rag/upload-batch', {
      method: 'POST',
      body: formData
    });

    const result = await response.json();

    if (result.success) {
      console.log('✅ 文件上传成功:', result.message);
      alert(`✅ ${result.message}`);
      loadUploadedFiles(); // 刷新文件列表
    } else {
      console.error('❌ 文件上传失败:', result.error);
      alert(`❌ 上传失败: ${result.error}`);
    }

  } catch (error) {
    console.error('❌ 上传文件时发生错误:', error);
    alert('❌ 上传失败: ' + error.message);
  } finally {
    isUploadingFiles.value = false;
  }
}

// 加载已上传的文件列表
async function loadUploadedFiles() {
  try {
    const response = await fetch('/api/rag/documents');
    const result = await response.json();

    if (result.success) {
      uploadedFiles.value = result.documents;
    } else {
      console.error('❌ 获取文件列表失败:', result.error);
    }
  } catch (error) {
    console.error('❌ 获取文件列表时发生错误:', error);
  }
}

// 格式化文件大小
function formatFileSize(bytes) {
  if (bytes === 0) return '0 Bytes';
  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

// 从设置中获取API密钥
function getApiKeyFromSettings() {
  try {
    const settings = localStorage.getItem('appSettings');
    if (settings) {
      const parsedSettings = JSON.parse(settings);
      return parsedSettings.apiKey || '';
    }
  } catch (error) {
    console.warn('读取API密钥失败:', error);
  }
  return '';
}

// 从设置中获取API Base URL
function getApiBaseUrlFromSettings() {
  try {
    const settings = localStorage.getItem('appSettings');
    if (settings) {
      const parsedSettings = JSON.parse(settings);
      return parsedSettings.api_endpoint || '';
    }
  } catch (error) {
    console.warn('读取API Base URL失败:', error);
  }
  return '';
}

// 查询RAG知识库
async function queryRAG(query) {
  try {
    const apiKey = getApiKeyFromSettings();
    const baseUrl = getApiBaseUrlFromSettings();

    if (!apiKey) {
      alert('❌ 请先在设置页面配置OpenAI API密钥');
      return null;
    }

    console.log('🔍 查询RAG知识库:', query);

    const response = await fetch('/api/rag/query', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        query,
        mode: 'hybrid',
        apiKey,
        baseUrl
      })
    });

    const result = await response.json();

    if (result.success) {
      console.log('✅ RAG查询成功:', result.result);
      return result.result;
    } else {
      console.error('❌ RAG查询失败:', result.error);
      return null;
    }

  } catch (error) {
    console.error('❌ RAG查询时发生错误:', error);
    return null;
  }
}

onMounted(async () => {
  await loadChatModels();
  await loadChatHistory();
  focusInput();
});
</script>

<style scoped>
.chat-view {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.chat-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--spacing-md);
  flex-wrap: wrap;
}

.page-title {
  font-size: var(--font-size-xl);
  color: var(--primary-color);
  margin: 0;
}

.model-selector {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
}

.model-selector select {
  width: 150px;
}

.chat-controls {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
}

.toggle-container {
  display: flex;
  align-items: center;
  cursor: pointer;
}

.toggle-label {
  margin-left: var(--spacing-xs);
}

.chat-container {
  display: flex;
  flex: 1;
  gap: var(--spacing-md);
  min-height: 0;
}

.chat-sidebar {
  width: 250px;
  border-right: 1px solid var(--border-color);
  padding-right: var(--spacing-md);
  overflow-y: auto;
  display: flex;
  flex-direction: column;
}

.chat-history {
  padding: var(--spacing-md);
  flex: 1;
  display: flex;
  flex-direction: column;
}

.chat-history h3 {
  margin-bottom: var(--spacing-md);
  color: var(--secondary-color);
}

.chat-list {
  list-style: none;
  padding: 0;
  flex: 1;
  overflow-y: auto;
}

.chat-list li {
  padding: var(--spacing-sm) var(--spacing-md);
  border-radius: var(--border-radius);
  margin-bottom: var(--spacing-xs);
  cursor: pointer;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.chat-list li:hover {
  background-color: rgba(0, 0, 0, 0.05);
}

.chat-list li .chat-actions {
  display: none;
  gap: 5px;
}

.chat-list li:hover .chat-actions {
  display: flex;
}

.chat-list li.active {
  background-color: rgba(74, 107, 175, 0.1);
  font-weight: bold;
}

.chat-title {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  flex: 1;
  margin-right: 8px;
}

.action-btn {
  background: none;
  border: none;
  cursor: pointer;
  font-size: var(--font-size-sm);
  padding: 2px;
  border-radius: 50%;
}

.action-btn:hover {
  background-color: rgba(0, 0, 0, 0.1);
}

.history-controls {
  text-align: center;
  margin-top: var(--spacing-sm);
}

.chat-main {
  flex: 1;
  display: flex;
  flex-direction: column;
  min-width: 0;
}

.chat-main-loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: var(--text-muted);
  font-size: var(--font-size-lg);
  gap: var(--spacing-md);
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 4px solid #f3f3f3;
  border-top: 4px solid var(--primary-color);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.messages-container {
  flex: 1;
  overflow-y: auto;
  padding: var(--spacing-md);
  margin-bottom: var(--spacing-md);
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius);
  background: var(--bg-light);
  width: 100%;
}

.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  color: #888;
  text-align: center;
}

.empty-icon {
  font-size: 3rem;
  margin-bottom: var(--spacing-md);
}

.message-list {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-md);
}

.message {
  display: flex;
  margin-bottom: var(--spacing-md);
  gap: var(--spacing-sm);
  opacity: 1;
  transition: opacity 0.3s ease;
}

.message.thinking {
  opacity: 0.8;
  animation: typing 1s infinite;
}

@keyframes typing {
  0%, 100% { opacity: 0.8; }
  50% { opacity: 0.5; }
}

.loading-dots {
  display: inline-block;
  animation: dots 1.5s infinite;
}

@keyframes dots {
  0%, 20% { content: '.'; }
  40% { content: '..'; }
  60%, 100% { content: '...'; }
}

.message-avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  background: var(--bg-light);
  border: 1px solid var(--border-color);
  font-size: 1.2em;
  flex-shrink: 0;
}

.message-content {
  min-width: 0;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
}

.message-content.user-right {
  align-items: flex-end;
}

.message-text {
  padding: var(--spacing-sm);
  font-size: 0.9em;
  border-radius: var(--border-radius);
  background: white;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
  white-space: pre-wrap;
  word-break: break-word;
  max-width: 100%;
  font-family:Cambria, Cochin, Georgia, Times, 'Times New Roman', serif,微软雅黑;
}

.user-message .message-text {
  background: var(--primary-color-light);
  color: var(--primary-color-dark);
}

.ai-message .message-text {
  background: white;
}

.debug-message {
  opacity: 0.7;
}

.debug-message .message-text {
  background: var(--bg-light);
  font-style: italic;
}

.message-time {
  font-size: 0.8em;
  color: var(--text-light);
  margin-top: 4px;
}

.message-actions {
  margin-top: 6px;
  display: flex;
  gap: 8px;
}

.bottom-actions {
  margin-top: 6px;
  display: flex;
  gap: 8px;
}

.action-btn-icon {
  background: none;
  border: none;
  cursor: pointer;
  padding: 8px;
  border-radius: 4px;
  transition: background-color 0.2s;
}

.action-btn-icon:hover {
  background: var(--bg-light);
}

.input-container {
  padding: var(--spacing-md);
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius);
  background: white;
}

.input-tools {
  display: flex;
  gap: var(--spacing-sm);
  margin-bottom: var(--spacing-sm);
}

.tool-btn {
  background: none;
  border: none;
  cursor: pointer;
  padding: 8px;
  border-radius: 4px;
  transition: background-color 0.2s;
}

.tool-btn:hover {
  background: var(--bg-light);
}

.input-field {
  display: flex;
  gap: var(--spacing-sm);
  align-items: flex-end;
}

.input-actions-left {
  display: flex;
  gap: 8px;
}

.rag-button {
  padding: 8px 12px;
  background: var(--primary-color);
  color: white;
  border: none;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s ease;
  font-size: 14px;
}

.rag-button:hover {
  background: var(--primary-hover);
  transform: translateY(-1px);
}

/* RAG上传区域样式 */
.rag-upload-area {
  margin-bottom: 16px;
  padding: 20px;
  background: var(--bg-secondary);
  border-radius: 12px;
  border: 1px solid var(--border-color);
}

.upload-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.upload-header h4 {
  margin: 0;
  color: var(--text-primary);
  font-size: 16px;
}

.close-btn {
  background: none;
  border: none;
  font-size: 20px;
  cursor: pointer;
  color: var(--text-secondary);
  padding: 4px 8px;
  border-radius: 4px;
  transition: all 0.2s ease;
}

.close-btn:hover {
  background: var(--bg-hover);
  color: var(--text-primary);
}

.upload-zone {
  border: 2px dashed var(--border-color);
  border-radius: 8px;
  padding: 40px 20px;
  text-align: center;
  transition: all 0.2s ease;
  cursor: pointer;
}

.upload-zone:hover {
  border-color: var(--primary-color);
  background: var(--bg-hover);
}

.upload-content i {
  font-size: 48px;
  color: var(--primary-color);
  margin-bottom: 16px;
}

.upload-content p {
  margin: 8px 0;
  color: var(--text-primary);
  font-size: 16px;
}

.upload-content small {
  color: var(--text-secondary);
  font-size: 14px;
}

.uploaded-files {
  margin-top: 16px;
  padding-top: 16px;
  border-top: 1px solid var(--border-color);
}

.uploaded-files h5 {
  margin: 0 0 12px 0;
  color: var(--text-primary);
  font-size: 14px;
}

.file-item {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 12px;
  background: var(--bg-primary);
  border-radius: 6px;
  margin-bottom: 8px;
}

.file-item i {
  color: var(--primary-color);
}

.file-item span {
  color: var(--text-primary);
  font-size: 14px;
}

.file-size {
  color: var(--text-secondary) !important;
  font-size: 12px !important;
}

.chat-input {
  flex: 1;
  min-height: 60px;
  max-height: 200px;
  resize: vertical;
  padding: var(--spacing-sm);
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius);
  font-family: inherit;
}

.send-btn {
  align-self: flex-end;
  padding: 8px 16px;
  background: var(--primary-color);
  color: white;
  border: none;
  border-radius: var(--border-radius);
  cursor: pointer;
  transition: background-color 0.2s;
}

.send-btn:hover {
  background: var(--primary-color-dark);
}

.send-btn:disabled {
  background: var(--border-color);
  cursor: not-allowed;
}

.send-btn.streaming {
  background: #dc3545;
}

.send-btn.streaming:hover {
  background: #c82333;
}

.input-hint {
  margin-top: 4px;
  font-size: 0.8em;
  color: var(--text-light);
  text-align: right;
}

/* 模态框样式 */
.modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.modal-content {
  background-color: white;
  border-radius: var(--border-radius);
  width: 500px;
  max-width: 90%;
  max-height: 80vh;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--spacing-md);
  border-bottom: 1px solid var(--border-color);
}

.modal-header h3 {
  margin: 0;
  color: var(--secondary-color);
}

.close-btn {
  background: none;
  border: none;
  font-size: 1.5rem;
  cursor: pointer;
  color: #888;
}

.modal-body {
  padding: var(--spacing-md);
  overflow-y: auto;
  flex: 1;
}

.modal-footer {
  padding: var(--spacing-md);
  border-top: 1px solid var(--border-color);
  display: flex;
  justify-content: flex-end;
  gap: var(--spacing-sm);
}

.selection-list {
  list-style: none;
  padding: 0;
  margin-top: var(--spacing-md);
}

.selection-list li {
  padding: var(--spacing-sm);
  border-radius: var(--border-radius);
  cursor: pointer;
  margin-bottom: var(--spacing-xs);
}

.selection-list li:hover {
  background-color: rgba(74, 107, 175, 0.1);
}

.selection-list li.active {
  background-color: var(--primary-color);
  color: white;
}

.file-input {
  margin-top: var(--spacing-md);
  width: 100%;
}

.script-categories {
  display: flex;
  flex-wrap: wrap;
  gap: var(--spacing-sm);
  margin-bottom: var(--spacing-md);
}

.category-btn {
  padding: var(--spacing-xs) var(--spacing-sm);
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius);
  background: none;
  cursor: pointer;
}

.category-btn.active {
  background-color: var(--primary-color);
  color: white;
  border-color: var(--primary-color);
}

@media (max-width: 768px) {
  .chat-header {
    flex-direction: column;
    align-items: flex-start;
  }
  
  .chat-sidebar {
    display: none;
  }
  
  .message {
    margin-left: 0 !important;
    margin-right: 0 !important;
  }
}

/* markdown 列表样式修正 */
.message-text :deep(ul),
.message-text :deep(ol) {
  margin: 0 0 0.5em 1.2em; /* 减小左边距 */
  padding: 0;
}

.message-text :deep(li) {
  margin: 0;
  list-style-position: outside;
  line-height:1.4; /* 减小行距 */
  font-weight: 300;
}

.message-text :deep(ul) {
  margin: 0;
  padding-left: 2.2em;
  list-style-position: outside;
  line-height: 0; /* 减小行距 */
}

.message-text :deep(ol) {
  margin: 0;
  padding-left: 2.2em;
  list-style-position: outside;
  line-height: 0; /* 减小行距 */
  font-weight: 600;
}

/* 代码高亮样式 */
.message-text :deep(pre) {
  background: #1e1e1e;
  border: 1px solid #3c3c3c;
  border-radius: 8px;
  padding: 16px;
  overflow-x: auto;
  margin: 12px 0;
  position: relative;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.message-text :deep(pre::before) {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: linear-gradient(90deg, #ff6b6b, #4ecdc4, #45b7d1, #96ceb4, #feca57);
  border-radius: 8px 8px 0 0;
}

.message-text :deep(pre.hljs) {
  background: #1e1e1e;
  border: 1px solid #3c3c3c;
  border-radius: 8px;
  padding: 16px;
  overflow-x: auto;
  margin: 12px 0;
  position: relative;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.message-text :deep(pre.hljs::before) {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: linear-gradient(90deg, #ff6b6b, #4ecdc4, #45b7d1, #96ceb4, #feca57);
  border-radius: 8px 8px 0 0;
}

.message-text :deep(code) {
  background: #f1f3f4;
  padding: 2px 6px;
  border-radius: 4px;
  font-family: 'SFMono-Regular', Consolas, 'Liberation Mono', Menlo, 'Courier New', monospace;
  font-size: 0.85em;
  color: #d73a49;
  border: 1px solid #e1e4e8;
}

.message-text :deep(pre code) {
  background: none;
  padding: 0;
  border-radius: 0;
  border: none;
  color: #d4d4d4;
  font-size: 0.9em;
  line-height: 1.5;
}

.message-text :deep(pre code.hljs) {
  background: none;
  padding: 0;
  border-radius: 0;
  border: none;
  color: #d4d4d4;
  font-size: 0.9em;
  line-height: 1.5;
}

/* 语法高亮颜色 - 基于 GitHub Dark 主题 */
.message-text :deep(.hljs-keyword) {
  color: #ff7b72;
}

.message-text :deep(.hljs-string) {
  color: #a5d6ff;
}

.message-text :deep(.hljs-comment) {
  color: #8b949e;
  font-style: italic;
}

.message-text :deep(.hljs-number) {
  color: #79c0ff;
}

.message-text :deep(.hljs-function) {
  color: #d2a8ff;
}

.message-text :deep(.hljs-variable) {
  color: #ffa657;
}

.message-text :deep(.hljs-params) {
  color: #ffa657;
}

.message-text :deep(.hljs-title) {
  color: #d2a8ff;
}

.message-text :deep(.hljs-built_in) {
  color: #ffa657;
}

.message-text :deep(.hljs-literal) {
  color: #ff7b72;
}

.message-text :deep(.hljs-type) {
  color: #ffa657;
}

.message-text :deep(.hljs-property) {
  color: #79c0ff;
}

.message-text :deep(.hljs-operator) {
  color: #ff7b72;
}

.message-text :deep(.hljs-punctuation) {
  color: #c9d1d9;
}

.message-text :deep(.hljs-regexp) {
  color: #a5d6ff;
}

.message-text :deep(.hljs-symbol) {
  color: #d2a8ff;
}

.message-text :deep(.hljs-tag) {
  color: #7ee787;
}

.message-text :deep(.hljs-name) {
  color: #7ee787;
}

.message-text :deep(.hljs-attr) {
  color: #79c0ff;
}

.message-text :deep(.hljs-value) {
  color: #a5d6ff;
}

.message-text :deep(.hljs-class) {
  color: #ffa657;
}

.message-text :deep(.hljs-title.class_) {
  color: #ffa657;
}

.message-text :deep(.hljs-meta) {
  color: #ff7b72;
}

.message-text :deep(.hljs-meta .hljs-keyword) {
  color: #ff7b72;
}

.message-text :deep(.hljs-emphasis) {
  font-style: italic;
}

.message-text :deep(.hljs-strong) {
  font-weight: bold;
}

/* 代码块滚动条样式 */
.message-text :deep(pre::-webkit-scrollbar) {
  height: 6px;
}

.message-text :deep(pre::-webkit-scrollbar-track) {
  background: #2d2d2d;
  border-radius: 3px;
}

.message-text :deep(pre::-webkit-scrollbar-thumb) {
  background: #555;
  border-radius: 3px;
}

.message-text :deep(pre::-webkit-scrollbar-thumb:hover) {
  background: #777;
}

/* 行内代码样式 */
.message-text :deep(p code) {
  background: #f1f3f4;
  color: #d73a49;
  padding: 2px 6px;
  border-radius: 4px;
  font-family: 'SFMono-Regular', Consolas, 'Liberation Mono', Menlo, monospace;
  font-size: 0.85em;
  border: 1px solid #e1e4e8;
}

/* 代码块语言标识 */
.message-text :deep(pre[data-language]) {
  position: relative;
}

.message-text :deep(pre[data-language]::after) {
  content: attr(data-language);
  position: absolute;
  top: 8px;
  right: 12px;
  font-size: 0.75em;
  color: #888;
  background: #2d2d2d;
  padding: 2px 6px;
  border-radius: 3px;
  font-family: 'SFMono-Regular', Consolas, 'Liberation Mono', Menlo, monospace;
  text-transform: uppercase;
  font-weight: 500;
}

/* 用户消息样式调整 */
.user-message {
  flex-direction: row-reverse;
}

.user-message .message-avatar {
  order: 2;
}

.user-message .message-content {
  order: 1;
  margin-right: var(--spacing-sm);
}

/* 表格样式 */
.message-text :deep(table) {
  border-collapse: collapse;
  width: 100%;
  margin: 8px 0;
}

.message-text :deep(th),
.message-text :deep(td) {
  border: 1px solid #ddd;
  padding: 8px;
  text-align: left;
}

/* 思考过程样式 */
.reasoning-section {
  margin-bottom: var(--spacing-sm);
  border: 1px solid #e0e0e0;
  border-radius: var(--border-radius);
  background: #f8f9fa;
}

.reasoning-header {
  display: flex;
  align-items: center;
  padding: var(--spacing-sm);
  background: #f0f0f0;
  border-bottom: 1px solid #e0e0e0;
  border-radius: var(--border-radius) var(--border-radius) 0 0;
  cursor: pointer;
}

.reasoning-icon {
  margin-right: var(--spacing-xs);
  font-size: 1.1em;
}

.reasoning-title {
  flex: 1;
  font-weight: 500;
  color: #666;
  font-size: 0.9em;
}

.reasoning-toggle {
  background: none;
  border: 1px solid #ccc;
  border-radius: 4px;
  padding: 2px 8px;
  font-size: 0.8em;
  color: #666;
  cursor: pointer;
  transition: all 0.2s ease;
}

.reasoning-toggle:hover {
  background: #e0e0e0;
  border-color: #999;
}

.reasoning-content {
  padding: var(--spacing-sm);
  font-family: 'SFMono-Regular', Consolas, 'Liberation Mono', Menlo, monospace;
  font-size: 0.85em;
  line-height: 1.4;
  color: #555;
  white-space: pre-wrap;
  word-break: break-word;
  max-height: 300px;
  overflow-y: auto;
  background: white;
  border-radius: 0 0 var(--border-radius) var(--border-radius);
}

/* 大纲模态框样式 */
.outline-modal {
  max-width: 800px;
  max-height: 90vh;
  overflow-y: auto;
}

.outline-editor {
  padding: var(--spacing-md);
}

.outline-title {
  margin-bottom: var(--spacing-md);
}

.outline-title label {
  display: block;
  margin-bottom: var(--spacing-xs);
  font-weight: 500;
  color: #333;
}

.title-input {
  width: 100%;
  padding: var(--spacing-sm);
  border: 1px solid #ddd;
  border-radius: var(--border-radius);
  font-size: 1.1em;
  font-weight: 500;
}

.slides-container {
  margin-bottom: var(--spacing-md);
}

.slide-editor {
  border: 1px solid #e0e0e0;
  border-radius: var(--border-radius);
  margin-bottom: var(--spacing-md);
  padding: var(--spacing-md);
  background: #f9f9f9;
}

.slide-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--spacing-sm);
}

.slide-number {
  font-weight: 500;
  color: #666;
}

.delete-slide-btn {
  background: none;
  border: none;
  cursor: pointer;
  font-size: 1.2em;
  padding: 4px;
  border-radius: 4px;
  transition: background-color 0.2s;
}

.delete-slide-btn:hover {
  background: #ffebee;
}

.slide-title {
  margin-bottom: var(--spacing-sm);
}

.slide-title label {
  display: block;
  margin-bottom: var(--spacing-xs);
  font-weight: 500;
  color: #333;
}

.slide-title-input {
  width: 100%;
  padding: var(--spacing-sm);
  border: 1px solid #ddd;
  border-radius: var(--border-radius);
  font-weight: 500;
}

.slide-keywords {
  margin-bottom: var(--spacing-sm);
}

.slide-keywords label {
  display: block;
  margin-bottom: var(--spacing-xs);
  font-weight: 500;
  color: #333;
}

.slide-points label {
  display: block;
  margin-bottom: var(--spacing-xs);
  font-weight: 500;
  color: #333;
}

.point-item-container {
  margin-bottom: var(--spacing-sm);
}

.point-item {
  display: flex;
  align-items: center;
  margin-bottom: var(--spacing-xs);
}

.point-keywords {
  margin-left: 0;
  margin-top: var(--spacing-xs);
}

.keywords-label {
  display: block;
  font-size: 0.8em;
  color: #666;
  margin-bottom: 4px;
  font-weight: 500;
}

.keywords-input {
  width: 100%;
  padding: var(--spacing-xs);
  border: 1px solid #ddd;
  border-radius: var(--border-radius);
  font-size: 0.9em;
  color: #666;
}

.keywords-input.small {
  font-size: 0.8em;
  padding: 4px 8px;
}

.keywords-input:focus {
  border-color: #4caf50;
  outline: none;
}

.point-input {
  flex: 1;
  padding: var(--spacing-sm);
  border: 1px solid #ddd;
  border-radius: var(--border-radius);
  margin-right: var(--spacing-xs);
}

.delete-point-btn {
  background: #ff4444;
  color: white;
  border: none;
  border-radius: 50%;
  width: 24px;
  height: 24px;
  cursor: pointer;
  font-size: 14px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: background-color 0.2s;
}

.delete-point-btn:hover {
  background: #cc0000;
}

.add-point-btn {
  background: #e8f5e8;
  color: #2e7d32;
  border: 1px solid #4caf50;
  border-radius: var(--border-radius);
  padding: var(--spacing-xs) var(--spacing-sm);
  cursor: pointer;
  font-size: 0.9em;
  transition: all 0.2s;
}

.add-point-btn:hover {
  background: #4caf50;
  color: white;
}

.add-slide-btn {
  background: #e3f2fd;
  color: #1976d2;
  border: 1px solid #2196f3;
  border-radius: var(--border-radius);
  padding: var(--spacing-sm) var(--spacing-md);
  cursor: pointer;
  font-size: 1em;
  font-weight: 500;
  transition: all 0.2s;
  width: 100%;
}

.add-slide-btn:hover {
  background: #2196f3;
  color: white;
}

/* 加载动画样式 */
.loading-spinner {
  display: inline-block;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.action-btn-icon:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.action-btn-icon:disabled:hover {
  background: var(--secondary-color);
  transform: none;
}

.message-text :deep(th) {
  background-color: #f2f2f2;
  font-weight: bold;
}

.message-text :deep(tr:nth-child(even)) {
  background-color: #f9f9f9;
}

.message-text :deep(tr:hover) {
  background-color: #f5f5f5;
}
</style>