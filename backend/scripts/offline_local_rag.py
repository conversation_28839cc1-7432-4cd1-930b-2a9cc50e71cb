#!/usr/bin/env python3
"""
离线本地RAG - 不依赖网络下载模型
使用简单的TF-IDF向量化进行文档检索
"""

import sys
import os
import json
import pickle
import re
from pathlib import Path
from typing import List, Dict, Any
from collections import Counter
import math

class OfflineLocalRAG:
    """离线本地RAG系统 - 使用TF-IDF"""
    
    def __init__(self, working_dir: str):
        self.working_dir = Path(working_dir)
        self.working_dir.mkdir(parents=True, exist_ok=True)
        
        # 存储文件
        self.documents_file = self.working_dir / "documents.json"
        self.tfidf_file = self.working_dir / "tfidf.pkl"
        
        # 文档和向量
        self.documents = []
        self.vocabulary = {}
        self.idf_scores = {}
        self.doc_vectors = []
        
        self._load_data()
    
    def _tokenize(self, text: str) -> List[str]:
        """简单分词"""
        # 移除标点符号，转换为小写
        text = re.sub(r'[^\w\s\u4e00-\u9fff]', ' ', text.lower())

        # 分割英文单词
        english_tokens = re.findall(r'[a-zA-Z]+', text)

        # 提取中文字符（单字和词组）
        chinese_chars = re.findall(r'[\u4e00-\u9fff]', text)

        # 简单的中文词组提取（2-3字词）
        chinese_bigrams = []
        chinese_trigrams = []
        for i in range(len(chinese_chars) - 1):
            chinese_bigrams.append(chinese_chars[i] + chinese_chars[i + 1])
        for i in range(len(chinese_chars) - 2):
            chinese_trigrams.append(chinese_chars[i] + chinese_chars[i + 1] + chinese_chars[i + 2])

        # 合并所有token
        tokens = english_tokens + chinese_chars + chinese_bigrams + chinese_trigrams

        return [token for token in tokens if len(token) >= 1]
    
    def _compute_tf(self, tokens: List[str]) -> Dict[str, float]:
        """计算词频(TF)"""
        token_count = len(tokens)
        tf_dict = {}
        
        for token in tokens:
            tf_dict[token] = tf_dict.get(token, 0) + 1
        
        # 归一化
        for token in tf_dict:
            tf_dict[token] = tf_dict[token] / token_count
        
        return tf_dict
    
    def _compute_idf(self):
        """计算逆文档频率(IDF)"""
        doc_count = len(self.documents)
        
        # 统计每个词出现在多少个文档中
        word_doc_count = {}
        for doc in self.documents:
            tokens = self._tokenize(doc['content'])
            unique_tokens = set(tokens)
            for token in unique_tokens:
                word_doc_count[token] = word_doc_count.get(token, 0) + 1
        
        # 计算IDF
        self.idf_scores = {}
        for word, count in word_doc_count.items():
            self.idf_scores[word] = math.log(doc_count / count)
    
    def _compute_tfidf_vector(self, text: str) -> Dict[str, float]:
        """计算TF-IDF向量"""
        tokens = self._tokenize(text)
        tf_dict = self._compute_tf(tokens)
        
        tfidf_vector = {}
        for token, tf in tf_dict.items():
            idf = self.idf_scores.get(token, 0)
            tfidf_vector[token] = tf * idf
        
        return tfidf_vector
    
    def _cosine_similarity(self, vec1: Dict[str, float], vec2: Dict[str, float]) -> float:
        """计算余弦相似度"""
        # 获取所有词汇
        all_words = set(vec1.keys()) | set(vec2.keys())
        
        # 计算点积
        dot_product = sum(vec1.get(word, 0) * vec2.get(word, 0) for word in all_words)
        
        # 计算向量长度
        norm1 = math.sqrt(sum(val ** 2 for val in vec1.values()))
        norm2 = math.sqrt(sum(val ** 2 for val in vec2.values()))
        
        if norm1 == 0 or norm2 == 0:
            return 0
        
        return dot_product / (norm1 * norm2)
    
    def _load_data(self):
        """加载已存储的文档和向量"""
        try:
            if self.documents_file.exists():
                with open(self.documents_file, 'r', encoding='utf-8') as f:
                    self.documents = json.load(f)
                print(f"📚 加载了 {len(self.documents)} 个文档")
            
            if self.tfidf_file.exists():
                with open(self.tfidf_file, 'rb') as f:
                    data = pickle.load(f)
                    self.vocabulary = data.get('vocabulary', {})
                    self.idf_scores = data.get('idf_scores', {})
                    self.doc_vectors = data.get('doc_vectors', [])
                print(f"📊 加载了TF-IDF数据")
        except Exception as e:
            print(f"⚠️ 加载数据失败: {e}")
            self.documents = []
            self.vocabulary = {}
            self.idf_scores = {}
            self.doc_vectors = []
    
    def _save_data(self):
        """保存文档和向量"""
        try:
            with open(self.documents_file, 'w', encoding='utf-8') as f:
                json.dump(self.documents, f, ensure_ascii=False, indent=2)
            
            tfidf_data = {
                'vocabulary': self.vocabulary,
                'idf_scores': self.idf_scores,
                'doc_vectors': self.doc_vectors
            }
            with open(self.tfidf_file, 'wb') as f:
                pickle.dump(tfidf_data, f)
            
            print("💾 数据保存成功")
        except Exception as e:
            print(f"❌ 数据保存失败: {e}")
    
    def add_document(self, content: str, metadata: Dict[str, Any] = None):
        """添加文档到知识库"""
        try:
            # 创建文档对象
            doc = {
                'id': len(self.documents),
                'content': content,
                'metadata': metadata or {}
            }
            
            # 添加到列表
            self.documents.append(doc)
            
            # 重新计算IDF和向量
            self._compute_idf()
            
            # 重新计算所有文档的TF-IDF向量
            self.doc_vectors = []
            for document in self.documents:
                vector = self._compute_tfidf_vector(document['content'])
                self.doc_vectors.append(vector)
            
            # 保存数据
            self._save_data()
            
            print(f"✅ 文档添加成功，ID: {doc['id']}")
            return doc['id']
            
        except Exception as e:
            print(f"❌ 文档添加失败: {e}")
            raise
    
    def query(self, query_text: str, top_k: int = 3) -> List[Dict[str, Any]]:
        """查询知识库"""
        try:
            if not self.documents:
                return []
            
            # 计算查询的TF-IDF向量
            query_vector = self._compute_tfidf_vector(query_text)
            
            # 计算与所有文档的相似度
            similarities = []
            for i, doc_vector in enumerate(self.doc_vectors):
                similarity = self._cosine_similarity(query_vector, doc_vector)
                similarities.append((i, similarity))
            
            # 按相似度排序
            similarities.sort(key=lambda x: x[1], reverse=True)

            # 调试信息
            print(f"🔍 查询: {query_text}")
            print(f"📊 相似度分数: {similarities}")

            # 获取top-k结果
            results = []
            for i, (doc_idx, similarity) in enumerate(similarities[:top_k]):
                if similarity > 0.001:  # 相似度阈值
                    result = {
                        'document': self.documents[doc_idx],
                        'similarity': float(similarity),
                        'content': self.documents[doc_idx]['content']
                    }
                    results.append(result)
            
            return results
            
        except Exception as e:
            print(f"❌ 查询失败: {e}")
            return []
    
    def get_stats(self) -> Dict[str, Any]:
        """获取统计信息"""
        return {
            'document_count': len(self.documents),
            'vocabulary_size': len(self.vocabulary),
            'method': 'TF-IDF',
            'working_dir': str(self.working_dir)
        }

def main():
    """主函数"""
    if len(sys.argv) < 3:
        print("用法: python offline_local_rag.py <working_dir> <command> [args...]")
        print("命令:")
        print("  query <query_text>     - 查询知识库")
        print("  add <content>          - 添加文档")
        print("  stats                  - 显示统计信息")
        sys.exit(1)
    
    working_dir = sys.argv[1]
    command = sys.argv[2]
    
    try:
        rag = OfflineLocalRAG(working_dir)
        
        if command == "query":
            if len(sys.argv) < 4:
                print("❌ 缺少查询文本")
                sys.exit(1)
            
            query_text = sys.argv[3]
            results = rag.query(query_text)
            
            if results:
                print(f"🔍 查询: {query_text}")
                print(f"📄 找到 {len(results)} 个相关文档:")
                
                best_result = results[0]
                response = f"根据知识库内容，{best_result['content'][:200]}..."
                
                print("LOCAL_RAG_RESULT:", response)
            else:
                print("LOCAL_RAG_RESULT: 未找到相关信息")
        
        elif command == "add":
            if len(sys.argv) < 4:
                print("❌ 缺少文档内容")
                sys.exit(1)
            
            content = sys.argv[3]
            doc_id = rag.add_document(content)
            print("LOCAL_RAG_RESULT:", f"文档添加成功，ID: {doc_id}")
        
        elif command == "stats":
            stats = rag.get_stats()
            print("LOCAL_RAG_RESULT:", json.dumps(stats, ensure_ascii=False))
        
        else:
            print(f"❌ 未知命令: {command}")
            sys.exit(1)
    
    except Exception as e:
        print(f"❌ 执行失败: {e}")
        print("LOCAL_RAG_ERROR:", str(e))
        sys.exit(1)

if __name__ == "__main__":
    main()
