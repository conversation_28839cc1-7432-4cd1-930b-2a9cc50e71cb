const { spawn } = require('child_process');
const path = require('path');
const fs = require('fs').promises;
const multer = require('multer');

class RAGService {
  constructor() {
    this.ragAnythingPath = '/opt/RAG-Anything';
    this.workingDir = path.join(__dirname, '../rag_storage');
    this.uploadsDir = path.join(__dirname, '../uploads');
    this.isInitialized = false;
    
    // 确保目录存在
    this.ensureDirectories();
  }

  async ensureDirectories() {
    try {
      await fs.mkdir(this.workingDir, { recursive: true });
      await fs.mkdir(this.uploadsDir, { recursive: true });
      console.log('📁 RAG目录创建成功');
    } catch (error) {
      console.error('❌ 创建RAG目录失败:', error);
    }
  }

  // 配置文件上传
  getUploadMiddleware() {
    const storage = multer.diskStorage({
      destination: (req, file, cb) => {
        cb(null, this.uploadsDir);
      },
      filename: (req, file, cb) => {
        // 保持原始文件名，添加时间戳避免冲突
        const timestamp = Date.now();
        const originalName = Buffer.from(file.originalname, 'latin1').toString('utf8');
        cb(null, `${timestamp}_${originalName}`);
      }
    });

    return multer({
      storage,
      limits: {
        fileSize: 50 * 1024 * 1024, // 50MB限制
      },
      fileFilter: (req, file, cb) => {
        // 支持的文件类型
        const allowedTypes = [
          'application/pdf',
          'application/msword',
          'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
          'text/plain',
          'image/jpeg',
          'image/png',
          'image/gif',
          'application/vnd.ms-excel',
          'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
        ];
        
        if (allowedTypes.includes(file.mimetype)) {
          cb(null, true);
        } else {
          cb(new Error(`不支持的文件类型: ${file.mimetype}`), false);
        }
      }
    });
  }

  // 处理文档上传和解析
  async processDocument(filePath, apiKey, baseUrl = null) {
    return new Promise((resolve, reject) => {
      console.log('🔄 开始处理文档:', filePath);
      
      const pythonScript = path.join(this.ragAnythingPath, 'examples/raganything_example.py');
      const args = [
        pythonScript,
        filePath,
        '--working_dir', this.workingDir,
        '--output', path.join(this.workingDir, 'output'),
        '--api-key', apiKey
      ];
      
      if (baseUrl) {
        args.push('--base-url', baseUrl);
      }

      // 激活虚拟环境并运行Python脚本
      const pythonProcess = spawn('bash', ['-c', `cd ${this.ragAnythingPath} && source venv/bin/activate && python ${args.join(' ')}`], {
        stdio: ['pipe', 'pipe', 'pipe']
      });

      let stdout = '';
      let stderr = '';

      pythonProcess.stdout.on('data', (data) => {
        stdout += data.toString();
        console.log('📄 RAG处理输出:', data.toString());
      });

      pythonProcess.stderr.on('data', (data) => {
        stderr += data.toString();
        console.error('⚠️ RAG处理警告:', data.toString());
      });

      pythonProcess.on('close', (code) => {
        if (code === 0) {
          console.log('✅ 文档处理完成');
          resolve({
            success: true,
            message: '文档处理完成',
            output: stdout
          });
        } else {
          console.error('❌ 文档处理失败，退出码:', code);
          reject(new Error(`文档处理失败: ${stderr}`));
        }
      });

      pythonProcess.on('error', (error) => {
        console.error('❌ Python进程错误:', error);
        reject(error);
      });
    });
  }

  // 查询RAG知识库
  async queryRAG(query, mode = 'hybrid', apiKey, baseUrl = null) {
    return new Promise((resolve, reject) => {
      console.log('🔍 查询RAG知识库:', query);
      
      // 创建临时查询脚本
      const queryScript = `
import asyncio
import sys
import os
sys.path.insert(0, '${this.ragAnythingPath}')

from lightrag.llm.openai import openai_complete_if_cache, openai_embed
from lightrag.utils import EmbeddingFunc
from raganything.raganything import RAGAnything

async def query_rag():
    try:
        rag = RAGAnything(
            working_dir='${this.workingDir}',
            llm_model_func=lambda prompt, system_prompt=None, history_messages=[], **kwargs: openai_complete_if_cache(
                "gpt-4o-mini",
                prompt,
                system_prompt=system_prompt,
                history_messages=history_messages,
                api_key='${apiKey}',
                ${baseUrl ? `base_url='${baseUrl}',` : ''}
                **kwargs,
            ),
            embedding_func=EmbeddingFunc(
                embedding_dim=3072,
                max_token_size=8192,
                func=lambda texts: openai_embed(
                    texts,
                    model="text-embedding-3-large",
                    api_key='${apiKey}',
                    ${baseUrl ? `base_url='${baseUrl}',` : ''}
                ),
            ),
        )
        
        result = await rag.query_with_multimodal('${query}', mode='${mode}')
        print("RAG_RESULT:", result)
        
    except Exception as e:
        print("RAG_ERROR:", str(e))

if __name__ == "__main__":
    asyncio.run(query_rag())
`;

      const tempScriptPath = path.join(this.workingDir, 'temp_query.py');
      
      // 写入临时脚本
      fs.writeFile(tempScriptPath, queryScript)
        .then(() => {
          const pythonProcess = spawn('bash', ['-c', `cd ${this.ragAnythingPath} && source venv/bin/activate && python ${tempScriptPath}`], {
            stdio: ['pipe', 'pipe', 'pipe']
          });

          let stdout = '';
          let stderr = '';

          pythonProcess.stdout.on('data', (data) => {
            stdout += data.toString();
          });

          pythonProcess.stderr.on('data', (data) => {
            stderr += data.toString();
          });

          pythonProcess.on('close', (code) => {
            // 清理临时文件
            fs.unlink(tempScriptPath).catch(console.error);
            
            if (code === 0) {
              // 解析结果
              const lines = stdout.split('\n');
              const resultLine = lines.find(line => line.startsWith('RAG_RESULT:'));
              
              if (resultLine) {
                const result = resultLine.replace('RAG_RESULT:', '').trim();
                resolve({
                  success: true,
                  result: result,
                  query: query
                });
              } else {
                resolve({
                  success: true,
                  result: '未找到相关信息',
                  query: query
                });
              }
            } else {
              const errorLine = stdout.split('\n').find(line => line.startsWith('RAG_ERROR:'));
              const errorMsg = errorLine ? errorLine.replace('RAG_ERROR:', '').trim() : stderr;
              reject(new Error(`查询失败: ${errorMsg}`));
            }
          });

          pythonProcess.on('error', (error) => {
            // 清理临时文件
            fs.unlink(tempScriptPath).catch(console.error);
            reject(error);
          });
        })
        .catch(reject);
    });
  }

  // 获取已上传的文档列表
  async getDocumentList() {
    try {
      const files = await fs.readdir(this.uploadsDir);
      const documents = [];
      
      for (const file of files) {
        const filePath = path.join(this.uploadsDir, file);
        const stats = await fs.stat(filePath);
        
        documents.push({
          filename: file,
          originalName: file.replace(/^\d+_/, ''), // 移除时间戳前缀
          size: stats.size,
          uploadTime: stats.mtime,
          path: filePath
        });
      }
      
      return documents.sort((a, b) => b.uploadTime - a.uploadTime);
    } catch (error) {
      console.error('❌ 获取文档列表失败:', error);
      return [];
    }
  }

  // 删除文档
  async deleteDocument(filename) {
    try {
      const filePath = path.join(this.uploadsDir, filename);
      await fs.unlink(filePath);
      console.log('🗑️ 文档删除成功:', filename);
      return true;
    } catch (error) {
      console.error('❌ 删除文档失败:', error);
      return false;
    }
  }

  // 检查RAG-Anything是否可用
  async checkRAGStatus() {
    try {
      const ragPath = path.join(this.ragAnythingPath, 'raganything');
      await fs.access(ragPath);
      return {
        available: true,
        path: this.ragAnythingPath,
        workingDir: this.workingDir
      };
    } catch (error) {
      return {
        available: false,
        error: error.message
      };
    }
  }
}

module.exports = new RAGService();
