<template>
  <div class="login-view">
    <div class="login-container">
      <div class="login-header">
        <h1 class="app-title">AIDE</h1>
        <p class="app-subtitle">AI-Integrated Design Environment</p>
      </div>
      
      <div class="login-form">
        <h2 class="form-title">登录</h2>
        <p class="form-subtitle">请输入您的账号信息</p>
        
        <div class="form-group">
          <label for="username">用户名</label>
          <input 
            type="text" 
            id="username" 
            v-model="username" 
            class="form-input"
            placeholder="请输入用户名"
            @keyup.enter="login"
          >
          <div v-if="errors.username" class="error-message">{{ errors.username }}</div>
        </div>
        
        <div class="form-group">
          <label for="password">密码</label>
          <input 
            type="password" 
            id="password" 
            v-model="password" 
            class="form-input"
            placeholder="请输入密码"
            @keyup.enter="login"
          >
          <div v-if="errors.password" class="error-message">{{ errors.password }}</div>
        </div>
        
        <div class="form-actions">
          <button class="btn-primary login-btn" @click="login" :disabled="isLoading">
            {{ isLoading ? '登录中...' : '登录' }}
          </button>
        </div>
        
        <div v-if="loginError" class="login-error">
          {{ loginError }}
        </div>
        
        <div class="register-link">
          没有账号？<router-link to="/register">立即注册</router-link>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive } from 'vue';
import { useRouter } from 'vue-router';
import { authAPI, setCurrentUserToCache } from '../services/api.js';

const router = useRouter();
const username = ref('');
const password = ref('');
const isLoading = ref(false);
const loginError = ref('');
const errors = reactive({
  username: '',
  password: ''
});

// 用户登录验证
async function login() {
  // 重置错误信息
  loginError.value = '';
  errors.username = '';
  errors.password = '';
  
  // 表单验证
  let isValid = true;
  
  if (!username.value.trim()) {
    errors.username = '请输入用户名';
    isValid = false;
  }
  
  if (!password.value.trim()) {
    errors.password = '请输入密码';
    isValid = false;
  }
  
  if (!isValid) return;
  
  // 开始登录流程
  isLoading.value = true;
  
  try {
    // 调用登录API
    const response = await authAPI.login(username.value, password.value);
    
    // 构造用户数据
    const userData = {
      id: response.user.id,
      username: response.user.username,
      displayName: response.user.displayName,
      isAdmin: response.user.role === 'admin',
      role: response.user.role,
      avatarUrl: response.user.avatarUrl,
      lastLogin: new Date().toISOString()
    };
    
    // 将用户信息存储到缓存
    setCurrentUserToCache(userData);
    
    // 重定向到首页
    router.push('/');
  } catch (error) {
    // 登录失败
    loginError.value = error.message || '登录失败，请重试';
  } finally {
    isLoading.value = false;
  }
}
</script>

<style scoped>
.login-view {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 100vh;
  background-color: #f5f5f5;
}

.login-container {
  width: 100%;
  max-width: 400px;
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.login-header {
  background-color: #4a6baf;
  color: white;
  padding: 2rem;
  text-align: center;
}

.app-title {
  font-size: 2rem;
  margin-bottom: 0.5rem;
}

.app-subtitle {
  font-size: 0.9rem;
  opacity: 0.9;
}

.login-form {
  padding: 2rem;
}

.form-title {
  font-size: 1.5rem;
  margin-bottom: 0.5rem;
  color: #333;
}

.form-subtitle {
  color: #666;
  margin-bottom: 1.5rem;
  font-size: 0.9rem;
}

.form-group {
  margin-bottom: 1.5rem;
}

.form-group label {
  display: block;
  margin-bottom: 0.5rem;
  font-weight: 500;
  color: #333;
}

.form-input {
  width: 100%;
  padding: 10px 12px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 1rem;
  transition: border-color 0.2s;
}

.form-input:focus {
  border-color: #4a6baf;
  outline: none;
}

.form-actions {
  margin-top: 2rem;
}

.login-btn {
  width: 100%;
  padding: 12px;
  font-size: 1rem;
  font-weight: 500;
}

.btn-primary {
  background-color: #4a6baf;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  transition: background-color 0.2s;
}

.btn-primary:hover {
  background-color: #3d5a8f;
}

.btn-primary:disabled {
  background-color: #7a92c9;
  cursor: not-allowed;
}

.error-message {
  color: #dc3545;
  font-size: 0.85rem;
  margin-top: 0.5rem;
}

.login-error {
  margin-top: 1rem;
  padding: 0.75rem;
  background-color: #f8d7da;
  color: #721c24;
  border-radius: 4px;
  font-size: 0.9rem;
  text-align: center;
}

.register-link {
  margin-top: 1.5rem;
  text-align: center;
  font-size: 0.9rem;
}

.register-link a {
  color: #4a6baf;
  text-decoration: none;
  font-weight: 500;
}

.register-link a:hover {
  text-decoration: underline;
}
</style>