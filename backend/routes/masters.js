/**
 * 大师库管理路由
 * 
 * 功能说明：
 * - 大师信息管理
 * - 大师分类管理
 * - 大师标签管理
 * - 大师属性管理
 */

const express = require('express');
const router = express.Router();

const db = require('../config/database');
const { validate, masterItemSchema, paginationSchema } = require('../middleware/validation');
const { authenticateToken, requireAdmin } = require('../middleware/auth');
const { asyncHandler, ValidationError, NotFoundError, AuthorizationError, createSuccessResponse } = require('../middleware/errorHandler');

// ==================== 分类管理 ====================

/**
 * @route GET /api/masters/categories
 * @desc 获取大师库分类列表
 * @access Private
 */
router.get('/categories', authenticateToken, asyncHandler(async (req, res) => {
  const categories = await db.execute(
    `SELECT mc.id, mc.name, mc.description, mc.created_at, mc.updated_at,
            COUNT(mi.id) as master_count
     FROM master_categories mc
     LEFT JOIN master_items mi ON mc.id = mi.category_id
     GROUP BY mc.id
     ORDER BY mc.name`
  );
  
  res.json(createSuccessResponse(categories));
}));

/**
 * @route POST /api/masters/categories
 * @desc 创建大师库分类
 * @access Private (Admin only)
 */
router.post('/categories', authenticateToken, requireAdmin, asyncHandler(async (req, res) => {
  const { name, description } = req.body;
  
  if (!name || name.trim().length === 0) {
    throw new ValidationError('分类名称不能为空');
  }
  
  // 检查分类名称是否已存在
  const existing = await db.execute(
    'SELECT id FROM master_categories WHERE name = ?',
    [name.trim()]
  );
  
  if (existing.length > 0) {
    throw new ValidationError('分类名称已存在');
  }
  
  // 创建分类
  const result = await db.execute(
    'INSERT INTO master_categories (id, name, description, created_by, created_at, updated_at) VALUES (?, ?, ?, ?, NOW(), NOW())',
    [name.toLowerCase().replace(/\s+/g, '_'), name.trim(), description || '', req.user.id]
  );
  
  // 获取创建的分类信息
  const categories = await db.execute(
    'SELECT id, name, description, created_at, updated_at FROM master_categories WHERE id = ?',
    [name.toLowerCase().replace(/\s+/g, '_')]
  );
  
  res.status(201).json(createSuccessResponse(categories[0], '分类创建成功'));
}));

/**
 * @route PUT /api/masters/categories/:id
 * @desc 更新大师库分类
 * @access Private (Admin only)
 */
router.put('/categories/:id', authenticateToken, requireAdmin, asyncHandler(async (req, res) => {
  const categoryId = req.params.id;
  const { name, description } = req.body;
  
  // 检查分类是否存在
  const categories = await db.execute(
    'SELECT id FROM master_categories WHERE id = ?',
    [categoryId]
  );
  
  if (categories.length === 0) {
    throw new NotFoundError('分类不存在');
  }
  
  // 构建更新语句
  const updateFields = [];
  const updateValues = [];
  
  if (name !== undefined) {
    updateFields.push('name = ?');
    updateValues.push(name.trim());
  }
  
  if (description !== undefined) {
    updateFields.push('description = ?');
    updateValues.push(description);
  }
  
  if (updateFields.length > 0) {
    updateFields.push('updated_at = NOW()');
    updateValues.push(categoryId);
    
    await db.execute(
      `UPDATE master_categories SET ${updateFields.join(', ')} WHERE id = ?`,
      updateValues
    );
  }
  
  res.json(createSuccessResponse(null, '分类更新成功'));
}));

/**
 * @route DELETE /api/masters/categories/:id
 * @desc 删除大师库分类
 * @access Private (Admin only)
 */
router.delete('/categories/:id', authenticateToken, requireAdmin, asyncHandler(async (req, res) => {
  const categoryId = req.params.id;
  
  // 检查分类是否存在
  const categories = await db.execute(
    'SELECT id FROM master_categories WHERE id = ?',
    [categoryId]
  );
  
  if (categories.length === 0) {
    throw new NotFoundError('分类不存在');
  }
  
  // 检查是否有关联的大师
  const masters = await db.execute(
    'SELECT COUNT(*) as count FROM master_items WHERE category_id = ?',
    [categoryId]
  );
  
  if (masters[0].count > 0) {
    throw new ValidationError('无法删除包含大师信息的分类');
  }
  
  // 删除分类
  await db.execute(
    'DELETE FROM master_categories WHERE id = ?',
    [categoryId]
  );
  
  res.json(createSuccessResponse(null, '分类删除成功'));
}));

// ==================== 大师信息管理 ====================

/**
 * @route GET /api/masters
 * @desc 获取大师列表
 * @access Private
 */
router.get('/', authenticateToken, validate(paginationSchema, 'query'), asyncHandler(async (req, res) => {
  const { page, limit, sortBy, sortOrder, search } = req.query;
  const { categoryId, tags } = req.query;
  const offset = (page - 1) * limit;
  
  // 构建查询条件
  let whereClause = 'WHERE 1=1';
  let queryParams = [];
  
  if (search) {
    whereClause += ' AND (mi.name LIKE ? OR mi.description LIKE ?)';
    queryParams.push(`%${search}%`, `%${search}%`);
  }
  
  if (categoryId) {
    whereClause += ' AND mi.category_id = ?';
    queryParams.push(categoryId);
  }
  
  // 如果有标签筛选
  let tagJoin = '';
  if (tags) {
    const tagList = tags.split(',').map(tag => tag.trim()).filter(tag => tag);
    if (tagList.length > 0) {
      tagJoin = `
        INNER JOIN master_item_tags mit ON mi.id = mit.item_id
        INNER JOIN master_tags mt ON mit.tag_id = mt.id
      `;
      whereClause += ` AND mt.name IN (${tagList.map(() => '?').join(',')})`;
      queryParams.push(...tagList);
    }
  }
  
  // 获取大师列表
  const masters = await db.execute(
    `SELECT DISTINCT mi.id, mi.name, mi.description, mi.avatar_url, mi.created_at, mi.updated_at,
            mi.category_id as categoryId,
            mc.name as category_name,
            u.display_name as creator_name
     FROM master_items mi
     LEFT JOIN master_categories mc ON mi.category_id = mc.id
     LEFT JOIN users u ON mi.created_by = u.id
     ${tagJoin}
     ${whereClause}
     ORDER BY mi.${sortBy} ${sortOrder}
     LIMIT ? OFFSET ?`,
    [...queryParams, String(limit), String(offset)]
  );
  
  // 获取每个大师的标签和属性
  for (let master of masters) {
    // 获取标签
    const masterTags = await db.execute(
      `SELECT mt.id, mt.name
       FROM master_tags mt
       INNER JOIN master_item_tags mit ON mt.id = mit.tag_id
       WHERE mit.item_id = ?`,
      [master.id]
    );
    master.tags = masterTags;
    
    // 获取属性
    const properties = await db.execute(
      'SELECT property_key, property_value FROM master_properties WHERE master_id = ?',
      [master.id]
    );
    master.properties = properties.reduce((acc, prop) => {
      acc[prop.property_key] = prop.property_value;
      return acc;
    }, {});
  }
  
  // 获取总数
  const countResult = await db.execute(
    `SELECT COUNT(DISTINCT mi.id) as total
     FROM master_items mi
     ${tagJoin}
     ${whereClause}`,
    queryParams
  );
  
  const total = countResult[0].total;
  const totalPages = Math.ceil(total / limit);
  
  res.json(createSuccessResponse(masters, '获取大师列表成功', {
    pagination: {
      page,
      limit,
      total,
      totalPages,
      hasNext: page < totalPages,
      hasPrev: page > 1
    }
  }));
}));

/**
 * @route GET /api/masters/:id
 * @desc 获取大师详情
 * @access Private
 */
router.get('/:id', authenticateToken, asyncHandler(async (req, res) => {
  const masterId = req.params.id;
  
  // 获取大师详情
  const masters = await db.execute(
    `SELECT mi.id, mi.name, mi.description, mi.avatar_url, mi.category_id, mi.created_by, mi.created_at, mi.updated_at,
            mc.name as category_name,
            u.display_name as creator_name
     FROM master_items mi
     LEFT JOIN master_categories mc ON mi.category_id = mc.id
     LEFT JOIN users u ON mi.created_by = u.id
     WHERE mi.id = ?`,
    [masterId]
  );
  
  if (masters.length === 0) {
    throw new NotFoundError('大师信息不存在');
  }
  
  const master = masters[0];
  
  // 获取标签
  const tags = await db.execute(
    `SELECT mt.id, mt.name
     FROM master_tags mt
     INNER JOIN master_item_tags mit ON mt.id = mit.tag_id
     WHERE mit.item_id = ?`,
    [masterId]
  );
  master.tags = tags;
  
  // 获取属性
  const properties = await db.execute(
    'SELECT property_key, property_value FROM master_properties WHERE master_id = ?',
    [masterId]
  );
  master.properties = properties.reduce((acc, prop) => {
    acc[prop.property_key] = prop.property_value;
    return acc;
  }, {});
  
  res.json(createSuccessResponse(master));
}));

/**
 * @route POST /api/masters
 * @desc 创建大师信息
 * @access Private
 */
router.post('/', authenticateToken, asyncHandler(async (req, res, next) => {
  // 手动转换properties格式
  if (req.body.properties && Array.isArray(req.body.properties)) {
    req.body.properties = req.body.properties.reduce((acc, prop) => {
      if (prop.key && prop.value) {
        acc[prop.key] = prop.value;
      }
      return acc;
    }, {});
  }
  next();
}), validate(masterItemSchema), asyncHandler(async (req, res) => {
  const { name, categoryId, description, avatar_url, tags, properties } = req.body;
  
  // 验证分类是否存在
  const categories = await db.execute(
    'SELECT id FROM master_categories WHERE id = ?',
    [categoryId]
  );
  
  if (categories.length === 0) {
    throw new ValidationError('指定的分类不存在');
  }
  
  // 检查大师名称是否已存在
  const existing = await db.execute(
    'SELECT id FROM master_items WHERE name = ?',
    [name]
  );
  
  if (existing.length > 0) {
    throw new ValidationError('大师名称已存在');
  }
  
  // 开始事务
  const connection = await db.getConnection();
  await db.beginTransaction(connection);
  
  try {
    // 创建大师信息
    const result = await db.execute(
      `INSERT INTO master_items (name, category_id, description, avatar_url, created_by, created_at, updated_at)
       VALUES (?, ?, ?, ?, ?, NOW(), NOW())`,
      [name, categoryId, description, avatar_url || null, req.user.id],
      connection
    );
    
    const masterId = result.insertId;
    
    // 处理标签
    if (tags && tags.length > 0) {
      for (const tagName of tags) {
        if (tagName.trim()) {
          // 查找或创建标签
          let existingTags = await db.execute(
            'SELECT id FROM master_tags WHERE name = ?',
            [tagName.trim()],
            connection
          );
          
          let tagId;
          if (existingTags.length === 0) {
            const tagResult = await db.execute(
              'INSERT INTO master_tags (name, created_at) VALUES (?, NOW())',
              [tagName.trim()],
              connection
            );
            tagId = tagResult.insertId;
          } else {
            tagId = existingTags[0].id;
          }
          
          // 关联标签和大师
          await db.execute(
            'INSERT INTO master_item_tags (item_id, tag_id) VALUES (?, ?)',
            [masterId, tagId],
            connection
          );
        }
      }
    }
    
    // 处理属性
    if (properties && typeof properties === 'object') {
      for (const [key, value] of Object.entries(properties)) {
        if (key.trim() && value) {
          await db.execute(
            'INSERT INTO master_properties (master_id, property_key, property_value) VALUES (?, ?, ?)',
            [masterId, key.trim(), value.toString()],
            connection
          );
        }
      }
    }
    
    await db.commitTransaction(connection);
    
    // 获取创建的大师信息
    const masters = await db.execute(
      `SELECT mi.id, mi.name, mi.description, mi.avatar_url, mi.created_at, mi.updated_at,
              mc.name as category_name,
              u.display_name as creator_name
       FROM master_items mi
       LEFT JOIN master_categories mc ON mi.category_id = mc.id
       LEFT JOIN users u ON mi.created_by = u.id
       WHERE mi.id = ?`,
      [masterId]
    );
    
    const master = masters[0];
    
    // 获取标签
    const masterTags = await db.execute(
      `SELECT mt.id, mt.name
       FROM master_tags mt
       INNER JOIN master_item_tags mit ON mt.id = mit.tag_id
       WHERE mit.item_id = ?`,
      [masterId]
    );
    master.tags = masterTags;
    
    // 获取属性
    const masterProperties = await db.execute(
      'SELECT property_key, property_value FROM master_properties WHERE master_id = ?',
      [masterId]
    );
    master.properties = masterProperties.reduce((acc, prop) => {
      acc[prop.property_key] = prop.property_value;
      return acc;
    }, {});
    
    res.status(201).json(createSuccessResponse(master, '大师信息创建成功'));
  } catch (error) {
    await db.rollbackTransaction(connection);
    throw error;
  }
}));

/**
 * @route PUT /api/masters/:id
 * @desc 更新大师信息
 * @access Private
 */
router.put('/:id', authenticateToken, asyncHandler(async (req, res, next) => {
  // 手动转换properties格式
  if (req.body.properties && Array.isArray(req.body.properties)) {
    req.body.properties = req.body.properties.reduce((acc, prop) => {
      if (prop.key && prop.value) {
        acc[prop.key] = prop.value;
      }
      return acc;
    }, {});
  }
  next();
}), asyncHandler(async (req, res) => {
  const masterId = req.params.id;
  const { name, categoryId, description, avatar_url, tags, properties } = req.body;
  
  // 检查大师是否存在和权限
  const masters = await db.execute(
    'SELECT created_by FROM master_items WHERE id = ?',
    [masterId]
  );
  
  if (masters.length === 0) {
    throw new NotFoundError('大师信息不存在');
  }
  
  if (masters[0].created_by !== req.user.id && req.user.role !== 'admin') {
    throw new AuthorizationError('无权修改此大师信息');
  }
  
  // 验证分类是否存在
  if (categoryId) {
    const categories = await db.execute(
      'SELECT id FROM master_categories WHERE id = ?',
      [categoryId]
    );
    
    if (categories.length === 0) {
      throw new ValidationError('指定的分类不存在');
    }
  }
  
  // 检查名称是否与其他大师冲突
  if (name) {
    const existing = await db.execute(
      'SELECT id FROM master_items WHERE name = ? AND id != ?',
      [name, masterId]
    );
    
    if (existing.length > 0) {
      throw new ValidationError('大师名称已存在');
    }
  }
  
  // 开始事务
  const connection = await db.getConnection();
  await db.beginTransaction(connection);
  
  try {
    // 更新基本信息
    const updateFields = [];
    const updateValues = [];
    
    if (name !== undefined) {
      updateFields.push('name = ?');
      updateValues.push(name);
    }
    
    if (categoryId !== undefined) {
      updateFields.push('category_id = ?');
      updateValues.push(categoryId);
    }
    
    if (description !== undefined) {
      updateFields.push('description = ?');
      updateValues.push(description);
    }
    
    if (avatar_url !== undefined) {
      updateFields.push('avatar_url = ?');
      updateValues.push(avatar_url);
    }
    
    if (updateFields.length > 0) {
      updateFields.push('updated_at = NOW()');
      updateValues.push(masterId);
      
      await db.execute(
        `UPDATE master_items SET ${updateFields.join(', ')} WHERE id = ?`,
        updateValues,
        connection
      );
    }
    
    // 更新标签
    if (tags !== undefined) {
      // 删除现有标签关联
      await db.execute(
        'DELETE FROM master_item_tags WHERE item_id = ?',
        [masterId],
        connection
      );
      
      // 添加新标签
      if (tags && tags.length > 0) {
        for (const tagName of tags) {
          if (tagName.trim()) {
            // 查找或创建标签
            let existingTags = await db.execute(
              'SELECT id FROM master_tags WHERE name = ?',
              [tagName.trim()],
              connection
            );
            
            let tagId;
            if (existingTags.length === 0) {
              const tagResult = await db.execute(
                'INSERT INTO master_tags (name, created_at) VALUES (?, NOW())',
                [tagName.trim()],
                connection
              );
              tagId = tagResult.insertId;
            } else {
              tagId = existingTags[0].id;
            }
            
            // 关联标签和大师
            await db.execute(
              'INSERT INTO master_item_tags (item_id, tag_id) VALUES (?, ?)',
              [masterId, tagId],
              connection
            );
          }
        }
      }
    }
    
    // 更新属性
    if (properties !== undefined) {
      // 删除现有属性
      await db.execute(
        'DELETE FROM master_properties WHERE master_id = ?',
        [masterId],
        connection
      );
      
      // 添加新属性
      if (properties && typeof properties === 'object') {
        for (const [key, value] of Object.entries(properties)) {
          if (key.trim() && value) {
            await db.execute(
              'INSERT INTO master_properties (master_id, property_key, property_value) VALUES (?, ?, ?)',
              [masterId, key.trim(), value.toString()],
              connection
            );
          }
        }
      }
    }
    
    await db.commitTransaction(connection);
    
    res.json(createSuccessResponse(null, '大师信息更新成功'));
  } catch (error) {
    await db.rollbackTransaction(connection);
    throw error;
  }
}));

/**
 * @route DELETE /api/masters/:id
 * @desc 删除大师信息
 * @access Private
 */
router.delete('/:id', authenticateToken, asyncHandler(async (req, res) => {
  const masterId = req.params.id;
  
  // 检查大师是否存在和权限
  const masters = await db.execute(
    'SELECT created_by FROM master_items WHERE id = ?',
    [masterId]
  );
  
  if (masters.length === 0) {
    throw new NotFoundError('大师信息不存在');
  }
  
  if (masters[0].created_by !== req.user.id && req.user.role !== 'admin') {
    throw new AuthorizationError('无权删除此大师信息');
  }
  
  // 开始事务
  const connection = await db.getConnection();
  await db.beginTransaction(connection);
  
  try {
    // 删除属性
    await db.execute(
      'DELETE FROM master_properties WHERE master_id = ?',
      [masterId],
      connection
    );
    
    // 删除标签关联
    await db.execute(
      'DELETE FROM master_item_tags WHERE item_id = ?',
      [masterId],
      connection
    );
    
    // 删除消息引用
    await db.execute(
      'DELETE FROM message_references WHERE reference_type = "master" AND reference_id = ?',
      [masterId],
      connection
    );
    
    // 删除大师信息
    await db.execute(
      'DELETE FROM master_items WHERE id = ?',
      [masterId],
      connection
    );
    
    await db.commitTransaction(connection);
    
    res.json(createSuccessResponse(null, '大师信息删除成功'));
  } catch (error) {
    await db.rollbackTransaction(connection);
    throw error;
  }
}));

// ==================== 标签管理 ====================

/**
 * @route GET /api/masters/tags
 * @desc 获取大师库标签列表
 * @access Private
 */
router.get('/tags', authenticateToken, asyncHandler(async (req, res) => {
  const [tags] = await db.execute(
    `SELECT mt.id, mt.name, mt.created_at,
            COUNT(mit.item_id) as usage_count
     FROM master_tags mt
     LEFT JOIN master_item_tags mit ON mt.id = mit.tag_id
     GROUP BY mt.id
     ORDER BY usage_count DESC, mt.name`
  );
  
  res.json(createSuccessResponse(tags));
}));

/**
 * @route GET /api/masters/stats
 * @desc 获取大师库统计信息
 * @access Private
 */
router.get('/stats', authenticateToken, asyncHandler(async (req, res) => {
  const userId = req.user.id;
  
  // 获取统计信息
  const [stats] = await db.execute(
    `SELECT 
       (SELECT COUNT(*) FROM master_items) as total_masters,
       (SELECT COUNT(*) FROM master_items WHERE created_by = ?) as my_masters,
       (SELECT COUNT(*) FROM master_categories) as total_categories,
       (SELECT COUNT(*) FROM master_tags) as total_tags`,
    [userId]
  );
  
  // 获取分类统计
  const [categoryStats] = await db.execute(
    `SELECT mc.name, COUNT(mi.id) as master_count
     FROM master_categories mc
     LEFT JOIN master_items mi ON mc.id = mi.category_id
     GROUP BY mc.id
     ORDER BY master_count DESC
     LIMIT 10`
  );
  
  // 获取最近创建的大师
  const [recentMasters] = await db.execute(
    `SELECT mi.id, mi.name, mi.created_at,
            mc.name as category_name,
            u.display_name as creator_name
     FROM master_items mi
     LEFT JOIN master_categories mc ON mi.category_id = mc.id
     LEFT JOIN users u ON mi.created_by = u.id
     ORDER BY mi.created_at DESC
     LIMIT 5`
  );
  
  res.json(createSuccessResponse({
    overview: stats[0],
    categoryStats,
    recentMasters
  }));
}));

module.exports = router;