/**
 * 数据库配置模块
 * 
 * 功能说明：
 * - 配置MySQL数据库连接
 * - 创建连接池
 * - 提供数据库操作方法
 * - 处理连接错误和重连
 */

const mysql = require('mysql2/promise');
require('dotenv').config();

// 数据库连接配置
const dbConfig = {
  host: process.env.DB_HOST || 'localhost',
  port: process.env.DB_PORT || 3306,
  user: process.env.DB_USER || 'root',
  password: process.env.DB_PASSWORD || '1qaz@wsX',
  database: process.env.DB_NAME || 'scaia',
  charset: process.env.DB_CHARSET || 'utf8mb4',
  
  // 连接池配置
  connectionLimit: parseInt(process.env.DB_CONNECTION_LIMIT) || 10,
  
  // 其他配置
  multipleStatements: false, // 禁用多语句查询以提高安全性
  dateStrings: true, // 将日期作为字符串返回
  
  // 时区配置
  timezone: '+08:00',
  
  // SSL配置 (生产环境建议启用)
  ssl: process.env.NODE_ENV === 'production' ? {
    rejectUnauthorized: false
  } : false
};

// 创建数据库连接池
const pool = mysql.createPool(dbConfig);

// 监听连接池事件
pool.on('connection', (connection) => {
  console.log(`📊 新的数据库连接已建立: ${connection.threadId}`);
});

pool.on('error', (err) => {
  console.error('❌ 数据库连接池错误:', err);
  
  // 处理常见的数据库错误
  if (err.code === 'PROTOCOL_CONNECTION_LOST') {
    console.log('🔄 数据库连接丢失，正在重新连接...');
  } else if (err.code === 'ER_CON_COUNT_ERROR') {
    console.log('⚠️  数据库连接数过多');
  } else if (err.code === 'ECONNREFUSED') {
    console.log('❌ 数据库连接被拒绝');
  }
});

/**
 * 执行SQL查询
 * @param {string} sql - SQL查询语句
 * @param {Array} params - 查询参数
 * @returns {Promise<Array>} 查询结果
 */
async function execute(sql, params = [], connection = null) {
  try {
    const target = connection || pool;
    const [rows] = await target.execute(sql, params);
    return rows;
  } catch (error) {
    console.error('❌ SQL执行错误:', {
      sql: sql,
      params: params,
      error: error.message
    });
    throw error;
  }
}

/**
 * 执行SQL查询 (返回详细结果)
 * @param {string} sql - SQL查询语句
 * @param {Array} params - 查询参数
 * @returns {Promise<Object>} 包含rows和fields的结果对象
 */
async function query(sql, params = []) {
  try {
    const [rows, fields] = await pool.execute(sql, params);
    return { rows, fields };
  } catch (error) {
    console.error('❌ SQL查询错误:', {
      sql: sql,
      params: params,
      error: error.message
    });
    throw error;
  }
}

/**
 * 开始事务
 * @returns {Promise<Connection>} 数据库连接对象
 */
async function beginTransaction(connection) {
  await connection.beginTransaction();
}

/**
 * 提交事务
 * @param {Connection} connection - 数据库连接对象
 */
async function commitTransaction(connection) {
  try {
    await connection.commit();
  } finally {
    connection.release();
  }
}

/**
 * 回滚事务
 * @param {Connection} connection - 数据库连接对象
 */
async function rollbackTransaction(connection) {
  try {
    await connection.rollback();
  } finally {
    connection.release();
  }
}

/**
 * 获取单个连接 (用于复杂操作)
 * @returns {Promise<Connection>} 数据库连接对象
 */
async function getConnection() {
  return await pool.getConnection();
}

/**
 * 测试数据库连接
 * @returns {Promise<boolean>} 连接是否成功
 */
async function testConnection() {
  try {
    await execute('SELECT 1 as test');
    console.log('✅ 数据库连接测试成功');
    return true;
  } catch (error) {
    console.error('❌ 数据库连接测试失败:', error.message);
    return false;
  }
}

/**
 * 关闭数据库连接池
 */
async function closePool() {
  try {
    await pool.end();
    console.log('📊 数据库连接池已关闭');
  } catch (error) {
    console.error('❌ 关闭数据库连接池时出错:', error.message);
  }
}

/**
 * 格式化SQL查询日志
 * @param {string} sql - SQL语句
 * @param {Array} params - 参数
 * @returns {string} 格式化后的SQL
 */
function formatSqlLog(sql, params = []) {
  let formattedSql = sql;
  params.forEach((param, index) => {
    const placeholder = '?';
    const value = typeof param === 'string' ? `'${param}'` : param;
    formattedSql = formattedSql.replace(placeholder, value);
  });
  return formattedSql;
}

/**
 * 构建分页查询
 * @param {string} baseSql - 基础SQL查询
 * @param {number} page - 页码 (从1开始)
 * @param {number} limit - 每页数量
 * @returns {Object} 包含SQL和参数的对象
 */
function buildPaginationQuery(baseSql, page = 1, limit = 10) {
  const offset = (page - 1) * limit;
  const sql = `${baseSql} LIMIT ? OFFSET ?`;
  return {
    sql,
    params: [limit, offset]
  };
}

/**
 * 构建搜索条件
 * @param {Object} searchParams - 搜索参数
 * @param {Array} allowedFields - 允许搜索的字段
 * @returns {Object} 包含WHERE子句和参数的对象
 */
function buildSearchConditions(searchParams, allowedFields = []) {
  const conditions = [];
  const params = [];
  
  Object.keys(searchParams).forEach(key => {
    if (allowedFields.includes(key) && searchParams[key] !== undefined && searchParams[key] !== '') {
      if (typeof searchParams[key] === 'string') {
        conditions.push(`${key} LIKE ?`);
        params.push(`%${searchParams[key]}%`);
      } else {
        conditions.push(`${key} = ?`);
        params.push(searchParams[key]);
      }
    }
  });
  
  const whereClause = conditions.length > 0 ? `WHERE ${conditions.join(' AND ')}` : '';
  
  return {
    whereClause,
    params
  };
}

// 导出数据库操作方法
module.exports = {
  pool,
  execute,
  query,
  beginTransaction,
  commitTransaction,
  rollbackTransaction,
  getConnection,
  testConnection,
  closePool,
  formatSqlLog,
  buildPaginationQuery,
  buildSearchConditions,

  // 关闭连接池
  close: async () => {
    console.log('⏳ 正在关闭数据库连接池...');
    await pool.end();
    console.log('✅ 数据库连接池已成功关闭');
  }
};

// 进程退出时关闭连接池
process.on('SIGINT', async () => {
  console.log('🔄 正在关闭数据库连接池...');
  await closePool();
  process.exit(0);
});

process.on('SIGTERM', async () => {
  console.log('🔄 正在关闭数据库连接池...');
  await closePool();
  process.exit(0);
});