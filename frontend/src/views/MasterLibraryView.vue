<template>
  <div class="master-library-view">
    <h1 class="page-title">大师库</h1>
    <p class="page-description">浏览和管理建筑、室内、景观、规划领域的大师及其代表理念</p>
    
    <div class="library-container">
      <div class="category-tabs">
        <button 
          v-for="category in categories" 
          :key="category.id"
          class="tab-btn" 
          :class="{ active: activeCategory === category.id }"
          @click="activeCategory = category.id"
        >
          {{ category.name }}
        </button>
      </div>
      
      <div class="action-bar">
        <button class="btn btn-primary" @click="showAddItemModal = true">
          <span class="btn-icon">+</span> 添加项目
        </button>
        <div class="search-box">
          <input 
            type="text" 
            v-model="searchQuery" 
            class="form-input" 
            placeholder="搜索大师库..."
          >
          <button class="search-btn" @click="searchLibrary">
            🔍
          </button>
        </div>
      </div>
      
      <div class="library-content">
        <div v-if="filteredItems.length === 0" class="empty-state">
          <div class="empty-icon">📚</div>
          <h3>当前分类为空</h3>
          <p>点击"添加项目"按钮添加您的第一个主库项目</p>
        </div>
        
        <div v-else class="library-grid">
          <div 
            v-for="item in filteredItems" 
            :key="item.id"
            class="library-item"
            @click="viewItemDetails(item)"
          >
            <div class="item-preview" :style="getPreviewStyle(item)">
              <div v-if="!item.avatar_url" class="no-image">{{ item.name.charAt(0) }}</div>
            </div>
            <div class="item-content">
              <h3 class="item-title">{{ item.name }}</h3>
              <div class="item-tags">
                <span 
                  v-for="(tag, tagIndex) in item.tags" 
                  :key="tagIndex"
                  class="tag"
                >
                  {{ tag.name }}
                </span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    
    <!-- 添加项目模态框 -->
    <div v-if="showAddItemModal" class="modal">
      <div class="modal-content">
        <div class="modal-header">
          <h3>添加大师</h3>
          <button class="close-btn" @click="showAddItemModal = false">×</button>
        </div>
        <div class="modal-body">
          <div class="form-group">
            <label class="form-label">分类</label>
            <select v-model="newItem.categoryId" class="form-input">
              <option v-for="category in categories.filter(c => c.id !== 'all')" :key="category.id" :value="category.id">
                {{ category.name }}
              </option>
            </select>
          </div>
          
          <div class="form-group">
            <label class="form-label">大师名称</label>
            <input type="text" v-model="newItem.name" class="form-input" placeholder="输入大师名称">
          </div>
          
          <div class="form-group">
            <label class="form-label">简介</label>
            <textarea v-model="newItem.description" class="form-input" rows="3" placeholder="添加大师简介及代表理念"></textarea>
          </div>
          
          <div class="form-group">
            <label class="form-label">图片URL</label>
            <input type="text" v-model="newItem.avatar_url" class="form-input" placeholder="输入大师照片URL（可选）">
          </div>
          
          <div class="form-group">
            <label class="form-label">标签</label>
            <input type="text" v-model="newItem.tagsString" class="form-input" placeholder="用逗号分隔多个标签">
          </div>
          
          <div class="form-group">
            <label class="form-label">属性</label>
            <div class="properties-container">
              <div v-for="(prop, index) in newItem.properties" :key="index" class="property-row">
                <input 
                  type="text" 
                  v-model="prop.key" 
                  class="form-input property-key" 
                  placeholder="属性名"
                >
                <input 
                  type="text" 
                  v-model="prop.value" 
                  class="form-input property-value" 
                  placeholder="属性值"
                >
                <button class="remove-btn" @click="removeProperty(index)">×</button>
              </div>
              <button class="btn btn-small" @click="addProperty">添加属性</button>
            </div>
          </div>
        </div>
        <div class="modal-footer">
          <button class="btn btn-secondary" @click="showAddItemModal = false">取消</button>
          <button class="btn btn-primary" @click="addLibraryItem">添加</button>
        </div>
      </div>
    </div>
    
    <!-- 项目详情模态框 -->
    <div v-if="showDetailsModal" class="modal">
      <div class="modal-content">
        <div class="modal-header">
          <h3>项目详情</h3>
          <button class="close-btn" @click="showDetailsModal = false">×</button>
        </div>
        <div class="modal-body">
          <div class="detail-header">
            <div class="detail-image" :style="getPreviewStyle(selectedItem)">
              <div v-if="!selectedItem.avatar_url" class="no-image large">{{ selectedItem.name?.charAt(0) }}</div>
            </div>
            <div class="detail-title-section">
              <h2 class="detail-title">{{ selectedItem.name }}</h2>
              <p class="detail-category">
                {{ getCategoryName(selectedItem.categoryId) }}
              </p>
            </div>
          </div>
          
          <div class="detail-section">
            <h4>简介</h4>
            <p>{{ selectedItem.description || '无简介' }}</p>
          </div>
          
          <div class="detail-section">
            <h4>标签</h4>
            <div class="detail-tags">
              <span 
                v-for="(tag, tagIndex) in selectedItem.tags" 
                :key="tagIndex"
                class="tag"
              >
                {{ tag.name }}
              </span>
              <span v-if="!selectedItem.tags || selectedItem.tags.length === 0" class="no-tags">无标签</span>
            </div>
          </div>
          
          <div class="detail-section">
            <h4>属性</h4>
            <div class="properties-list">
              <div v-if="!selectedItem.properties || selectedItem.properties.length === 0" class="no-properties">
                无属性
              </div>
              <div v-else v-for="([key, value], index) in Object.entries(selectedItem.properties)" :key="index" class="property-item">
                <span class="property-key">{{ key }}:</span>
                <span class="property-value">{{ value }}</span>
              </div>
            </div>
          </div>
          
          <div class="detail-section">
            <h4>使用方法</h4>
            <p>在聊天界面中，点击"引用大师观点"按钮即可引用此大师的观点</p>
          </div>
        </div>
        <div class="modal-footer">
          <button class="btn btn-danger" @click="deleteLibraryItem(selectedItem)">删除</button>
          <button class="btn btn-secondary" @click="showDetailsModal = false">关闭</button>
          <button class="btn btn-primary" @click="editLibraryItem(selectedItem)">编辑</button>
        </div>
      </div>
    </div>
    
    <!-- 编辑项目模态框 -->
    <div v-if="showEditModal" class="modal">
      <div class="modal-content">
        <div class="modal-header">
          <h3>编辑大师信息</h3>
          <button class="close-btn" @click="showEditModal = false">×</button>
        </div>
        <div class="modal-body">
          <div class="form-group">
            <label class="form-label">分类</label>
            <select v-model="editItem.categoryId" class="form-input">
              <option v-for="category in categories.filter(c => c.id !== 'all')" :key="category.id" :value="category.id">
                {{ category.name }}
              </option>
            </select>
          </div>
          
          <div class="form-group">
            <label class="form-label">大师名称</label>
            <input type="text" v-model="editItem.name" class="form-input">
          </div>
          
          <div class="form-group">
            <label class="form-label">简介</label>
            <textarea v-model="editItem.description" class="form-input" rows="3"></textarea>
          </div>
          
          <div class="form-group">
            <label class="form-label">照片URL</label>
            <input type="text" v-model="editItem.avatar_url" class="form-input">
          </div>
          
          <div class="form-group">
            <label class="form-label">标签</label>
            <input type="text" v-model="editItem.tagsString" class="form-input" placeholder="用逗号分隔多个标签">
          </div>
          
          <div class="form-group">
            <label class="form-label">属性</label>
            <div class="properties-container">
              <div v-for="(prop, index) in editItem.properties" :key="index" class="property-row">
                <input 
                  type="text" 
                  v-model="prop.key" 
                  class="form-input property-key" 
                  placeholder="属性名"
                >
                <input 
                  type="text" 
                  v-model="prop.value" 
                  class="form-input property-value" 
                  placeholder="属性值"
                >
                <button class="remove-btn" @click="removeEditProperty(index)">×</button>
              </div>
              <button class="btn btn-small" @click="addEditProperty">添加属性</button>
            </div>
          </div>
        </div>
        <div class="modal-footer">
          <button class="btn btn-secondary" @click="showEditModal = false">取消</button>
          <button class="btn btn-primary" @click="saveLibraryItemEdit">保存</button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, watch } from 'vue';
import { masterAPI } from '@/services/api';

// 分类数据
const categories = ref([
  { id: 'all', name: '全部' }
]);

// 数据状态
const libraryItems = ref([])
const loading = ref(false)
const error = ref('')

// 状态变量
const activeCategory = ref('all');
const searchQuery = ref('');
const showAddItemModal = ref(false);
const showDetailsModal = ref(false);
const showEditModal = ref(false);
const selectedItem = ref({});

// 加载分类
async function loadCategories() {
  try {
    const response = await masterAPI.getMasterCategories();
    if (response.success) {
      categories.value = [{ id: 'all', name: '全部' }, ...response.data];
    } else {
      console.error('加载分类失败:', response.message);
    }
  } catch (err) {
    console.error('加载分类失败:', err);
  }
}

// 加载数据函数
async function loadMasterItems(categoryId) {
  try {
    loading.value = true;
    const params = categoryId && categoryId !== 'all' ? { categoryId } : {};
    const response = await masterAPI.getMasterList(params);
    libraryItems.value = response.data || [];
  } catch (err) {
    console.error('加载大师库失败:', err);
    error.value = '加载大师库失败';
    libraryItems.value = []; // 出错时清空列表
  } finally {
    loading.value = false;
  }
}

// 新项目数据
const newItem = ref({
  categoryId: 'architecture',
  name: '',
  description: '',
  avatar_url: '',
  tagsString: '',
  properties: [{ key: '', value: '' }]
});

// 编辑项目数据
const editItem = ref({
  id: null,
  categoryId: '',
  name: '',
  description: '',
  avatar_url: '',
  tagsString: '',
  properties: []
});

// 计算属性：过滤后的项目
const filteredItems = computed(() => {
  let items = libraryItems.value;
  
  if (searchQuery.value) {
    const query = searchQuery.value.toLowerCase();
    items = items.filter(item => {
      return (
        item.name.toLowerCase().includes(query) ||
        (item.description && item.description.toLowerCase().includes(query)) ||
        (item.tags && item.tags.some(tag => tag.name.toLowerCase().includes(query)))
      );
    });
  }
  
  return items;
});

// 方法
function searchLibrary() {
  // 搜索已通过计算属性实现
}

function getPreviewStyle(item) {
  if (item.avatar_url) {
    return { backgroundImage: `url(${item.avatar_url})` };
  }
  return {};
}

function getCategoryName(categoryId) {
  const category = categories.value.find(c => c.id === categoryId);
  return category ? category.name : '';
}

function addProperty() {
  newItem.value.properties.push({ key: '', value: '' });
}

function removeProperty(index) {
  newItem.value.properties.splice(index, 1);
  if (newItem.value.properties.length === 0) {
    addProperty();
  }
}

function addEditProperty() {
  editItem.value.properties.push({ key: '', value: '' });
}

function removeEditProperty(index) {
  editItem.value.properties.splice(index, 1);
  if (editItem.value.properties.length === 0) {
    addEditProperty();
  }
}

async function addLibraryItem() {
  try {
    loading.value = true
    const newLibraryItem = {
      categoryId: String(newItem.value.categoryId),
      name: newItem.value.name,
      description: newItem.value.description,
      avatar_url: newItem.value.avatar_url,
      tags: newItem.value.tagsString ? newItem.value.tagsString.split(',').map(tag => tag.trim()) : [],
      properties: newItem.value.properties.reduce((acc, prop) => {
        if (prop.key.trim() !== '' && prop.value.trim() !== '') {
          acc[prop.key.trim()] = prop.value.trim();
        }
        return acc;
      }, {})
    };
    
    await masterAPI.createMaster(newLibraryItem)
    
    // 重新加载数据
    await loadMasterItems(activeCategory.value)
    
    showAddItemModal.value = false;
    
    // 重置表单
    newItem.value = {
      categoryId: activeCategory.value === 'all' ? categories.value[1].id : activeCategory.value,
      name: '',
      description: '',
      avatar_url: '',
      tagsString: '',
      properties: [{ key: '', value: '' }]
    };
    
    alert('项目添加成功！');
  } catch (err) {
    console.error('添加大师失败:', err)
    alert('添加失败: ' + err.message)
  } finally {
    loading.value = false
  }
}

function viewItemDetails(item) {
  selectedItem.value = item;
  showDetailsModal.value = true;
}

function editLibraryItem(item) {
  editItem.value = {
    id: item.id,
    categoryId: item.categoryId,
    name: item.name,
    description: item.description || '',
    avatar_url: item.avatar_url || '',
    tagsString: item.tags ? item.tags.map(t => t.name).join(', ') : '',
    properties: item.properties ? Object.entries(item.properties).map(([key, value]) => ({ key, value })) : []
  };
  
  if (editItem.value.properties.length === 0) {
    editItem.value.properties.push({ key: '', value: '' });
  }
  
  showDetailsModal.value = false;
  showEditModal.value = true;
}

async function saveLibraryItemEdit() {
  try {
    loading.value = true
    const updateData = {
      categoryId: editItem.value.categoryId,
      name: editItem.value.name,
      description: editItem.value.description,
      avatar_url: editItem.value.avatar_url,
      tags: editItem.value.tagsString.split(',').map(tag => tag.trim()).filter(tag => tag),
      properties: editItem.value.properties.reduce((acc, prop) => {
        if (prop.key.trim() !== '' && prop.value.trim() !== '') {
          acc[prop.key.trim()] = prop.value.trim();
        }
        return acc;
      }, {})
    };
    console.log('更新数据:', updateData);
    
    await masterAPI.updateMaster(editItem.value.id, updateData)
    
    // 重新加载数据
    await loadMasterItems(activeCategory.value)
    
    showEditModal.value = false;
    alert('项目更新成功！');
  } catch (err) {
    console.error('更新大师失败:', err)
    alert('更新失败: ' + err.message)
  } finally {
    loading.value = false
  }
}

async function deleteLibraryItem(item) {
  if (confirm(`确定要删除大师 "${item.name}" 吗？`)) {
    try {
      loading.value = true;
      await masterAPI.deleteMaster(item.id);
      await loadMasterItems(activeCategory.value);
      showDetailsModal.value = false;
      alert('项目删除成功！');
    } catch (err) {
      console.error('删除大师失败:', err)
      alert('删除失败: ' + err.message)
    } finally {
      loading.value = false
    }
  }
}
// 监听分类变化
watch(activeCategory, (newCategory) => {
  loadMasterItems(newCategory);
});

// 组件挂载时加载数据
onMounted(() => {
  loadCategories();
  loadMasterItems(activeCategory.value);
});
</script>

<style scoped>
.master-library-view {
  padding: 20px;
  height: 100%;
  /* 移除overflow-y: auto，避免与App.vue中的滚动冲突 */
}

.page-title {
  font-size: 2rem;
  margin-bottom: 0.5rem;
  color: var(--color-heading);
}

.page-description {
  color: var(--color-text-light);
  margin-bottom: 2rem;
}

.library-container {
  background-color: var(--color-background-soft);
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
}

.category-tabs {
  display: flex;
  gap: 10px;
  margin-bottom: 20px;
  overflow-x: auto;
  padding-bottom: 5px;
}

.tab-btn {
  padding: 8px 16px;
  border: none;
  border-radius: 4px;
  background-color: var(--color-background-mute);
  color: var(--color-text);
  cursor: pointer;
  white-space: nowrap;
  transition: background-color 0.2s, color 0.2s;
}

.tab-btn.active {
  background-color: var(--primary-color);
  color: white;
}

.action-bar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.search-box {
  display: flex;
  align-items: center;
  width: 300px;
}

.search-btn {
  background-color: var(--color-primary);
  color: white;
  border: none;
  border-radius: 0 4px 4px 0;
  padding: 8px 12px;
  cursor: pointer;
}

.library-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: 20px;
}

.library-item {
  background-color: white;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  cursor: pointer;
  transition: transform 0.2s, box-shadow 0.2s;
}

.library-item:hover {
  transform: translateY(-3px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.item-preview {
  height: 120px;
  background-color: var(--color-background-mute);
  background-size: cover;
  background-position: center;
  display: flex;
  align-items: center;
  justify-content: center;
}

.no-image {
  font-size: 2.5rem;
  color: var(--color-text-light);
  background-color: var(--color-background-soft);
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.no-image.large {
  font-size: 4rem;
  height: 200px;
}

.item-content {
  padding: 15px;
}

.item-title {
  font-size: 1.1rem;
  margin-bottom: 8px;
  color: var(--color-heading);
}

.item-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 5px;
}

.tag {
  background-color: var(--color-background-mute);
  color: var(--color-text);
  padding: 3px 8px;
  border-radius: 4px;
  font-size: 0.8rem;
}

/* 空状态 */
.empty-state {
  text-align: center;
  padding: 40px 20px;
  color: var(--color-text-light);
}

.empty-icon {
  font-size: 4rem;
  margin-bottom: 20px;
}

/* 模态框样式 */
.modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.modal-content {
  background-color: white;
  border-radius: 8px;
  width: 90%;
  max-width: 600px;
  max-height: 90vh;
  overflow-y: auto;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px 20px;
  border-bottom: 1px solid var(--color-border);
}

.modal-body {
  padding: 20px;
}

.modal-footer {
  padding: 15px 20px;
  border-top: 1px solid var(--color-border);
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}

.close-btn {
  background: none;
  border: none;
  font-size: 1.5rem;
  cursor: pointer;
  color: var(--color-text-light);
}

/* 详情模态框样式 */
.detail-header {
  display: flex;
  gap: 20px;
  margin-bottom: 20px;
}

.detail-image {
  width: 150px;
  height: 150px;
  background-color: var(--color-background-mute);
  background-size: cover;
  background-position: center;
  border-radius: 8px;
  flex-shrink: 0;
  display: flex;
  align-items: center;
  justify-content: center;
}

.detail-title-section {
  flex: 1;
}

.detail-title {
  margin-bottom: 5px;
  color: var(--color-heading);
}

.detail-category {
  color: var(--color-text-light);
  font-size: 0.9rem;
  margin-bottom: 10px;
}

.detail-section {
  margin-bottom: 20px;
}

.detail-section h4 {
  margin-bottom: 10px;
  color: var(--color-heading);
  font-size: 1.1rem;
}

.detail-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.properties-list {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: 10px;
}

.property-item {
  background-color: var(--color-background-soft);
  padding: 8px 12px;
  border-radius: 4px;
}

.property-key {
  font-weight: 500;
  margin-right: 5px;
}

.no-tags, .no-properties {
  color: var(--color-text-light);
  font-style: italic;
}

/* 表单样式 */
.form-group {
  margin-bottom: 15px;
}

.form-label {
  display: block;
  margin-bottom: 5px;
  font-weight: 500;
}

.properties-container {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.property-row {
  display: flex;
  gap: 10px;
  align-items: center;
}

.property-key {
  flex: 1;
}

.property-value {
  flex: 2;
}

.remove-btn {
  background: none;
  border: none;
  color: var(--color-text-light);
  font-size: 1.2rem;
  cursor: pointer;
  padding: 0 5px;
}

.btn-small {
  padding: 5px 10px;
  font-size: 0.9rem;
  align-self: flex-start;
}

.btn-danger {
  background-color: #dc3545;
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 4px;
  cursor: pointer;
  transition: background-color 0.2s;
}

.btn-danger:hover {
  background-color: #c82333;
}

/* 响应式调整 */
@media (max-width: 768px) {
  .action-bar {
    flex-direction: column;
    gap: 10px;
    align-items: stretch;
  }
  
  .search-box {
    width: 100%;
  }
  
  .detail-header {
    flex-direction: column;
    align-items: center;
    text-align: center;
  }
  
  .detail-image {
    width: 200px;
    height: 200px;
  }
  
  .properties-list {
    grid-template-columns: 1fr;
  }
}
</style>