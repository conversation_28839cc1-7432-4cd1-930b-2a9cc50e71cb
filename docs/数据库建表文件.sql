-- --------------------------------------------------------
-- 主机:                           192.168.110.91
-- 服务器版本:                        8.0.42-0ubuntu0.24.04.1 - (Ubuntu)
-- 服务器操作系统:                      Linux
-- HeidiSQL 版本:                  12.10.0.7000
-- --------------------------------------------------------

/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET NAMES utf8 */;
/*!50503 SET NAMES utf8mb4 */;
/*!40103 SET @OLD_TIME_ZONE=@@TIME_ZONE */;
/*!40103 SET TIME_ZONE='+00:00' */;
/*!40014 SET @OLD_FOREIGN_KEY_CHECKS=@@FOREIGN_KEY_CHECKS, FOREIGN_KEY_CHECKS=0 */;
/*!40101 SET @OLD_SQL_MODE=@@SQL_MODE, SQL_MODE='NO_AUTO_VALUE_ON_ZERO' */;
/*!40111 SET @OLD_SQL_NOTES=@@SQL_NOTES, SQL_NOTES=0 */;

-- 导出  表 scaia.ai_models 结构
CREATE TABLE IF NOT EXISTS `ai_models` (
  `id` varchar(50) COLLATE utf8mb4_unicode_ci NOT NULL,
  `name` varchar(100) COLLATE utf8mb4_unicode_ci NOT NULL,
  `type` varchar(20) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT 'chat 或 image',
  `provider` varchar(50) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'openai',
  `api_key` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `api_endpoint` text COLLATE utf8mb4_unicode_ci NOT NULL,
  `max_tokens` int DEFAULT '4096',
  `temperature` decimal(3,2) DEFAULT '0.70',
  `is_active` tinyint(1) NOT NULL DEFAULT '1',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_ai_models_type` (`type`),
  KEY `idx_ai_models_provider` (`provider`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='AI模型配置表';

-- 数据导出被取消选择。

-- 导出  表 scaia.app_features 结构
CREATE TABLE IF NOT EXISTS `app_features` (
  `id` int NOT NULL AUTO_INCREMENT,
  `icon` varchar(10) COLLATE utf8mb4_unicode_ci NOT NULL,
  `title` varchar(100) COLLATE utf8mb4_unicode_ci NOT NULL,
  `description` text COLLATE utf8mb4_unicode_ci NOT NULL,
  `link` varchar(200) COLLATE utf8mb4_unicode_ci NOT NULL,
  `sort_order` int NOT NULL DEFAULT '0',
  `is_active` tinyint(1) NOT NULL DEFAULT '1',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=8 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='应用功能特性表';

-- 数据导出被取消选择。

-- 导出  表 scaia.chat_sessions 结构
CREATE TABLE IF NOT EXISTS `chat_sessions` (
  `id` int NOT NULL AUTO_INCREMENT,
  `user_id` int NOT NULL,
  `title` varchar(200) COLLATE utf8mb4_unicode_ci NOT NULL,
  `ai_model` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `enable_web_search` tinyint(1) NOT NULL DEFAULT '0',
  `system_prompt` text COLLATE utf8mb4_unicode_ci,
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_chat_sessions_user_id` (`user_id`),
  KEY `idx_chat_sessions_ai_model` (`ai_model`),
  KEY `idx_chat_sessions_created_at` (`created_at`),
  CONSTRAINT `fk_chat_sessions_user_id` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='聊天会话表';

-- 数据导出被取消选择。

-- 导出  表 scaia.image_categories 结构
CREATE TABLE IF NOT EXISTS `image_categories` (
  `id` int NOT NULL AUTO_INCREMENT,
  `name` varchar(100) COLLATE utf8mb4_unicode_ci NOT NULL,
  `description` text COLLATE utf8mb4_unicode_ci,
  `created_by` int NOT NULL,
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_image_categories_name` (`name`),
  KEY `idx_image_categories_created_by` (`created_by`),
  CONSTRAINT `fk_image_categories_created_by` FOREIGN KEY (`created_by`) REFERENCES `users` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=8 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='图片库分类表';

-- 数据导出被取消选择。

-- 导出  表 scaia.image_items 结构
CREATE TABLE IF NOT EXISTS `image_items` (
  `id` int NOT NULL AUTO_INCREMENT,
  `title` varchar(200) COLLATE utf8mb4_unicode_ci NOT NULL,
  `description` text COLLATE utf8mb4_unicode_ci,
  `category_id` int DEFAULT NULL,
  `file_name` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `file_path` varchar(500) COLLATE utf8mb4_unicode_ci NOT NULL,
  `file_size` bigint NOT NULL,
  `file_type` varchar(50) COLLATE utf8mb4_unicode_ci NOT NULL,
  `width` int DEFAULT NULL,
  `height` int DEFAULT NULL,
  `is_public` tinyint(1) NOT NULL DEFAULT '0',
  `created_by` int NOT NULL,
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_image_items_category_id` (`category_id`),
  KEY `idx_image_items_created_by` (`created_by`),
  KEY `idx_image_items_is_public` (`is_public`),
  KEY `idx_image_items_created_at` (`created_at`),
  CONSTRAINT `fk_image_items_category_id` FOREIGN KEY (`category_id`) REFERENCES `image_categories` (`id`) ON DELETE SET NULL,
  CONSTRAINT `fk_image_items_created_by` FOREIGN KEY (`created_by`) REFERENCES `users` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=5 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='图片库项目表';

-- 数据导出被取消选择。

-- 导出  表 scaia.image_item_tags 结构
CREATE TABLE IF NOT EXISTS `image_item_tags` (
  `id` int NOT NULL AUTO_INCREMENT,
  `item_id` int NOT NULL,
  `tag_id` int NOT NULL,
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_image_item_tags` (`item_id`,`tag_id`),
  KEY `idx_image_item_tags_tag_id` (`tag_id`),
  CONSTRAINT `fk_image_item_tags_item_id` FOREIGN KEY (`item_id`) REFERENCES `image_items` (`id`) ON DELETE CASCADE,
  CONSTRAINT `fk_image_item_tags_tag_id` FOREIGN KEY (`tag_id`) REFERENCES `image_tags` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=25 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='图片库项目标签关联表';

-- 数据导出被取消选择。

-- 导出  表 scaia.image_prompts 结构
CREATE TABLE IF NOT EXISTS `image_prompts` (
  `id` int NOT NULL AUTO_INCREMENT,
  `image_id` int NOT NULL,
  `prompt_type` varchar(20) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT 'positive, negative',
  `prompt_text` text COLLATE utf8mb4_unicode_ci NOT NULL,
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_image_prompts_image_id` (`image_id`),
  KEY `idx_image_prompts_type` (`prompt_type`),
  CONSTRAINT `fk_image_prompts_image_id` FOREIGN KEY (`image_id`) REFERENCES `image_items` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=30 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='图片提示词表';

-- 数据导出被取消选择。

-- 导出  表 scaia.image_tags 结构
CREATE TABLE IF NOT EXISTS `image_tags` (
  `id` int NOT NULL AUTO_INCREMENT,
  `name` varchar(50) COLLATE utf8mb4_unicode_ci NOT NULL,
  `color` varchar(7) COLLATE utf8mb4_unicode_ci DEFAULT '#dc3545',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_image_tags_name` (`name`)
) ENGINE=InnoDB AUTO_INCREMENT=16 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='图片库标签表';

-- 数据导出被取消选择。

-- 导出  表 scaia.knowledge_categories 结构
CREATE TABLE IF NOT EXISTS `knowledge_categories` (
  `id` int NOT NULL AUTO_INCREMENT,
  `name` varchar(100) COLLATE utf8mb4_unicode_ci NOT NULL,
  `description` text COLLATE utf8mb4_unicode_ci,
  `created_by` int NOT NULL,
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_knowledge_categories_name` (`name`),
  KEY `idx_knowledge_categories_created_by` (`created_by`),
  CONSTRAINT `fk_knowledge_categories_created_by` FOREIGN KEY (`created_by`) REFERENCES `users` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=6 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='知识库分类表';

-- 数据导出被取消选择。

-- 导出  表 scaia.knowledge_items 结构
CREATE TABLE IF NOT EXISTS `knowledge_items` (
  `id` int NOT NULL AUTO_INCREMENT,
  `title` varchar(200) COLLATE utf8mb4_unicode_ci NOT NULL,
  `content` longtext COLLATE utf8mb4_unicode_ci,
  `category_id` int DEFAULT NULL,
  `file_name` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `file_path` varchar(500) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `file_size` bigint DEFAULT NULL,
  `file_type` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `is_public` tinyint(1) NOT NULL DEFAULT '0',
  `created_by` int NOT NULL,
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_knowledge_items_category_id` (`category_id`),
  KEY `idx_knowledge_items_created_by` (`created_by`),
  KEY `idx_knowledge_items_is_public` (`is_public`),
  KEY `idx_knowledge_items_created_at` (`created_at`),
  CONSTRAINT `fk_knowledge_items_category_id` FOREIGN KEY (`category_id`) REFERENCES `knowledge_categories` (`id`) ON DELETE SET NULL,
  CONSTRAINT `fk_knowledge_items_created_by` FOREIGN KEY (`created_by`) REFERENCES `users` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=5 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='知识库项目表';

-- 数据导出被取消选择。

-- 导出  表 scaia.knowledge_item_tags 结构
CREATE TABLE IF NOT EXISTS `knowledge_item_tags` (
  `id` int NOT NULL AUTO_INCREMENT,
  `item_id` int NOT NULL,
  `tag_id` int NOT NULL,
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_knowledge_item_tags` (`item_id`,`tag_id`),
  KEY `idx_knowledge_item_tags_tag_id` (`tag_id`),
  CONSTRAINT `fk_knowledge_item_tags_item_id` FOREIGN KEY (`item_id`) REFERENCES `knowledge_items` (`id`) ON DELETE CASCADE,
  CONSTRAINT `fk_knowledge_item_tags_tag_id` FOREIGN KEY (`tag_id`) REFERENCES `knowledge_tags` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=7 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='知识库项目标签关联表';

-- 数据导出被取消选择。

-- 导出  表 scaia.knowledge_tags 结构
CREATE TABLE IF NOT EXISTS `knowledge_tags` (
  `id` int NOT NULL AUTO_INCREMENT,
  `name` varchar(50) COLLATE utf8mb4_unicode_ci NOT NULL,
  `color` varchar(7) COLLATE utf8mb4_unicode_ci DEFAULT '#007bff',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_knowledge_tags_name` (`name`)
) ENGINE=InnoDB AUTO_INCREMENT=7 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='知识库标签表';

-- 数据导出被取消选择。

-- 导出  表 scaia.master_categories 结构
CREATE TABLE IF NOT EXISTS `master_categories` (
  `id` varchar(50) COLLATE utf8mb4_unicode_ci NOT NULL,
  `name` varchar(100) COLLATE utf8mb4_unicode_ci NOT NULL,
  `description` text COLLATE utf8mb4_unicode_ci,
  `created_by` int NOT NULL,
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_master_categories_name` (`name`),
  KEY `idx_master_categories_created_by` (`created_by`),
  CONSTRAINT `fk_master_categories_created_by` FOREIGN KEY (`created_by`) REFERENCES `users` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='大师库分类表';

-- 数据导出被取消选择。

-- 导出  表 scaia.master_items 结构
CREATE TABLE IF NOT EXISTS `master_items` (
  `id` int NOT NULL AUTO_INCREMENT,
  `name` varchar(100) COLLATE utf8mb4_unicode_ci NOT NULL,
  `description` text COLLATE utf8mb4_unicode_ci,
  `category_id` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `avatar_url` text COLLATE utf8mb4_unicode_ci,
  `system_prompt` longtext COLLATE utf8mb4_unicode_ci,
  `is_public` tinyint(1) NOT NULL DEFAULT '0',
  `created_by` int NOT NULL,
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_master_items_category_id` (`category_id`),
  KEY `idx_master_items_created_by` (`created_by`),
  KEY `idx_master_items_is_public` (`is_public`),
  KEY `idx_master_items_created_at` (`created_at`),
  CONSTRAINT `fk_master_items_category_id` FOREIGN KEY (`category_id`) REFERENCES `master_categories` (`id`) ON DELETE SET NULL,
  CONSTRAINT `fk_master_items_created_by` FOREIGN KEY (`created_by`) REFERENCES `users` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=5 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='大师库项目表';

-- 数据导出被取消选择。

-- 导出  表 scaia.master_item_tags 结构
CREATE TABLE IF NOT EXISTS `master_item_tags` (
  `id` int NOT NULL AUTO_INCREMENT,
  `item_id` int NOT NULL,
  `tag_id` int NOT NULL,
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_master_item_tags` (`item_id`,`tag_id`),
  KEY `idx_master_item_tags_tag_id` (`tag_id`),
  CONSTRAINT `fk_master_item_tags_item_id` FOREIGN KEY (`item_id`) REFERENCES `master_items` (`id`) ON DELETE CASCADE,
  CONSTRAINT `fk_master_item_tags_tag_id` FOREIGN KEY (`tag_id`) REFERENCES `master_tags` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=9 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='大师库项目标签关联表';

-- 数据导出被取消选择。

-- 导出  表 scaia.master_properties 结构
CREATE TABLE IF NOT EXISTS `master_properties` (
  `id` int NOT NULL AUTO_INCREMENT,
  `master_id` int NOT NULL,
  `property_key` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `property_value` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci,
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_master_attributes_master_id` (`master_id`),
  KEY `idx_master_attributes_key` (`property_key`) USING BTREE,
  CONSTRAINT `fk_master_attributes_master_id` FOREIGN KEY (`master_id`) REFERENCES `master_items` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='大师库属性表';

-- 数据导出被取消选择。

-- 导出  表 scaia.master_tags 结构
CREATE TABLE IF NOT EXISTS `master_tags` (
  `id` int NOT NULL AUTO_INCREMENT,
  `name` varchar(50) COLLATE utf8mb4_unicode_ci NOT NULL,
  `color` varchar(7) COLLATE utf8mb4_unicode_ci DEFAULT '#28a745',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_master_tags_name` (`name`)
) ENGINE=InnoDB AUTO_INCREMENT=7 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='大师库标签表';

-- 数据导出被取消选择。

-- 导出  表 scaia.messages 结构
CREATE TABLE IF NOT EXISTS `messages` (
  `id` int NOT NULL AUTO_INCREMENT,
  `session_id` int NOT NULL,
  `user_id` int NOT NULL,
  `content` longtext COLLATE utf8mb4_unicode_ci NOT NULL,
  `type` varchar(20) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'text' COMMENT 'text, image, file',
  `role` varchar(20) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT 'user, assistant, system',
  `token_count` int DEFAULT '0',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_messages_session_id` (`session_id`),
  KEY `idx_messages_user_id` (`user_id`),
  KEY `idx_messages_type` (`type`),
  KEY `idx_messages_role` (`role`),
  KEY `idx_messages_created_at` (`created_at`),
  CONSTRAINT `fk_messages_session_id` FOREIGN KEY (`session_id`) REFERENCES `chat_sessions` (`id`) ON DELETE CASCADE,
  CONSTRAINT `fk_messages_user_id` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='消息表';

-- 数据导出被取消选择。

-- 导出  表 scaia.message_references 结构
CREATE TABLE IF NOT EXISTS `message_references` (
  `id` int NOT NULL AUTO_INCREMENT,
  `message_id` int NOT NULL,
  `reference_type` varchar(20) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT 'knowledge, master, image, prompt, script',
  `reference_id` int NOT NULL,
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_message_references_message_id` (`message_id`),
  KEY `idx_message_references_type_id` (`reference_type`,`reference_id`),
  CONSTRAINT `fk_message_references_message_id` FOREIGN KEY (`message_id`) REFERENCES `messages` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='消息引用表';

-- 数据导出被取消选择。

-- 导出  表 scaia.model_usage 结构
CREATE TABLE IF NOT EXISTS `model_usage` (
  `id` int NOT NULL AUTO_INCREMENT,
  `model_id` varchar(50) COLLATE utf8mb4_unicode_ci NOT NULL,
  `user_id` int NOT NULL,
  `call_count` int NOT NULL DEFAULT '0',
  `token_count` int NOT NULL DEFAULT '0',
  `month` varchar(7) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '格式: YYYY-MM',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_model_usage_model_user_month` (`model_id`,`user_id`,`month`),
  KEY `idx_model_usage_user_id` (`user_id`),
  CONSTRAINT `fk_model_usage_user_id` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='模型使用统计表';

-- 数据导出被取消选择。

-- 导出  表 scaia.prompt_categories 结构
CREATE TABLE IF NOT EXISTS `prompt_categories` (
  `id` int NOT NULL AUTO_INCREMENT,
  `name` varchar(100) COLLATE utf8mb4_unicode_ci NOT NULL,
  `description` text COLLATE utf8mb4_unicode_ci,
  `list_order` int NOT NULL DEFAULT '0',
  `created_by` int NOT NULL,
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_prompt_categories_name` (`name`),
  KEY `idx_prompt_categories_created_by` (`created_by`),
  CONSTRAINT `fk_prompt_categories_created_by` FOREIGN KEY (`created_by`) REFERENCES `users` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=8 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='提示词库分类表';

-- 数据导出被取消选择。

-- 导出  表 scaia.prompt_items 结构
CREATE TABLE IF NOT EXISTS `prompt_items` (
  `id` int NOT NULL AUTO_INCREMENT,
  `title` varchar(200) COLLATE utf8mb4_unicode_ci NOT NULL,
  `content` longtext COLLATE utf8mb4_unicode_ci NOT NULL,
  `category_id` int DEFAULT NULL,
  `description` text COLLATE utf8mb4_unicode_ci,
  `usage_count` int NOT NULL DEFAULT '0',
  `is_public` tinyint(1) NOT NULL DEFAULT '0',
  `created_by` int NOT NULL,
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_prompt_items_category_id` (`category_id`),
  KEY `idx_prompt_items_created_by` (`created_by`),
  KEY `idx_prompt_items_is_public` (`is_public`),
  KEY `idx_prompt_items_usage_count` (`usage_count`),
  KEY `idx_prompt_items_created_at` (`created_at`),
  CONSTRAINT `fk_prompt_items_category_id` FOREIGN KEY (`category_id`) REFERENCES `prompt_categories` (`id`) ON DELETE SET NULL,
  CONSTRAINT `fk_prompt_items_created_by` FOREIGN KEY (`created_by`) REFERENCES `users` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=58 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='提示词库项目表';

-- 数据导出被取消选择。

-- 导出  表 scaia.scripts 结构
CREATE TABLE IF NOT EXISTS `scripts` (
  `id` int NOT NULL AUTO_INCREMENT,
  `title` varchar(200) COLLATE utf8mb4_unicode_ci NOT NULL,
  `content` longtext COLLATE utf8mb4_unicode_ci NOT NULL,
  `category_id` int DEFAULT NULL,
  `description` text COLLATE utf8mb4_unicode_ci,
  `usage_count` int NOT NULL DEFAULT '0',
  `is_public` tinyint(1) NOT NULL DEFAULT '0',
  `created_by` int NOT NULL,
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_scripts_category_id` (`category_id`),
  KEY `idx_scripts_created_by` (`created_by`),
  KEY `idx_scripts_is_public` (`is_public`),
  KEY `idx_scripts_usage_count` (`usage_count`),
  KEY `idx_scripts_created_at` (`created_at`),
  CONSTRAINT `fk_scripts_category_id` FOREIGN KEY (`category_id`) REFERENCES `script_categories` (`id`) ON DELETE SET NULL,
  CONSTRAINT `fk_scripts_created_by` FOREIGN KEY (`created_by`) REFERENCES `users` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=18 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='话术库项目表';

-- 数据导出被取消选择。

-- 导出  表 scaia.script_categories 结构
CREATE TABLE IF NOT EXISTS `script_categories` (
  `id` int NOT NULL AUTO_INCREMENT,
  `name` varchar(100) COLLATE utf8mb4_unicode_ci NOT NULL,
  `description` text COLLATE utf8mb4_unicode_ci,
  `created_by` int NOT NULL,
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_script_categories_name` (`name`),
  KEY `idx_script_categories_created_by` (`created_by`),
  CONSTRAINT `fk_script_categories_created_by` FOREIGN KEY (`created_by`) REFERENCES `users` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=8 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='话术库分类表';

-- 数据导出被取消选择。

-- 导出  表 scaia.script_item_tags 结构
CREATE TABLE IF NOT EXISTS `script_item_tags` (
  `id` int NOT NULL AUTO_INCREMENT,
  `item_id` int NOT NULL,
  `tag_id` int NOT NULL,
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `script_id` int NOT NULL DEFAULT (0),
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_script_item_tags` (`item_id`,`tag_id`),
  KEY `idx_script_item_tags_tag_id` (`tag_id`),
  CONSTRAINT `fk_script_item_tags_item_id` FOREIGN KEY (`item_id`) REFERENCES `scripts` (`id`) ON DELETE CASCADE,
  CONSTRAINT `fk_script_item_tags_tag_id` FOREIGN KEY (`tag_id`) REFERENCES `script_tags` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=11 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='话术库项目标签关联表';

-- 数据导出被取消选择。

-- 导出  表 scaia.script_parameters 结构
CREATE TABLE IF NOT EXISTS `script_parameters` (
  `id` int NOT NULL AUTO_INCREMENT,
  `script_id` int NOT NULL,
  `parameter_name` varchar(50) COLLATE utf8mb4_unicode_ci NOT NULL,
  `parameter_type` varchar(20) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'text' COMMENT 'text, number, select, textarea',
  `parameter_options` text COLLATE utf8mb4_unicode_ci COMMENT 'JSON格式的选项列表',
  `default_value` text COLLATE utf8mb4_unicode_ci,
  `is_required` tinyint(1) NOT NULL DEFAULT '0',
  `description` text COLLATE utf8mb4_unicode_ci,
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_script_parameters_script_id` (`script_id`),
  CONSTRAINT `fk_script_parameters_script_id` FOREIGN KEY (`script_id`) REFERENCES `scripts` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='话术参数表';

-- 数据导出被取消选择。

-- 导出  表 scaia.script_tags 结构
CREATE TABLE IF NOT EXISTS `script_tags` (
  `id` int NOT NULL AUTO_INCREMENT,
  `name` varchar(50) COLLATE utf8mb4_unicode_ci NOT NULL,
  `color` varchar(7) COLLATE utf8mb4_unicode_ci DEFAULT '#6f42c1',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_script_tags_name` (`name`)
) ENGINE=InnoDB AUTO_INCREMENT=10 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='话术库标签表';

-- 数据导出被取消选择。

-- 导出  表 scaia.users 结构
CREATE TABLE IF NOT EXISTS `users` (
  `id` int NOT NULL AUTO_INCREMENT,
  `username` varchar(50) COLLATE utf8mb4_unicode_ci NOT NULL,
  `password_hash` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '存储加密后的密码',
  `display_name` varchar(100) COLLATE utf8mb4_unicode_ci NOT NULL,
  `email` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `role` varchar(20) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'user' COMMENT 'admin 或 user',
  `status` varchar(20) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'active' COMMENT 'active, inactive, banned',
  `avatar_url` text COLLATE utf8mb4_unicode_ci,
  `last_login_at` datetime DEFAULT NULL,
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_users_username` (`username`),
  UNIQUE KEY `uk_users_email` (`email`),
  KEY `idx_users_role` (`role`),
  KEY `idx_users_status` (`status`)
) ENGINE=InnoDB AUTO_INCREMENT=15 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户表';

-- 数据导出被取消选择。

-- 导出  表 scaia.user_activity_logs 结构
CREATE TABLE IF NOT EXISTS `user_activity_logs` (
  `id` int NOT NULL AUTO_INCREMENT,
  `user_id` int NOT NULL,
  `resource_id` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT '',
  `resource_type` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `action_type` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `details` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci,
  `ip_address` varchar(45) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `user_agent` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci,
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_user_activity_logs_user_id` (`user_id`),
  KEY `idx_user_activity_logs_created_at` (`created_at`),
  KEY `idx_user_activity_logs_action` (`resource_type`) USING BTREE,
  CONSTRAINT `fk_user_activity_logs_user_id` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=1084 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户活动日志表';

-- 数据导出被取消选择。

-- 导出  表 scaia.user_settings 结构
CREATE TABLE IF NOT EXISTS `user_settings` (
  `id` int NOT NULL AUTO_INCREMENT,
  `user_id` int NOT NULL,
  `theme` varchar(20) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'light',
  `language` varchar(10) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'zh-CN',
  `font_size` int NOT NULL DEFAULT '14',
  `font_family` varchar(50) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'system',
  `primary_color` varchar(50) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'blue',
  `date_format` varchar(50) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'YYYY-MM-DD',
  `animations` int NOT NULL DEFAULT '1',
  `enable_notifications` tinyint(1) NOT NULL DEFAULT '1',
  `enable_sound` tinyint(1) NOT NULL DEFAULT '1',
  `auto_save` tinyint(1) NOT NULL DEFAULT '1',
  `show_line_numbers` tinyint(1) NOT NULL DEFAULT '1',
  `word_wrap` tinyint(1) NOT NULL DEFAULT '1',
  `tab_size` int NOT NULL DEFAULT '2',
  `enable_ai_suggestions` tinyint(1) NOT NULL DEFAULT '1',
  `default_ai_model` varchar(50) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'gpt-4',
  `max_tokens` int NOT NULL DEFAULT '2048',
  `temperature` decimal(3,2) NOT NULL DEFAULT '0.70',
  `enable_web_search` tinyint(1) NOT NULL DEFAULT '0',
  `desktop_notifications` int NOT NULL DEFAULT '1',
  `sound_notifications` int NOT NULL DEFAULT '1',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_user_settings_user_id` (`user_id`),
  CONSTRAINT `fk_user_settings_user_id` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=7 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户设置表';

-- 数据导出被取消选择。

/*!40103 SET TIME_ZONE=IFNULL(@OLD_TIME_ZONE, 'system') */;
/*!40101 SET SQL_MODE=IFNULL(@OLD_SQL_MODE, '') */;
/*!40014 SET FOREIGN_KEY_CHECKS=IFNULL(@OLD_FOREIGN_KEY_CHECKS, 1) */;
/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40111 SET SQL_NOTES=IFNULL(@OLD_SQL_NOTES, 1) */;
