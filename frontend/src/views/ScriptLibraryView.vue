<template>
  <div class="script-library-view">
    <h1 class="page-title">话述库</h1>
    <p class="page-description">管理您的常用问题模板，提高与AI聊天的效率</p>
    
    <div class="library-container">
      <div class="action-bar">
        <button class="btn btn-primary" @click="showAddScriptModal = true">
          <span class="btn-icon">+</span> 添加话述
        </button>
        <div class="search-box">
          <input 
            type="text" 
            v-model="searchQuery" 
            class="form-input" 
            placeholder="搜索话述库..."
          >
          <button class="search-btn" @click="searchScripts">
            🔍
          </button>
        </div>
      </div>
      
      <div class="category-tabs">
        <button 
          v-for="category in categories" 
          :key="category.id"
          class="tab-btn" 
          :class="{ active: activeCategory === category.id }"
          @click="activeCategory = category.id"
        >
          {{ category.name }}
        </button>
      </div>
      
      <div class="script-list">
        <div v-if="filteredScripts.length === 0" class="empty-state">
          <div class="empty-icon">📜</div>
          <h3>当前分类为空</h3>
          <p>点击"添加话述"按钮添加您的第一个常用问题模板</p>
        </div>
        
        <div v-else class="script-grid">
          <div 
            v-for="script in filteredScripts" 
            :key="script.id"
            class="script-item"
          >
            <div class="script-header">
              <div class="script-title-section">
                <h3 class="script-title">{{ script.title }}</h3>
                <div class="script-meta">
                  <span class="script-date">{{ formatDate(script.created_at) }}</span>
                  <span class="script-category">{{ getCategoryName(script.category_id) }}</span>
                </div>
              </div>
            </div>
            <p class="script-description">{{ script.description || '无描述' }}</p>
            <div class="script-tags">
              <span 
                v-for="(tag, tagIndex) in script.tags" 
                :key="tagIndex"
                class="tag"
              >
                {{ tag.name }}
              </span>
            </div>
            <div class="script-actions">
              <button class="action-btn" @click="runScript(script)" title="使用话述">
                ▶️
              </button>
              <button class="action-btn" @click="viewScriptDetails(script)" title="查看详情">
                👁️
              </button>
              <button class="action-btn" @click="editScript(script)" title="编辑">
                ✏️
              </button>
              <button class="action-btn" @click="deleteScript(script)" title="删除">
                🗑️
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
    
    <!-- 添加脚本模态框 -->
    <div v-if="showAddScriptModal" class="modal">
      <div class="modal-content">
        <div class="modal-header">
          <h3>添加话述</h3>
          <button class="close-btn" @click="showAddScriptModal = false">×</button>
        </div>
        <div class="modal-body">
          <div class="form-group">
            <label class="form-label">名称</label>
            <input type="text" v-model="newScript.title" class="form-input" placeholder="输入脚本名称">
          </div>
          
          <div class="form-group">
            <label class="form-label">分类</label>
            <select v-model="newScript.categoryId" class="form-input">
              <option value="" disabled>请选择分类</option>
              <option v-for="category in categories.filter(c => c.id !== 'all')" :key="category.id" :value="category.id">
                {{ category.name }}
              </option>
            </select>
          </div>
          
          <div class="form-group">
            <label class="form-label">描述</label>
            <textarea v-model="newScript.description" class="form-input" rows="3" placeholder="添加脚本描述"></textarea>
          </div>
          
          <div class="form-group">
            <label class="form-label">话述内容</label>
            <textarea v-model="newScript.content" class="form-input code-input" rows="10" placeholder="在此输入话述内容"></textarea>
          </div>
        </div>
        <div class="modal-footer">
          <button class="btn btn-secondary" @click="showAddScriptModal = false">取消</button>
          <button class="btn btn-primary" @click="addScript">添加</button>
        </div>
      </div>
    </div>
    
    <!-- 脚本详情模态框 -->
    <div v-if="showDetailsModal" class="modal">
      <div class="modal-content">
        <div class="modal-header">
          <h3>话述详情</h3>
          <button class="close-btn" @click="showDetailsModal = false">×</button>
        </div>
        <div class="modal-body">
          <div class="detail-header">
            <div class="detail-title-section">
              <h2 class="detail-title">{{ selectedScript.title }}</h2>
              <p class="detail-meta">
                <span class="detail-category">{{ getCategoryName(selectedScript.category_id) }}</span>
                <span class="detail-date">创建于 {{ formatDate(selectedScript.created_at) }}</span>
              </p>
            </div>
          </div>
          
          <div class="detail-section">
            <h4>描述</h4>
            <p>{{ selectedScript.description || '无描述' }}</p>
          </div>
          
          <div class="detail-section">
            <h4>话述内容</h4>
            <div class="code-preview">
              <pre><code>{{ selectedScript.content }}</code></pre>
            </div>
          </div>
        </div>
        <div class="modal-footer">
          <button class="btn btn-danger" @click="deleteScript(selectedScript)">删除</button>
          <button class="btn btn-secondary" @click="showDetailsModal = false">关闭</button>
          <button class="btn btn-primary" @click="editScript(selectedScript)">编辑</button>
          <button class="btn btn-primary" @click="runScript(selectedScript)">
            <span class="btn-icon">▶️</span> 使用
          </button>
        </div>
      </div>
    </div>
    
    <!-- 编辑脚本模态框 -->
    <div v-if="showEditModal" class="modal">
      <div class="modal-content">
        <div class="modal-header">
          <h3>编辑话述</h3>
          <button class="close-btn" @click="showEditModal = false">×</button>
        </div>
        <div class="modal-body">
          <div class="form-group">
            <label class="form-label">名称</label>
            <input type="text" v-model="editData.title" class="form-input">
          </div>
          
          <div class="form-group">
            <label class="form-label">分类</label>
            <select v-model="editData.categoryId" class="form-input">
              <option v-for="category in categories.filter(c => c.id !== 'all')" :key="category.id" :value="category.id">
                {{ category.name }}
              </option>
            </select>
          </div>
          
          <div class="form-group">
            <label class="form-label">描述</label>
            <textarea v-model="editData.description" class="form-input" rows="3"></textarea>
          </div>
          
          <div class="form-group">
            <label class="form-label">话述内容</label>
            <textarea v-model="editData.content" class="form-input code-input" rows="10"></textarea>
          </div>
        </div>
        <div class="modal-footer">
          <button class="btn btn-secondary" @click="showEditModal = false">取消</button>
          <button class="btn btn-primary" @click="saveScriptEdit">保存</button>
        </div>
      </div>
    </div>
    
    <!-- 运行脚本模态框 -->
    <div v-if="showRunModal" class="modal">
      <div class="modal-content">
        <div class="modal-header">
          <h3>使用话述: {{ selectedScript.title }}</h3>
          <button class="close-btn" @click="showRunModal = false">×</button>
        </div>
        <div class="modal-body">
          <div class="run-preview">
            <h4>使用预览</h4>
            <div class="run-preview-content">
              <p>话述将以以下内容插入聊天框:</p>
              <pre><code>{{ getRunPreview() }}</code></pre>
            </div>
          </div>
        </div>
        <div class="modal-footer">
          <button class="btn btn-secondary" @click="showRunModal = false">取消</button>
          <button class="btn btn-primary" @click="executeScript">使用</button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue';
import { scriptAPI } from '../services/api';
import { ElMessage } from 'element-plus';

// 状态变量
const activeCategory = ref('all');
const searchQuery = ref('');
const showAddScriptModal = ref(false);
const showDetailsModal = ref(false);
const showEditModal = ref(false);
const showRunModal = ref(false);
const selectedScript = ref({});
const runParams = ref({});
const loading = ref(false);

// 数据
const scripts = ref([]);
const categories = ref([{ id: 'all', name: '全部' }]);

// 新脚本数据
const newScript = ref({
  title: '',
  categoryId: '',
  description: '',
  content: '',
  isPublic: false,
  tags: []
});

// 编辑脚本数据
const editData = ref({
  id: null,
  title: '',
  categoryId: '',
  description: '',
  content: '',
  isPublic: false,
  tags: []
});

// 计算属性：过滤后的脚本
const filteredScripts = computed(() => {
  let result = activeCategory.value === 'all' 
    ? scripts.value 
    : scripts.value.filter(script => script.category_id === activeCategory.value);
  
  if (searchQuery.value) {
    const query = searchQuery.value.toLowerCase();
    result = result.filter(script => {
      return (
        script.title.toLowerCase().includes(query) ||
        (script.description && script.description.toLowerCase().includes(query)) ||
        (script.tags && script.tags.some(tag => tag.name.toLowerCase().includes(query)))
      );
    });
  }
  
  return result;
});

// 初始化数据
onMounted(async () => {
  await loadCategories();
  await loadScripts();
});

// 加载分类
async function loadCategories() {
  try {
    const response = await scriptAPI.getScriptCategories();
    if (response.success) {
      categories.value = [{ id: 'all', name: '全部' }, ...response.data];
    }
  } catch (error) {
    console.error('加载分类失败:', error);
    ElMessage.error(error.message || '加载分类失败，请刷新页面重试');
  }
}

// 加载脚本列表
async function loadScripts() {
  try {
    loading.value = true;
    const response = await scriptAPI.getScriptList({
      search: searchQuery.value,
      categoryId: activeCategory.value === 'all' ? undefined : activeCategory.value
    });
    if (response.success) {
      scripts.value = response.data;
    }
  } catch (error) {
    console.error('加载脚本失败:', error);
    ElMessage.error(error.message || '加载脚本失败，请刷新页面重试');
  } finally {
    loading.value = false;
  }
}

// 获取分类名称
function getCategoryName(categoryId) {
  const category = categories.value.find(cat => cat.id === categoryId);
  return category ? category.name : '未知分类';
}

// 方法
function searchScripts() {
  loadScripts();
}

function getScriptIcon(type) {
  const icons = {
    'customer': '👥',
    'opinion': '💡',
    'strategy': '📊',
    'prompt': '📝',
    'batch': '📦',
    'utility': '⚙️'
  };
  
  return icons[type] || '📜';
}

function formatDate(date) {
  return new Intl.DateTimeFormat('zh-CN', {
    year: 'numeric',
    month: 'long',
    day: 'numeric'
  }).format(new Date(date));
}

// 使用从scriptStore导入的getCategoryName函数，不需要在这里定义

async function addScript() {
  if (!newScript.value.title) {
    ElMessage.warning('请输入脚本名称');
    return;
  }
  
  if (!newScript.value.categoryId) {
    ElMessage.warning('请选择分类');
    return;
  }
  
  try {
    loading.value = true;
    const response = await scriptAPI.createScript({
      title: newScript.value.title,
      categoryId: newScript.value.categoryId.toString(),
      description: newScript.value.description,
      content: newScript.value.content,
      isPublic: newScript.value.isPublic,
    });
    
    if (response.success) {
      showAddScriptModal.value = false;
      
      // 重置表单
      newScript.value = {
        title: '',
        categoryId: '',
        description: '',
        content: '',
        isPublic: false,
        tags: []
      };
      
      // 重新加载脚本列表
      await loadScripts();
      ElMessage.success('脚本添加成功！');
    }
  } catch (error) {
    console.error('添加脚本失败:', error);
    ElMessage.error(error.message || '添加脚本失败，请重试');
  } finally {
    loading.value = false;
  }
}

function viewScriptDetails(script) {
  selectedScript.value = script;
  showDetailsModal.value = true;
}

function editScript(script) {
  editData.value = {
    id: script.id,
    title: script.title,
    categoryId: script.category_id,
    description: script.description || '',
    content: script.content || '',
    isPublic: script.is_public || false,
    tags: script.tags ? script.tags.map(tag => tag.name) : []
  };
  
  showDetailsModal.value = false;
  showEditModal.value = true;
}

async function saveScriptEdit() {
  if (!editData.value.title) {
    ElMessage.warning('请输入脚本名称');
    return;
  }
  
  if (!editData.value.categoryId) {
    ElMessage.warning('请选择分类');
    return;
  }
  
  try {
    loading.value = true;
    const response = await scriptAPI.updateScript(editData.value.id, {
      title: editData.value.title,
      categoryId: editData.value.categoryId,
      description: editData.value.description,
      content: editData.value.content,
      isPublic: editData.value.isPublic,
      tags: editData.value.tags
    });
    
    if (response.success) {
      showEditModal.value = false;
      // 重新加载脚本列表
      await loadScripts();
      ElMessage.success('脚本更新成功！');
    }
  } catch (error) {
    console.error('更新脚本失败:', error);
    ElMessage.error(error.message || '更新脚本失败，请重试');
  } finally {
    loading.value = false;
  }
}

async function deleteScript(script) {
  if (confirm(`确定要删除 "${script.title}" 吗？此操作不可撤销。`)) {
    try {
      loading.value = true;
      const response = await scriptAPI.deleteScript(script.id);
      
      if (response.success) {
        showDetailsModal.value = false;
        // 重新加载脚本列表
        await loadScripts();
        ElMessage.success('脚本已删除！');
      }
    } catch (error) {
      console.error('删除脚本失败:', error);
      ElMessage.error(error.message || '删除脚本失败，请重试');
    } finally {
      loading.value = false;
    }
  }
}

function runScript(script) {
  selectedScript.value = script;
  runParams.value = {};
  showDetailsModal.value = false;
  showRunModal.value = true;
}

function getRunPreview() {
  return selectedScript.value.content;
}

function executeScript() {
  // 这里可以添加将话述内容发送到聊天框的逻辑
  showRunModal.value = false;
  alert('话述内容已复制到剪贴板！');
}
</script>

<style scoped>
.script-library-view {
  padding: 20px;
  height: 100%;
  /* 移除overflow-y: auto，避免与App.vue中的滚动冲突 */
}

.page-title {
  font-size: 2rem;
  margin-bottom: 0.5rem;
  color: var(--color-heading);
}

.page-description {
  color: var(--color-text-light);
  margin-bottom: 2rem;
}

.library-container {
  background-color: var(--color-background-soft);
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
}

.action-bar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.search-box {
  display: flex;
  align-items: center;
  width: 300px;
}

.search-btn {
  background-color: var(--primary-color);
  color: white;
  border: none;
  border-radius: 0 4px 4px 0;
  padding: 8px 12px;
  cursor: pointer;
}

.category-tabs {
  display: flex;
  gap: 10px;
  margin-bottom: 20px;
  overflow-x: auto;
  padding-bottom: 5px;
}

.tab-btn {
  padding: 8px 16px;
  border: none;
  border-radius: 4px;
  background-color: var(--color-background-mute);
  color: var(--color-text);
  cursor: pointer;
  white-space: nowrap;
  transition: background-color 0.2s, color 0.2s;
}

.tab-btn.active {
  background-color: var(--primary-color);
  color: white;
}

.script-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 20px;
}

.script-item {
  background-color: white;
  border-radius: 8px;
  padding: 15px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  display: flex;
  flex-direction: column;
  transition: transform 0.2s, box-shadow 0.2s;
}

.script-item:hover {
  transform: translateY(-3px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.script-header {
  display: flex;
  gap: 10px;
  margin-bottom: 10px;
}

.script-icon {
  font-size: 2rem;
  color: var(--primary-color);
}

.script-title-section {
  flex: 1;
}

.script-title {
  font-size: 1.2rem;
  margin-bottom: 5px;
  color: var(--color-heading);
}

.script-meta {
  display: flex;
  gap: 10px;
  font-size: 0.8rem;
  color: var(--color-text-light);
}

.script-description {
  margin-bottom: 15px;
  font-size: 0.9rem;
  color: var(--color-text);
  flex: 1;
}

.script-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 5px;
  margin-bottom: 15px;
}

.tag {
  background-color: var(--color-background-mute);
  color: var(--color-text);
  padding: 3px 8px;
  border-radius: 4px;
  font-size: 0.8rem;
}

.script-actions {
  display: flex;
  justify-content: flex-end;
  gap: 5px;
}

.action-btn {
  background: none;
  border: none;
  font-size: 1.1rem;
  cursor: pointer;
  padding: 5px;
  border-radius: 4px;
  transition: background-color 0.2s;
}

.action-btn:hover {
  background-color: var(--color-background-mute);
}

/* 空状态 */
.empty-state {
  text-align: center;
  padding: 40px 20px;
  color: var(--color-text-light);
}

.empty-icon {
  font-size: 4rem;
  margin-bottom: 20px;
}

/* 模态框样式 */
.modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.modal-content {
  background-color: white;
  border-radius: 8px;
  width: 90%;
  max-width: 800px;
  max-height: 90vh;
  overflow-y: auto;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px 20px;
  border-bottom: 1px solid var(--color-border);
}

.modal-body {
  padding: 20px;
}

.modal-footer {
  padding: 15px 20px;
  border-top: 1px solid var(--color-border);
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}

.close-btn {
  background: none;
  border: none;
  font-size: 1.5rem;
  cursor: pointer;
  color: var(--color-text-light);
}

/* 详情模态框样式 */
.detail-header {
  display: flex;
  gap: 15px;
  margin-bottom: 20px;
}

.detail-icon {
  font-size: 3rem;
  color: var(--color-primary);
}

.detail-title-section {
  flex: 1;
}

.detail-title {
  margin-bottom: 5px;
  color: var(--color-heading);
}

.detail-meta {
  display: flex;
  gap: 15px;
  color: var(--color-text-light);
  font-size: 0.9rem;
  flex-wrap: wrap;
}

.detail-section {
  margin-bottom: 20px;
}

.detail-section h4 {
  margin-bottom: 10px;
  color: var(--color-heading);
  font-size: 1.1rem;
}

.detail-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.code-preview {
  background-color: var(--color-background-mute);
  padding: 15px;
  border-radius: 4px;
  overflow-x: auto;
  font-family: monospace;
  white-space: pre-wrap;
  max-height: 300px;
  overflow-y: auto;
}

.parameters-list {
  margin-top: 10px;
}

.parameters-table {
  border: 1px solid var(--color-border);
  border-radius: 4px;
  overflow: hidden;
}

.param-header {
  display: grid;
  grid-template-columns: 1fr 1fr 1fr;
  background-color: var(--color-background-mute);
  font-weight: 500;
}

.param-row {
  display: grid;
  grid-template-columns: 1fr 1fr 1fr;
  border-top: 1px solid var(--color-border);
}

.param-cell {
  padding: 8px 12px;
  border-right: 1px solid var(--color-border);
}

.param-cell:last-child {
  border-right: none;
}

.no-tags, .no-parameters {
  color: var(--color-text-light);
  font-style: italic;
}

/* 表单样式 */
.form-group {
  margin-bottom: 15px;
}

.form-label {
  display: block;
  margin-bottom: 5px;
  font-weight: 500;
}

.code-input {
  font-family: monospace;
}

.parameters-container {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.parameter-row {
  display: flex;
  gap: 10px;
  align-items: center;
}

.parameter-name {
  flex: 2;
}

.parameter-type {
  flex: 1;
}

.parameter-default {
  flex: 2;
}

.remove-btn {
  background: none;
  border: none;
  color: var(--color-text-light);
  font-size: 1.2rem;
  cursor: pointer;
  padding: 0 5px;
}

.btn-small {
  padding: 5px 10px;
  font-size: 0.9rem;
  align-self: flex-start;
}

.btn-danger {
  background-color: #dc3545;
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 4px;
  cursor: pointer;
  transition: background-color 0.2s;
}

.btn-danger:hover {
  background-color: #c82333;
}

/* 运行脚本模态框样式 */
.run-params-title {
  margin-bottom: 15px;
  font-size: 1.1rem;
  color: var(--color-heading);
}

.checkbox-group {
  display: flex;
  align-items: center;
  gap: 8px;
}

.no-params-message {
  color: var(--color-text-light);
  font-style: italic;
  margin-bottom: 20px;
}

.run-preview {
  margin-top: 20px;
  background-color: var(--color-background-soft);
  padding: 15px;
  border-radius: 4px;
}

.run-preview h4 {
  margin-bottom: 10px;
  color: var(--color-heading);
  font-size: 1.1rem;
}

.run-preview-content {
  background-color: var(--color-background-mute);
  padding: 15px;
  border-radius: 4px;
  margin-top: 10px;
}

.run-preview-content pre {
  margin: 0;
  font-family: monospace;
  white-space: pre-wrap;
}

/* 响应式调整 */
@media (max-width: 768px) {
  .action-bar {
    flex-direction: column;
    gap: 10px;
    align-items: stretch;
  }
  
  .search-box {
    width: 100%;
  }
  
  .script-grid {
    grid-template-columns: 1fr;
  }
  
  .detail-header {
    flex-direction: column;
    align-items: center;
    text-align: center;
  }
  
  .parameter-row {
    flex-direction: column;
    gap: 5px;
  }
  
  .parameter-name, .parameter-type, .parameter-default {
    width: 100%;
  }
  
  .param-header, .param-row {
    grid-template-columns: 1fr;
  }
  
  .param-cell {
    border-right: none;
    border-bottom: 1px solid var(--color-border);
  }
  
  .param-row:last-child .param-cell:last-child {
    border-bottom: none;
  }
}
</style>