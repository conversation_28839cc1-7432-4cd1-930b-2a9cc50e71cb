#!/usr/bin/env python3
"""
测试本地嵌入模型
"""

import sys
import os
sys.path.insert(0, '/opt/RAG-Anything')

try:
    from sentence_transformers import SentenceTransformer
    import numpy as np
    print("✅ sentence-transformers 导入成功")
except ImportError as e:
    print(f"❌ sentence-transformers 导入失败: {e}")
    sys.exit(1)

def test_embedding_model():
    """测试嵌入模型"""
    
    print("🔄 开始测试本地嵌入模型...")
    
    # 测试不同的模型
    models_to_test = [
        "BAAI/bge-small-zh-v1.5",  # 中文模型
        "all-MiniLM-L6-v2",        # 英文轻量模型
        "all-mpnet-base-v2"        # 英文高质量模型
    ]
    
    for model_name in models_to_test:
        print(f"\n📊 测试模型: {model_name}")
        
        try:
            # 加载模型
            model = SentenceTransformer(model_name)
            print(f"✅ 模型加载成功")
            
            # 测试文本
            test_texts = [
                "这是一个测试文本",
                "人工智能是未来的发展方向",
                "This is a test sentence",
                "Machine learning is transforming the world"
            ]
            
            # 生成嵌入
            embeddings = model.encode(test_texts)
            
            print(f"📏 嵌入维度: {embeddings.shape[1]}")
            print(f"📝 处理文本数量: {len(test_texts)}")
            print(f"🎯 嵌入形状: {embeddings.shape}")
            
            # 计算相似度
            from sklearn.metrics.pairwise import cosine_similarity
            similarity_matrix = cosine_similarity(embeddings)
            print(f"🔗 相似度矩阵形状: {similarity_matrix.shape}")
            
            # 显示一些相似度
            print("📊 文本相似度示例:")
            for i in range(len(test_texts)):
                for j in range(i+1, len(test_texts)):
                    similarity = similarity_matrix[i][j]
                    print(f"  '{test_texts[i][:20]}...' vs '{test_texts[j][:20]}...': {similarity:.3f}")
            
            print(f"✅ 模型 {model_name} 测试成功")
            
            # 只测试第一个成功的模型
            return model_name, embeddings.shape[1]
            
        except Exception as e:
            print(f"❌ 模型 {model_name} 测试失败: {e}")
            continue
    
    return None, None

def test_simple_rag():
    """测试简单的RAG功能"""
    
    print("\n🧠 测试简单RAG功能...")
    
    # 获取可用的模型
    model_name, embedding_dim = test_embedding_model()
    
    if not model_name:
        print("❌ 没有可用的嵌入模型")
        return False
    
    print(f"\n🎯 使用模型: {model_name} (维度: {embedding_dim})")
    
    try:
        # 加载模型
        model = SentenceTransformer(model_name)
        
        # 模拟知识库文档
        documents = [
            "人工智能（AI）是计算机科学的一个分支，致力于创建能够执行通常需要人类智能的任务的系统。",
            "机器学习是人工智能的一个子集，它使计算机能够在没有明确编程的情况下学习和改进。",
            "深度学习是机器学习的一个分支，使用神经网络来模拟人脑的工作方式。",
            "自然语言处理（NLP）是人工智能的一个领域，专注于计算机与人类语言之间的交互。",
            "计算机视觉是人工智能的一个领域，致力于让计算机能够理解和解释视觉信息。"
        ]
        
        # 生成文档嵌入
        doc_embeddings = model.encode(documents)
        print(f"📚 知识库文档数量: {len(documents)}")
        print(f"📊 文档嵌入形状: {doc_embeddings.shape}")
        
        # 测试查询
        queries = [
            "什么是人工智能？",
            "机器学习如何工作？",
            "深度学习和神经网络的关系",
            "NLP是什么？"
        ]
        
        from sklearn.metrics.pairwise import cosine_similarity
        
        for query in queries:
            print(f"\n🔍 查询: {query}")
            
            # 生成查询嵌入
            query_embedding = model.encode([query])
            
            # 计算相似度
            similarities = cosine_similarity(query_embedding, doc_embeddings)[0]
            
            # 找到最相似的文档
            best_match_idx = np.argmax(similarities)
            best_similarity = similarities[best_match_idx]
            
            print(f"📄 最佳匹配文档 (相似度: {best_similarity:.3f}):")
            print(f"   {documents[best_match_idx]}")
            
            # 显示前3个匹配
            top_indices = np.argsort(similarities)[::-1][:3]
            print("🏆 前3个匹配:")
            for i, idx in enumerate(top_indices):
                print(f"   {i+1}. (相似度: {similarities[idx]:.3f}) {documents[idx][:50]}...")
        
        print("\n✅ 简单RAG测试成功")
        return True
        
    except Exception as e:
        print(f"❌ 简单RAG测试失败: {e}")
        return False

if __name__ == "__main__":
    print("🚀 开始本地RAG测试...")
    
    # 测试嵌入模型
    success = test_simple_rag()
    
    if success:
        print("\n🎉 本地RAG测试完成！所有功能正常工作。")
        print("\n📋 测试结果:")
        print("✅ sentence-transformers 已安装")
        print("✅ 嵌入模型可以正常加载")
        print("✅ 文本向量化功能正常")
        print("✅ 相似度计算功能正常")
        print("✅ 简单RAG检索功能正常")
        print("\n🏠 本地RAG环境已准备就绪！")
    else:
        print("\n❌ 本地RAG测试失败，请检查环境配置。")
        sys.exit(1)
