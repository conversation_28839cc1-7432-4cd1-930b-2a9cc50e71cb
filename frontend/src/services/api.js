/**
 * API服务模块
 * 统一管理所有的后端API调用
 */

// 动态获取API基础URL
function getApiBaseUrl() {
  // 从环境变量获取
  if (import.meta.env.VITE_API_BASE_URL) {
    return import.meta.env.VITE_API_BASE_URL;
  }
  
  // 从localStorage获取用户设置的API地址
  const savedApiUrl = localStorage.getItem('apiBaseUrl');
  if (savedApiUrl) {
    return savedApiUrl;
  }
  
  // 根据当前域名动态设置
  const protocol = window.location.protocol;
  const hostname = window.location.hostname;
  
  // 如果是localhost或127.0.0.1，使用localhost:3003
  if (hostname === 'localhost' || hostname === '127.0.0.1') {
    return `${protocol}//${hostname}:3000/api`;
  }
  
  // 如果是其他IP地址，使用相同的IP但端口为3003
  return `${protocol}//${hostname}:3000/api`;
}

const API_BASE_URL = getApiBaseUrl();

// 设置API基础URL
export function setApiBaseUrl(url) {
  localStorage.setItem('apiBaseUrl', url);
  // 重新加载页面以应用新的API地址
  window.location.reload();
}

// 获取当前API基础URL
export function getCurrentApiBaseUrl() {
  return API_BASE_URL;
}

// 获取存储的token
function getToken() {
  return localStorage.getItem('accessToken');
}

// 设置token
function setToken(token) {
  localStorage.setItem('accessToken', token);
}

// 移除token
function removeToken() {
  localStorage.removeItem('accessToken');
}

// 通用请求函数
async function request(url, options = {}) {
  const token = getToken();
  
  // 添加调试信息
  console.log('🔍 API请求调试信息:', {
    url: `${API_BASE_URL}${url}`,
    method: options.method || 'GET',
    hasToken: !!token,
    tokenLength: token ? token.length : 0,
    headers: options.headers
  });
  
  const config = {
    headers: {
      ...(token && { 'Authorization': `Bearer ${token}` }),
      ...options.headers
    },
    ...options
  };

  // 只有在没有Content-Type且body不是FormData时才设置默认Content-Type
  if (!config.headers['Content-Type'] && !(config.body instanceof FormData)) {
    config.headers['Content-Type'] = 'application/json';
  }

  // 只有在body是对象且不是FormData时才JSON序列化
  if (config.body && typeof config.body === 'object' && !(config.body instanceof FormData)) {
    config.body = JSON.stringify(config.body);
  }

  try {
    const response = await fetch(`${API_BASE_URL}${url}`, config);
    
    if (!response.ok) {
      const errorData = await response.json().catch(() => ({ message: '未知错误', code: 'UNKNOWN_ERROR', status: 'error' }));
      console.error('❌ API请求失败:', {
        status: response.status,
        statusText: response.statusText,
        error: errorData
      });
      throw { success: false, message: errorData.message || `HTTP ${response.status}`, code: errorData.code, status: errorData.status };
    }

    let data;
    try {
      data = await response.json();
    } catch (jsonError) {
      console.error('Failed to parse JSON response:', jsonError);
      throw new Error('服务器返回了无效的响应格式');
    }
    
    if (typeof data !== 'object' || data === null) {
      console.error('API response returned non-object data:', data);
      throw new Error('服务器返回了非预期的响应数据类型');
    }

    return { success: true, message: '请求成功', data: data.data || data };
  } catch (error) {
    console.error('API请求错误:', error);
    // 确保抛出的是一个包含 success: false 和 message 的结构化错误对象
    if (error && typeof error === 'object' && error.success === false) {
      throw error;
    } else if (error && typeof error === 'object' && error.message) {
      throw { success: false, message: error.message, code: 'CLIENT_ERROR', status: 'error' };
    } else {
      throw { success: false, message: '未知错误', code: 'UNKNOWN_ERROR', status: 'error' };
    }
  }
}

// 认证相关API
export const authAPI = {
  // 用户登录
  async login(username, password) {
    const response = await request('/auth/login', {
      method: 'POST',
      body: { username, password }
    });
    
    // 保存token - 修复：从response.data中获取tokens
    if (response.data && response.data.tokens && response.data.tokens.accessToken) {
      setToken(response.data.tokens.accessToken);
    }
    return response.data;
  },

  // 用户注册
  async register(username, password, displayName) {
    const response = await request('/auth/register', {
      method: 'POST',
      body: { username, password, displayName }
    });
    
    // 保存token - 修复：从response.data中获取tokens
    if (response.data && response.data.tokens && response.data.tokens.accessToken) {
      setToken(response.data.tokens.accessToken);
    }
    
    return response.data;
  },

  // 用户登出
  async logout() {
    try {
      await request('/auth/logout', {
        method: 'POST'
      });
    } finally {
      removeToken();
    }
  },

  // 获取当前用户信息
  async getCurrentUser() {
    return await request('/auth/me');
  },

  // 验证token
  async verifyToken() {
    return await request('/auth/verify');
  },

  // 刷新token
  async refreshToken(refreshToken) {
    const response = await request('/auth/refresh', {
      method: 'POST',
      body: { refreshToken }
    });
    
    if (response.data && response.data.accessToken) {
      setToken(response.data.accessToken);
    }
    
    return response.data;
  }
};

// 用户管理相关API
export const userAPI = {
  // 获取用户个人资料
  async getProfile() {
    return await request('/users/profile');
  },

  // 更新用户个人资料
  async updateProfile(profileData) {
    return await request('/users/profile', {
      method: 'PUT',
      body: profileData
    });
  },

  // 上传头像
  async uploadAvatar(file) {
    const formData = new FormData();
    formData.append('avatar', file);
    
    return await request('/users/avatar', {
      method: 'POST',
      body: formData
    });
  },

  // 获取用户设置
  async getSettings() {
    return await request('/users/settings');
  },

  // 更新用户设置
  async updateSettings(settings) {
    return await request('/users/settings', {
      method: 'PUT',
      body: settings
    });
  },

  // 获取用户列表（管理员功能）
  async getUserList(params = {}) {
    const queryString = new URLSearchParams({
      page: 1,
      limit: 20,
      sortBy: 'created_at',
      sortOrder: 'DESC',
      ...params
    }).toString();
    
    return await request(`/users?${queryString}`);
  },

  // 获取指定用户信息（管理员功能）
  async getUserById(userId) {
    return await request(`/users/${userId}`);
  },

  // (管理员)创建新用户
  async createUser(userData) {
    return await request('/users', {
      method: 'POST',
      body: userData
    });
  },

  // (管理员)更新用户信息
  async updateUser(userId, userData) {
    return await request(`/users/${userId}`, {
      method: 'PUT',
      body: userData
    });
  },

  // (管理员)删除用户
  async deleteUser(userId) {
    return await request(`/users/${userId}`, {
      method: 'DELETE'
    });
  },

  // 更新用户状态（管理员功能）
  async updateUserStatus(userId, status) {
    return await request(`/users/${userId}/status`, {
      method: 'PUT',
      body: { status }
    });
  }
};

// 模型管理相关API
export const modelsAPI = {
  // 获取可用AI模型列表 (for users)
  async getAvailableModels(params = {}) {
    const queryString = new URLSearchParams(params).toString();
    return await request(`/models?${queryString}`);
  },

  // 获取所有AI模型列表（包括禁用的, for admins）
  async getAllModels() {
    return await request('/models/all');
  },

  // 获取单个AI模型详情
  async getModelById(id) {
    return await request(`/models/${id}`);
  },

  // 创建AI模型
  async createModel(data) {
    return await request('/models', {
      method: 'POST',
      body: data
    });
  },

  // 更新AI模型
  async updateModel(id, data) {
    return await request(`/models/${id}`, {
      method: 'PUT',
      body: data
    });
  },

  // 删除AI模型
  async deleteModel(id) {
    return await request(`/models/${id}`, {
      method: 'DELETE'
    });
  },

  // 切换AI模型状态
  async toggleModelStatus(id) {
    return await request(`/models/${id}/toggle`, {
      method: 'PUT'
    });
  }
};

// 检查用户是否已登录
export function isLoggedIn() {
  return !!getToken();
}

// 本地用户缓存
let currentUser = null;

// 从缓存中获取当前用户
export function getCurrentUserFromCache() {
  if (currentUser) {
    return currentUser;
  }
  
  const userJson = localStorage.getItem('currentUser');
  if (userJson) {
    currentUser = JSON.parse(userJson);
    return currentUser;
  }
  
  return null;
}

// 将当前用户存入缓存
export function setCurrentUserToCache(user) {
  currentUser = user;
  localStorage.setItem('currentUser', JSON.stringify(user));
}

// 清除当前用户缓存
export function clearCurrentUserCache() {
  currentUser = null;
  localStorage.removeItem('currentUser');
}

// 聊天相关API
export const chatAPI = {
  // 发送聊天消息
  async sendMessage(data) {
    return await request('/chat/send', {
      method: 'POST',
      body: data
    });
  },

  // 获取聊天历史
  async getChatHistory(params = {}) {
    const queryString = new URLSearchParams(params).toString();
    return await request(`/chat/history?${queryString}`);
  },

  // 创建新聊天会话
  async createChat(data) {
    return await request('/chat/sessions', {
      method: 'POST',
      body: data
    });
  },

  // 更新聊天会话
  async updateChat(sessionId, data) {
    return await request(`/chat/sessions/${sessionId}`, {
      method: 'PUT',
      body: data
    });
  },

  // 删除聊天会话
  async deleteChat(sessionId) {
    return await request(`/chat/sessions/${sessionId}`, {
      method: 'DELETE'
    });
  }
};

// 设置相关API
export const settingsAPI = {
  // 获取提示词分类
  async getPromptCategories() {
    return await request('/settings/prompt-categories');
  },

  // 添加提示词分类
  async addPromptCategory(data) {
    return await request('/settings/prompt-categories', {
      method: 'POST',
      body: data
    });
  },

  // 更新提示词分类
  async updatePromptCategory(id, data) {
    return await request(`/settings/prompt-categories/${id}`, {
      method: 'PUT',
      body: data
    });
  },

  // 删除提示词分类
  async deletePromptCategory(id) {
    return await request(`/settings/prompt-categories/${id}`, {
      method: 'DELETE'
    });
  },

  // 更新提示词分类排序
  async updatePromptCategoryOrder(data) {
    return await request('/settings/prompt-categories/order', {
      method: 'PUT',
      body: data
    });
  }
};

// 知识库相关API
export const knowledgeAPI = {
  // 获取知识库列表
  async getKnowledgeList(params = {}) {
    const queryString = new URLSearchParams({
      page: 1,
      limit: 50,
      sortBy: 'created_at',
      sortOrder: 'DESC',
      ...params
    }).toString();
    
    return await request(`/knowledge/items?${queryString}`);
  },

  // 获取知识库详情
  async getKnowledgeById(id) {
    return await request(`/knowledge/items/${id}`);
  },

  // 创建知识库项目
  async createKnowledge(formData) {
    return await request('/knowledge/items', {
      method: 'POST',
      body: formData
    });
  },

  // 更新知识库项目
  async updateKnowledge(id, data) {
    return await request(`/knowledge/items/${id}`, {
      method: 'PUT',
      body: data
    });
  },

  // 删除知识库项目
  async deleteKnowledge(id) {
    return await request(`/knowledge/items/${id}`, {
      method: 'DELETE'
    });
  },
};

// 大师库相关API
export const masterAPI = {
  // 获取大师库列表
  async getMasterList(params = {}) {
    const queryString = new URLSearchParams(params).toString();
    return await request(`/masters?${queryString}`);
  },

  // 获取大师库分类
  async getMasterCategories() {
    return await request('/masters/categories');
  },

  // 创建大师库分类
  async createMasterCategory(data) {
    return await request('/masters/categories', {
      method: 'POST',
      body: data
    });
  },

  // 更新大师库分类
  async updateMasterCategory(id, data) {
    return await request(`/masters/categories/${id}`, {
      method: 'PUT',
      body: data
    });
  },

  // 删除大师库分类
  async deleteMasterCategory(id) {
    return await request(`/masters/categories/${id}`, {
      method: 'DELETE'
    });
  },

  // 获取大师详情
  async getMasterById(id) {
    return await request(`/masters/${id}`);
  },

  // 创建大师项目
  async createMaster(data) {
    return await request('/masters', {
      method: 'POST',
      body: data
    });
  },

  // 更新大师项目
  async updateMaster(id, data) {
    return await request(`/masters/${id}`, {
      method: 'PUT',
      body: data
    });
  },

  // 删除大师项目
  async deleteMaster(id) {
    return await request(`/masters/${id}`, {
      method: 'DELETE'
    });
  }
};

// 话术库相关API
export const scriptAPI = {
  // 获取话术库列表
  async getScriptList(params = {}) {
    const queryString = new URLSearchParams({
      page: 1,
      limit: 50,
      sortBy: 'created_at',
      sortOrder: 'DESC',
      ...params
    }).toString();
    
    return await request(`/scripts?${queryString}`);
  },

  // 获取话术库分类
  async getScriptCategories() {
    return await request('/scripts/categories');
  },

  // 获取话术详情
  async getScriptById(id) {
    return await request(`/scripts/${id}`);
  },

  // 创建话术项目
  async createScript(data) {
    return await request('/scripts', {
      method: 'POST',
      body: data
    });
  },

  // 更新话术项目
  async updateScript(id, data) {
    return await request(`/scripts/${id}`, {
      method: 'PUT',
      body: data
    });
  },

  // 删除话术项目
  async deleteScript(id) {
    return await request(`/scripts/${id}`, {
      method: 'DELETE'
    });
  }
};

// 图片库相关API
export const imageAPI = {
  // 获取图片列表
  async getImageList(params = {}) {
    return await request('/images', { params });
  },

  // 获取图片分类
  async getImageCategories() {
    return await request('/images/categories');
  },

  // 获取单个图片
  async getImageById(id) {
    return await request(`/images/${id}`);
  },

  // 创建图片
  async createImage(data) {
    return await request('/images', {
      method: 'POST',
      body: data
    });
  },

  // 更新图片
  async updateImage(id, data) {
    return await request(`/images/${id}`, {
      method: 'PUT',
      body: data
    });
  },

  // 删除图片
  async deleteImage(id) {
    return await request(`/images/${id}`, {
      method: 'DELETE'
    });
  },

  // 上传图片
  async uploadImage(file, metadata) {
    const formData = new FormData();
    formData.append('file', file);
    if (metadata) {
      formData.append('metadata', JSON.stringify(metadata));
    }
    
    return await request('/images/upload', {
      method: 'POST',
      body: formData,
      headers: {} // 让浏览器自动设置Content-Type
    });
  },

  // 生图接口
  async generateImage(data) {
    return await request('/images/generate', {
      method: 'POST',
      body: data
    });
  }
};

// 提示词库相关API
export async function fetchPrompts(categoryId) {
  // 修正为 categoryId 参数
  const params = new URLSearchParams({ categoryId, page: 1, limit: 100, sortBy: 'created_at', sortOrder: 'DESC' });
  return await request(`/prompts?${params.toString()}`);
}

export async function fetchPromptCategories() {
  return await request('/prompts/categories');
}

// 提示词库相关API
export const promptAPI = {
  // 创建提示词
  async createPrompt(data) {
    return await request('/prompts', {
      method: 'POST',
      body: data
    });
  },

  // 更新提示词
  async updatePrompt(id, data) {
    return await request(`/prompts/${id}`, {
      method: 'PUT',
      body: data
    });
  },

  // 删除提示词
  async deletePrompt(id) {
    return await request(`/prompts/${id}`, {
      method: 'DELETE'
    });
  }
};

// 知识库向量化相关API
export const vectorizeKnowledgeItem = async (itemId) => {
  const response = await fetch(`${API_BASE_URL}/knowledge/items/${itemId}/vectorize`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${getToken()}`
    }
  });
  return handleResponse(response);
};

export const getVectorizationProgress = async (itemId) => {
  const response = await fetch(`${API_BASE_URL}/knowledge/items/${itemId}/vectorization-progress`, {
    headers: {
      'Authorization': `Bearer ${getToken()}`
    }
  });
  return handleResponse(response);
};

export const vectorSearch = async (query, limit = 5) => {
  const response = await fetch(`${API_BASE_URL}/knowledge/vector-search`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${getToken()}`
    },
    body: JSON.stringify({ query, limit })
  });
  return handleResponse(response);
};

// 聊天记录相关API
export const getChatSessions = async () => {
  return await request('/chat/sessions');
};

export const getChatSessionMessages = async (sessionId) => {
  return await request(`/chat/sessions/${sessionId}/messages`);
};

export const createChatSession = async (sessionData) => {
  return await request('/chat/sessions', {
    method: 'POST',
    body: sessionData
  });
};

export const renameChatSession = async (sessionId, newTitle) => {
  return await request(`/chat/sessions/${sessionId}`, {
    method: 'PUT',
    body: { title: newTitle }
  });
};

export const deleteChatSession = async (sessionId) => {
  return await request(`/chat/sessions/${sessionId}`, {
    method: 'DELETE'
  });
};

export const getChatMessages = async (sessionId) => {
  console.warn("getChatMessages is deprecated, use getChatSessionMessages instead.");
  return getChatSessionMessages(sessionId);
};

export const sendChatMessage = async (messageData) => {
  return request('/chat/send', {
    method: 'POST',
    body: messageData,
  });
};

export const updateMessageFeedback = async (messageId, feedback) => {
  return request(`/chat/messages/${messageId}/feedback`, {
    method: 'POST',
    body: { feedback },
  });
};

// 模型相关API
export const getModels = async (params = {}) => {
  return await request('/models', { params });
};

// 知识库相关API
export const getKnowledgeItems = async (params = {}) => {
  return await request('/knowledge/items', { params });
};

export const createKnowledgeItem = async (formData) => {
  return await request('/knowledge/items', {
    method: 'POST',
    body: formData,
    headers: {} // 让浏览器自动设置Content-Type为multipart/form-data
  });
};

export const updateKnowledgeItem = async (id, data) => {
  return await request(`/knowledge/items/${id}`, {
    method: 'PUT',
    body: data
  });
};

export const deleteKnowledgeItem = async (id) => {
  return await request(`/knowledge/items/${id}`, {
    method: 'DELETE'
  });
};

export const uploadKnowledgeFile = async (formData) => {
  return await request('/knowledge/upload', {
    method: 'POST',
    body: formData,
    headers: {} // 让浏览器自动设置Content-Type为multipart/form-data
  });
};

// 处理响应的辅助函数
function handleResponse(response) {
  if (!response.ok) {
    return response.json().then(data => {
      throw { success: false, message: data.message || `HTTP ${response.status}`, status: 'error' };
    });
  }
  return response.json().then(data => ({
    success: true,
    data: data.data || data,
    message: data.message || '请求成功'
  }));
}

/**
 * SSE流式发送消息，实时接收AI回复
 */
export const sendChatMessageStream = (messageData, onDelta, onSessionCreated, onDone, onError, onReasoning) => {
  const token = getToken();
  if (!token) {
    onError && onError('未找到认证token');
    return;
  }
  const apiUrl = `${getApiBaseUrl()}/chat/stream`;
  let retryCount = 0;
  const MAX_RETRIES = 3;
  const RETRY_DELAY = 1000;
  function connect() {
    const xhr = new XMLHttpRequest();
    xhr.open('POST', apiUrl, true);
    xhr.setRequestHeader('Content-Type', 'application/json');
    xhr.setRequestHeader('Authorization', `Bearer ${token}`);
    xhr.setRequestHeader('Accept', 'text/event-stream');
    xhr.setRequestHeader('Cache-Control', 'no-cache');
    let buffer = '';
    let lastEventTime = Date.now();
    let heartbeatTimer = null;
    let reconnectTimer = null;
    const TIMEOUT = 60000; // 增加到60秒超时
    const timeoutTimer = setTimeout(() => {
      xhr.abort();
      cleanup();
      if (retryCount < MAX_RETRIES) {
        retryCount++;
        reconnectTimer = setTimeout(connect, RETRY_DELAY);
      } else {
        onError && onError('AI服务繁忙，请稍后重试');
      }
    }, TIMEOUT);
    heartbeatTimer = setInterval(() => {
      const now = Date.now();
      if (now - lastEventTime > 4000) {
        xhr.abort();
        cleanup();
        if (retryCount < MAX_RETRIES) {
          retryCount++;
          reconnectTimer = setTimeout(connect, RETRY_DELAY);
        } else {
          onError && onError('AI服务繁忙，请稍后重试');
        }
      }
    }, 4000);
    function cleanup() {
      clearTimeout(timeoutTimer);
      clearInterval(heartbeatTimer);
      clearTimeout(reconnectTimer);
    }
    xhr.onreadystatechange = function() {
      console.log('XHR状态变化:', xhr.readyState, 'HTTP状态:', xhr.status);
      if (xhr.readyState >= 3) {
        lastEventTime = Date.now();
        const newData = xhr.responseText.substring(buffer.length);
        buffer = xhr.responseText;
        console.log('收到新数据:', newData.length, '字符');
        if (newData) {
          const lines = newData.split('\n');
          let currentEventType = null;

          for (const line of lines) {
            if (line.trim() === '') continue;

            if (line.startsWith('event: ')) {
              currentEventType = line.substring(7).trim();
              console.log('收到事件类型:', currentEventType);
            } else if (line.startsWith('data: ')) {
              const data = line.substring(6);

              if (data === '[DONE]') {
                cleanup();
                onDone && onDone();
                return;
              }

              try {
                const parsed = JSON.parse(data);

                // 根据事件类型处理数据
                if (currentEventType === 'message' && parsed.content) {
                  console.log('前端收到消息内容:', parsed.content);
                  onDelta && onDelta(parsed.content);
                } else if (currentEventType === 'reasoning' && parsed.content) {
                  console.log('前端收到推理内容:', parsed.content);
                  onReasoning && onReasoning(parsed.content);
                } else if (currentEventType === 'session' && parsed.sessionId) {
                  onSessionCreated && onSessionCreated(parsed.sessionId);
                } else if (currentEventType === 'error' && parsed.error) {
                  cleanup();
                  onError && onError(parsed.error.message || 'AI服务繁忙，请稍后重试');
                  return;
                } else if (parsed.content) {
                  // 兼容没有事件类型的旧格式
                  console.log('前端收到内容(兼容模式):', parsed.content);
                  onDelta && onDelta(parsed.content);
                } else if (parsed.sessionId) {
                  onSessionCreated && onSessionCreated(parsed.sessionId);
                } else if (parsed.error) {
                  cleanup();
                  onError && onError(parsed.error.message || 'AI服务繁忙，请稍后重试');
                  return;
                }
              } catch (error) {
                console.error('解析数据失败:', error, '原始数据:', data);
              }

              // 重置事件类型
              currentEventType = null;
            }
          }
        }
        if (xhr.readyState === 4) {
          cleanup();
          if (xhr.status !== 200) {
            if (retryCount < MAX_RETRIES) {
              retryCount++;
              reconnectTimer = setTimeout(connect, RETRY_DELAY);
            } else {
              onError && onError('AI服务繁忙，请稍后重试');
            }
          } else {
            onDone && onDone();
          }
        }
      }
    };
    xhr.onerror = function() {
      cleanup();
      if (retryCount < MAX_RETRIES) {
        retryCount++;
        reconnectTimer = setTimeout(connect, RETRY_DELAY);
      } else {
        onError && onError('AI服务繁忙，请稍后重试');
      }
    };
    try {
      console.log('发送流式请求到:', apiUrl);
      console.log('请求数据:', JSON.stringify(messageData, null, 2));
      xhr.send(JSON.stringify(messageData));
    } catch (error) {
      console.error('发送请求失败:', error);
      cleanup();
      onError && onError('AI服务繁忙，请稍后重试');
    }
    return () => {
      cleanup();
      xhr.abort();
    };
  }
  return connect();
};

/**
 * 生成PPT大纲
 */
export const generateOutline = async (content, messageId) => {
  try {
    const response = await fetch(`${API_BASE_URL}/chat/generate-outline`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${getToken()}`
      },
      body: JSON.stringify({ content, messageId })
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.message || '生成大纲失败');
    }

    const data = await response.json();
    return data.data.outline;
  } catch (error) {
    console.error('生成大纲失败:', error);
    throw error;
  }
};

/**
 * 生成PPT文件
 */
export const generatePPT = async (outline) => {
  try {
    const response = await fetch(`${API_BASE_URL}/chat/generate-ppt`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${getToken()}`
      },
      body: JSON.stringify({ outline })
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.message || '生成PPT失败');
    }

    // 返回文件blob
    const blob = await response.blob();
    return blob;
  } catch (error) {
    console.error('生成PPT失败:', error);
    throw error;
  }
};

export default {
  authAPI,
  userAPI,
  knowledgeAPI,
  masterAPI,
  scriptAPI,
  imageAPI,
  isLoggedIn,
  getCurrentUserFromCache,
  setCurrentUserToCache,
  clearCurrentUserCache,
  modelsAPI,
  settingsAPI,
  chatAPI,
  generateOutline,
  generatePPT
};