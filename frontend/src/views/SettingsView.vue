<template>
  <div class="settings-view">
    <h1 class="page-title">设置</h1>
    <p class="page-description">自定义您的AIDE应用程序设置和偏好</p>
    
    <div class="settings-container">
      <div class="settings-content">
        <!-- 常规设置 -->
        <div v-if="localActiveSection === 'general'" class="settings-section">
          <h2 class="section-title">常规设置</h2>
          
          <div class="setting-group">
            <h3 class="group-title">界面</h3>
            
            <div class="setting-item">
              <div class="setting-label">
                <span>主题</span>
                <span class="setting-description">选择应用程序的显示主题</span>
              </div>
              <div class="setting-control">
                <select v-model="settings.theme" class="form-input">
                  <option value="light">浅色</option>
                  <option value="dark">深色</option>
                  <option value="system">跟随系统</option>
                </select>
              </div>
            </div>
            
            <div class="setting-item">
              <div class="setting-label">
                <span>主色调</span>
                <span class="setting-description">选择应用程序的主色调</span>
              </div>
              <div class="setting-control color-picker">
                <div 
                  v-for="color in themeColors" 
                  :key="color.value"
                  class="color-option"
                  :class="{ active: settings.primaryColor === color.value }"
                  :style="{ backgroundColor: color.hex }"
                  @click="settings.primaryColor = color.value"
                ></div>
              </div>
            </div>
            
            <div class="setting-item">
              <div class="setting-label">
                <span>字体大小</span>
                <span class="setting-description">调整应用程序的文本大小</span>
              </div>
              <div class="setting-control">
                <div class="range-control">
                  <input 
                    type="range" 
                    v-model="settings.fontSize" 
                    min="12" 
                    max="20" 
                    step="1"
                  >
                  <span class="range-value">{{ settings.fontSize }}px</span>
                </div>
              </div>
            </div>
            
            <div class="setting-item">
              <div class="setting-label">
                <span>动画效果</span>
                <span class="setting-description">启用或禁用界面动画效果</span>
              </div>
              <div class="setting-control">
                <label class="toggle">
                  <input type="checkbox" v-model="settings.animations">
                  <span class="toggle-slider"></span>
                </label>
              </div>
            </div>
          </div>
          
          <div class="setting-group">
            <h3 class="group-title">语言与区域</h3>
            
            <div class="setting-item">
              <div class="setting-label">
                <span>语言</span>
                <span class="setting-description">选择应用程序的显示语言</span>
              </div>
              <div class="setting-control">
                <select v-model="settings.language" class="form-input">
                  <option value="zh-CN">简体中文</option>
                  <option value="en-US">English (US)</option>
                  <option value="ja-JP">日本語</option>
                  <option value="ko-KR">한국어</option>
                </select>
              </div>
            </div>
            
            <div class="setting-item">
              <div class="setting-label">
                <span>日期格式</span>
                <span class="setting-description">选择日期的显示格式</span>
              </div>
              <div class="setting-control">
                <select v-model="settings.dateFormat" class="form-input">
                  <option value="YYYY-MM-DD">YYYY-MM-DD</option>
                  <option value="DD/MM/YYYY">DD/MM/YYYY</option>
                  <option value="MM/DD/YYYY">MM/DD/YYYY</option>
                  <option value="YYYY年MM月DD日">YYYY年MM月DD日</option>
                </select>
              </div>
            </div>
          </div>
          
          <div class="setting-group">
            <h3 class="group-title">通知</h3>
            
            <div class="setting-item">
              <div class="setting-label">
                <span>桌面通知</span>
                <span class="setting-description">启用或禁用桌面通知</span>
              </div>
              <div class="setting-control">
                <label class="toggle">
                  <input type="checkbox" v-model="settings.desktopNotifications">
                  <span class="toggle-slider"></span>
                </label>
              </div>
            </div>
            
            <div class="setting-item">
              <div class="setting-label">
                <span>声音提醒</span>
                <span class="setting-description">启用或禁用声音提醒</span>
              </div>
              <div class="setting-control">
                <label class="toggle">
                  <input type="checkbox" v-model="settings.soundNotifications">
                  <span class="toggle-slider"></span>
                </label>
              </div>
            </div>
          </div>
          
          <div class="setting-group">
            <h3 class="group-title">API配置</h3>
            
            <div class="setting-item">
              <div class="setting-label">
                <span>API服务器地址</span>
                <span class="setting-description">设置后端API服务器的地址</span>
              </div>
              <div class="setting-control">
                <div class="api-url-control">
                  <input 
                    type="text" 
                    v-model="apiBaseUrl" 
                    class="form-input"
                    placeholder="http://localhost:3000/api"
                  >
                  <button class="btn-primary" @click="saveApiUrl">保存</button>
                </div>
                <div class="api-url-info">
                  <p>当前API地址: {{ currentApiUrl }}</p>
                  <p>支持的地址格式:</p>
                  <ul>
                    <li>http://localhost:3000/api</li>
                    <li>http://127.0.0.1:3000/api</li>
                    <li>http://**************:3000/api</li>
                  </ul>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 用户管理 -->
        <div v-if="localActiveSection === 'users'" class="settings-section">
          <UserManagementView />
        </div>
        
        <!-- AI设置 -->
        <div v-if="localActiveSection === 'ai'" class="settings-section">
          <h2 class="section-title">AI设置</h2>

          <!-- OpenAI API 配置 -->
          <div class="setting-group">
            <h3 class="group-title">🔑 OpenAI API 配置</h3>
            <p class="group-description">配置OpenAI API密钥以使用RAG知识库、PPT生成等功能</p>

            <div class="setting-item">
              <div class="setting-label">
                <span>API 密钥</span>
                <span class="setting-description">您的OpenAI API密钥</span>
              </div>
              <div class="setting-control">
                <input
                  type="password"
                  v-model="settings.apiKey"
                  class="form-input"
                  placeholder="sk-..."
                  @input="saveSettings"
                >
              </div>
            </div>

            <div class="setting-item">
              <div class="setting-label">
                <span>API 端点</span>
                <span class="setting-description">OpenAI API服务器地址</span>
              </div>
              <div class="setting-control">
                <input
                  type="text"
                  v-model="settings.api_endpoint"
                  class="form-input"
                  placeholder="https://api.openai.com/v1"
                  @input="saveSettings"
                >
              </div>
            </div>

            <div class="api-status">
              <div class="status-indicator" :class="{ 'configured': settings.apiKey }">
                {{ settings.apiKey ? '✅ API密钥已配置' : '⚠️ 请配置API密钥' }}
              </div>
              <small v-if="!settings.apiKey" class="status-hint">
                配置API密钥后可使用RAG知识库、PPT生成等高级功能
              </small>
            </div>
          </div>

          <!-- 本地RAG配置 -->
          <div class="setting-group">
            <h3 class="group-title">🏠 本地RAG配置</h3>
            <p class="group-description">使用本地模型进行RAG知识库处理，无需API密钥，完全本地化</p>

            <div class="local-rag-status">
              <div class="status-card" :class="localRagStatus.available ? 'status-available' : 'status-unavailable'">
                <div class="status-header">
                  <span class="status-icon">{{ localRagStatus.available ? '✅' : '⚠️' }}</span>
                  <span class="status-text">
                    {{ localRagStatus.available ? '本地RAG可用' : '本地RAG不可用' }}
                  </span>
                </div>
                <div class="status-details">
                  <div class="status-item">
                    <span class="status-label">Ollama服务:</span>
                    <span class="status-value" :class="localRagStatus.ollama?.available ? 'available' : 'unavailable'">
                      {{ localRagStatus.ollama?.available ? '运行中' : '未运行' }}
                    </span>
                  </div>
                  <div class="status-item">
                    <span class="status-label">离线RAG:</span>
                    <span class="status-value" :class="localRagStatus.embedding?.available ? 'available' : 'unavailable'">
                      {{ localRagStatus.embedding?.available ? '可用' : '不可用' }}
                    </span>
                  </div>
                  <div class="status-item" v-if="localRagStatus.ollama?.models?.length">
                    <span class="status-label">可用模型:</span>
                    <span class="status-value available">{{ localRagStatus.ollama.models.length }}个</span>
                  </div>
                </div>
              </div>
            </div>

            <div class="local-rag-actions">
              <button
                class="btn-primary"
                @click="startOllamaService"
                :disabled="ollamaStarting"
              >
                {{ ollamaStarting ? '启动中...' : '启动Ollama' }}
              </button>
              <button
                class="btn-secondary"
                @click="stopOllamaService"
                :disabled="ollamaStopping"
              >
                {{ ollamaStopping ? '停止中...' : '停止Ollama' }}
              </button>
              <button
                class="btn-primary"
                @click="startRAGService"
                :disabled="ragServiceStarting"
              >
                {{ ragServiceStarting ? '启动中...' : '启动RAG服务' }}
              </button>
              <button
                class="btn-secondary"
                @click="stopRAGService"
                :disabled="ragServiceStopping"
              >
                {{ ragServiceStopping ? '停止中...' : '停止RAG服务' }}
              </button>
              <button
                class="btn-secondary"
                @click="checkLocalRAGStatus"
                :disabled="localRagChecking"
              >
                {{ localRagChecking ? '检查中...' : '检查状态' }}
              </button>
            </div>

            <div class="local-rag-features">
              <h4>离线RAG特性:</h4>
              <ul class="features-list">
                <li>🏠 完全离线处理，无需网络连接</li>
                <li>🔒 数据隐私保护，文档不上传到云端</li>
                <li>🚀 无需API密钥，免费使用</li>
                <li>📊 TF-IDF向量化，支持中英文</li>
                <li>🔍 余弦相似度检索，快速准确</li>
                <li>💾 本地存储，数据持久化</li>
                <li>⚡ 轻量级实现，启动快速</li>
              </ul>
            </div>
          </div>

          <!-- Midjourney配置 -->
          <div class="setting-group">
            <h3 class="group-title">🎨 Midjourney配置</h3>
            <p class="group-description">配置Midjourney Discord Bot，实现AI图片生成功能</p>

            <div class="midjourney-status">
              <div class="status-card" :class="midjourneyStatus.configured ? 'status-available' : 'status-unavailable'">
                <div class="status-header">
                  <span class="status-icon">{{ midjourneyStatus.configured ? '✅' : '⚠️' }}</span>
                  <span class="status-text">
                    {{ midjourneyStatus.configured ? 'Midjourney已配置' : 'Midjourney未配置' }}
                  </span>
                </div>
                <div class="status-details">
                  <div class="status-item">
                    <span class="status-label">Discord Token:</span>
                    <span class="status-value" :class="midjourneyStatus.config?.hasToken ? 'available' : 'unavailable'">
                      {{ midjourneyStatus.config?.hasToken ? '已配置' : '未配置' }}
                    </span>
                  </div>
                  <div class="status-item">
                    <span class="status-label">频道ID:</span>
                    <span class="status-value" :class="midjourneyStatus.config?.hasChannelId ? 'available' : 'unavailable'">
                      {{ midjourneyStatus.config?.hasChannelId ? '已配置' : '未配置' }}
                    </span>
                  </div>
                  <div class="status-item">
                    <span class="status-label">服务器ID:</span>
                    <span class="status-value" :class="midjourneyStatus.config?.hasServerId ? 'available' : 'unavailable'">
                      {{ midjourneyStatus.config?.hasServerId ? '已配置' : '未配置' }}
                    </span>
                  </div>
                </div>
              </div>
            </div>

            <div class="midjourney-config">
              <div class="config-item">
                <label for="discordToken">Discord Bot Token:</label>
                <input
                  type="password"
                  id="discordToken"
                  v-model="midjourneyConfig.discordToken"
                  placeholder="输入Discord Bot Token"
                  @input="saveMidjourneyConfig"
                />
              </div>

              <div class="config-item">
                <label for="channelId">频道ID:</label>
                <input
                  type="text"
                  id="channelId"
                  v-model="midjourneyConfig.channelId"
                  placeholder="输入Midjourney频道ID"
                  @input="saveMidjourneyConfig"
                />
              </div>

              <div class="config-item">
                <label for="serverId">服务器ID:</label>
                <input
                  type="text"
                  id="serverId"
                  v-model="midjourneyConfig.serverId"
                  placeholder="输入Discord服务器ID"
                  @input="saveMidjourneyConfig"
                />
              </div>
            </div>

            <div class="midjourney-actions">
              <button
                class="btn-primary"
                @click="testMidjourneyConnection"
                :disabled="midjourneyTesting"
              >
                {{ midjourneyTesting ? '测试中...' : '测试连接' }}
              </button>
              <button
                class="btn-secondary"
                @click="checkMidjourneyStatus"
                :disabled="midjourneyChecking"
              >
                {{ midjourneyChecking ? '检查中...' : '检查状态' }}
              </button>
            </div>

            <div class="midjourney-features">
              <h4>Midjourney特性:</h4>
              <ul class="features-list">
                <li>🎨 高质量AI图片生成</li>
                <li>🔄 图片放大和变体功能</li>
                <li>⚙️ 丰富的生成参数控制</li>
                <li>📊 生成历史记录管理</li>
                <li>🎯 多种艺术风格支持</li>
                <li>🚀 异步任务处理</li>
                <li v-if="!midjourneyStatus.configured">🎭 演示模式可用（无需配置）</li>
              </ul>
            </div>

            <div v-if="!midjourneyStatus.configured" class="demo-mode-notice">
              <h4>🎭 演示模式说明:</h4>
              <p>当前系统将使用演示模式，生成模拟的图片结果用于功能展示。</p>
              <p>演示模式特点：</p>
              <ul>
                <li>✅ 无需Discord配置即可体验功能</li>
                <li>✅ 快速生成（3-8秒）</li>
                <li>✅ 支持所有参数设置</li>
                <li>⚠️ 生成的是示例图片，非真实Midjourney结果</li>
              </ul>
              <p>如需使用真实的Midjourney服务，请按上述指南配置Discord信息。</p>
            </div>

            <div class="midjourney-guide">
              <h4>配置指南:</h4>
              <ol class="guide-list">
                <li>创建Discord应用和Bot</li>
                <li>将Bot添加到有Midjourney的服务器</li>
                <li>获取Bot Token、频道ID和服务器ID</li>
                <li>在上方输入配置信息</li>
                <li>点击"测试连接"验证配置</li>
              </ol>
            </div>
          </div>

          <div class="setting-group">
            <h3 class="group-title">模型管理</h3>
            
            <div class="models-list-header">
              <div class="model-name-header">模型名称</div>
              <div class="model-type-header">类型</div>
              <div class="model-default-header">默认</div>
              <div class="model-actions-header">操作</div>
            </div>
            
            <div class="models-list">
              <div v-for="model in sortedAiModelsList" :key="model.id" class="model-item">
                <div class="model-name">{{ model.name }}</div>
                <div class="model-type">{{ model.type === 'chat' ? '聊天' : '生图' }}</div>
                <div class="model-default">
                  <span class="default-tag" :class="{ 'is-default': isDefaultModel(model) }">
                    {{ isDefaultModel(model) ? '是' : '' }}
                  </span>
                  <button 
                    v-if="!isDefaultModel(model)" 
                    class="btn-set-default" 
                    @click="setAsDefault(model)"
                    title="设为默认模型"
                  >
                    设为默认
                  </button>
                </div>
                <div class="model-actions">
                  <button class="btn-icon" @click="editModel(model)">
                    <span>✏️</span>
                  </button>
                  <button class="btn-icon" @click="deleteModel(model)">
                    <span>🗑️</span>
                  </button>
                </div>
              </div>
              <div class="add-model">
                <button class="btn-add" @click="showAddModelModal()">
                  <span>+</span> 添加新模型
                </button>
              </div>
            </div>
          </div>
          
          
          

        </div>
        
        <!-- 分类管理 -->
        <div v-if="localActiveSection === 'categories'" class="settings-section">
          <h2 class="section-title">分类管理</h2>
          
          <div class="category-tabs">
            <button 
              v-for="(type, index) in [{id: 'master', name: '大师库'}, {id: 'image', name: '图片库'}, {id: 'script', name: '话述库'}, {id: 'prompt', name: '提示词库'}]" 
              :key="type.id"
              class="tab-btn" 
              :class="{ active: activeCategoryType === type.id }"
              @click="activeCategoryType = type.id"
            >
              {{ type.name }}
            </button>
          </div>
          
          <div class="setting-group" v-if="activeCategoryType === 'master'">
            <h3 class="group-title">大师库分类</h3>
            
            <div class="categories-list">
              <div v-for="(category, index) in masterCategories" :key="category.id" class="category-item">
                <div class="category-info">
                  <span class="drag-handle">⠿</span>
                  <div class="category-content">
                    <span class="category-name">{{ category.name }}</span>
                  </div>
                </div>
                <div class="category-actions">
                  <button class="btn-icon" @click="editCategory('master', index)">
                    <span>✏️</span>
                  </button>
                  <button class="btn-icon" @click="deleteCategory('master', index)">
                    <span>🗑️</span>
                  </button>
                </div>
              </div>
              <div class="add-category">
                <button class="btn-add" @click="showAddCategoryModal('master')">
                  <span>+</span> 添加新分类
                </button>
              </div>
            </div>
          </div>
          
          <div class="setting-group" v-if="activeCategoryType === 'image'">
            <h3 class="group-title">图片库分类</h3>
            
            <div class="categories-list">
              <div v-for="(category, index) in imageCategories" :key="category.id" class="category-item">
                <div class="category-info">
                  <span class="drag-handle">⠿</span>
                  <div class="category-content">
                    <span class="category-name">{{ category.name }}</span>
                  </div>
                </div>
                <div class="category-actions">
                  <button class="btn-icon" @click="editCategory('image', index)">
                    <span>✏️</span>
                  </button>
                  <button class="btn-icon" @click="deleteCategory('image', index)">
                    <span>🗑️</span>
                  </button>
                </div>
              </div>
              <div class="add-category">
                <button class="btn-add" @click="showAddCategoryModal('image')">
                  <span>+</span> 添加新分类
                </button>
              </div>
            </div>
          </div>
          
          <div class="setting-group" v-if="activeCategoryType === 'script'">
            <h3 class="group-title">话述库分类</h3>
            
            <div class="categories-list">
              <div v-for="(category, index) in scriptCategories" :key="category.id" class="category-item">
                <div class="category-info">
                  <span class="drag-handle">⠿</span>
                  <div class="category-content">
                    <span class="category-name">{{ category.name }}</span>
                  </div>
                </div>
                <div class="category-actions">
                  <button class="btn-icon" @click="editCategory('script', index)">
                    <span>✏️</span>
                  </button>
                  <button class="btn-icon" @click="deleteCategory('script', index)">
                    <span>🗑️</span>
                  </button>
                </div>
              </div>
              <div class="add-category">
                <button class="btn-add" @click="showAddCategoryModal('script')">
                  <span>+</span> 添加新分类
                </button>
              </div>
            </div>
          </div>
          
          <!-- 新增：提示词库分类管理 -->
          <div class="setting-group" v-if="activeCategoryType === 'prompt'">
            <h3 class="group-title">提示词库分类</h3>
            <div class="categories-list-container">
              <div class="add-category-form">
                <input
                  type="text"
                  v-model="newPromptCategoryName"
                  class="form-input"
                  placeholder="输入新分类名称"
                  @keyup.enter="addPromptCategory"
                />
                <button class="btn btn-primary" @click="addPromptCategory">添加分类</button>
              </div>

              <div class="categories-list" ref="promptCategoriesListRef">
                <div 
                  v-for="(category, index) in promptCategories" 
                  :key="category.id" 
                  class="category-item"
                  draggable="true"
                  @dragstart="handleDragStart(index)"
                  @dragover.prevent
                  @drop="handleDrop(index)"
                  @dragend="handleDragEnd"
                  :class="{ 'dragging': draggingIndex === index }"
                >
                  <div class="category-info">
                    <span class="drag-handle">⠿</span>
                    <div class="category-content">
                      <input 
                        v-if="editingCategoryId === category.id" 
                        type="text" 
                        v-model="editingCategoryName" 
                        class="form-input edit-input"
                        @keyup.enter="saveCategoryEdit(category.id)"
                        @blur="cancelCategoryEdit"
                      />
                      <span v-else class="category-name">{{ category.name }}</span>
                    </div>
                  </div>
                  <div class="category-actions">
                    <button class="btn-icon" @click="startCategoryEdit(category)">✏️</button>
                    <button class="btn-icon" @click="deletePromptCategory(category.id)">🗑️</button>
                  </div>
                </div>
              </div>
              <div v-if="promptCategories.length === 0" class="empty-state">
                <p>暂无提示词分类，请添加一个。</p>
              </div>
            </div>
            <p class="section-description">管理提示词的分类，用于更好的组织和查找</p>
          </div>
        </div>
        
        <!-- 存储设置 -->
        <div v-if="localActiveSection === 'storage'" class="settings-section">
          <h2 class="section-title">存储设置</h2>
          
          <div class="setting-group">
            <h3 class="group-title">数据存储</h3>
            
            <div class="setting-item">
              <div class="setting-label">
                <span>存储位置</span>
                <span class="setting-description">选择数据存储的位置</span>
              </div>
              <div class="setting-control">
                <select v-model="settings.storageLocation" class="form-input">
                  <option value="local">本地存储</option>
                  <option value="cloud">云端存储</option>
                </select>
              </div>
            </div>
            
            <div class="setting-item">
              <div class="setting-label">
                <span>自动备份</span>
                <span class="setting-description">启用或禁用数据自动备份</span>
              </div>
              <div class="setting-control">
                <label class="toggle">
                  <input type="checkbox" v-model="settings.autoBackup">
                  <span class="toggle-slider"></span>
                </label>
              </div>
            </div>
            
            <div class="setting-item" v-if="settings.autoBackup">
              <div class="setting-label">
                <span>备份频率</span>
                <span class="setting-description">设置自动备份的频率</span>
              </div>
              <div class="setting-control">
                <select v-model="settings.backupFrequency" class="form-input">
                  <option value="daily">每天</option>
                  <option value="weekly">每周</option>
                  <option value="monthly">每月</option>
                </select>
              </div>
            </div>
          </div>
          
          <div class="setting-group">
            <h3 class="group-title">存储管理</h3>
            
            <div class="storage-stats">
              <div class="stat-item">
                <div class="stat-label">总存储空间</div>
                <div class="stat-value">{{ formatStorage(storageStats.total) }}</div>
              </div>
              <div class="stat-item">
                <div class="stat-label">已使用</div>
                <div class="stat-value">{{ formatStorage(storageStats.used) }}</div>
              </div>
              <div class="stat-item">
                <div class="stat-label">可用空间</div>
                <div class="stat-value">{{ formatStorage(storageStats.available) }}</div>
              </div>
            </div>
            
            <div class="storage-usage">
              <div class="usage-bar">
                <div 
                  class="usage-fill" 
                  :style="{ width: `${(storageStats.used / storageStats.total) * 100}%` }"
                ></div>
              </div>
              <div class="usage-text">{{ Math.round((storageStats.used / storageStats.total) * 100) }}% 已使用</div>
            </div>
            
            <div class="storage-actions">
              <button class="btn btn-secondary" @click="clearCache">清除缓存</button>
              <button class="btn btn-primary" @click="backupNow">立即备份</button>
            </div>
          </div>
          
          <div class="setting-group">
            <h3 class="group-title">数据导入/导出</h3>
            
            <div class="setting-actions">
              <button class="btn btn-secondary" @click="exportData">导出数据</button>
              <button class="btn btn-primary" @click="showImportModal = true">导入数据</button>
            </div>
          </div>
        </div>
        
        <!-- 隐私设置 -->
        <div v-if="localActiveSection === 'privacy'" class="settings-section">
          <h2 class="section-title">隐私设置</h2>
          
          <div class="setting-group">
            <h3 class="group-title">数据收集</h3>
            
            <div class="setting-item">
              <div class="setting-label">
                <span>使用数据分析</span>
                <span class="setting-description">允许收集匿名使用数据以改进应用</span>
              </div>
              <div class="setting-control">
                <label class="toggle">
                  <input type="checkbox" v-model="settings.usageAnalytics">
                  <span class="toggle-slider"></span>
                </label>
              </div>
            </div>
            
            <div class="setting-item">
              <div class="setting-label">
                <span>崩溃报告</span>
                <span class="setting-description">自动发送崩溃报告以帮助修复问题</span>
              </div>
              <div class="setting-control">
                <label class="toggle">
                  <input type="checkbox" v-model="settings.crashReports">
                  <span class="toggle-slider"></span>
                </label>
              </div>
            </div>
          </div>
          
          <div class="setting-group">
            <h3 class="group-title">数据保留</h3>
            
            <div class="setting-item">
              <div class="setting-label">
                <span>聊天历史</span>
                <span class="setting-description">设置聊天历史的保留时间</span>
              </div>
              <div class="setting-control">
                <select v-model="settings.chatHistoryRetention" class="form-input">
                  <option value="forever">永久保留</option>
                  <option value="1year">1年</option>
                  <option value="6months">6个月</option>
                  <option value="3months">3个月</option>
                  <option value="1month">1个月</option>
                </select>
              </div>
            </div>
            
            <div class="setting-actions">
              <button class="btn btn-danger" @click="showClearDataModal = true">清除所有数据</button>
            </div>
          </div>
        </div>
        
        <!-- 关于 -->
        <div v-if="localActiveSection === 'about'" class="settings-section">
          <h2 class="section-title">关于</h2>
          
          <div class="about-app">
            <div class="app-logo">AIDE</div>
            <div class="app-name">AI-Integrated Design Environment</div>
            <div class="app-version">版本 1.0.0</div>
            <div class="app-copyright">© 2023 AIDE Team. 保留所有权利。</div>
          </div>
          
          <div class="about-section">
            <h3>关于AIDE</h3>
            <p>AIDE (AI-Integrated Design Environment) 是一个集成了多种AI功能的设计环境，旨在帮助设计师提高工作效率和创造力。通过整合聊天、图像生成、知识库和资源库等功能，AIDE为设计工作流程提供了全方位的支持。</p>
          </div>
          
          <div class="about-section">
            <h3>技术栈</h3>
            <ul class="tech-stack">
              <li>前端框架: Vue.js 3</li>
              <li>UI组件: 自定义组件</li>
              <li>状态管理: Vue Composition API</li>
              <li>路由: Vue Router</li>
              <li>AI集成: 多种AI模型API</li>
            </ul>
          </div>
          
          <div class="about-section">
            <h3>许可证</h3>
            <p>本软件根据MIT许可证发布。</p>
          </div>
          
          <div class="about-actions">
            <button class="btn btn-secondary" @click="checkForUpdates">检查更新</button>
            <button class="btn btn-primary" @click="openDocumentation">查看文档</button>
          </div>
        </div>
      </div>
    </div>
    
    <!-- 导入数据模态框 -->
    <div v-if="showImportModal" class="modal">
      <div class="modal-content">
        <div class="modal-header">
          <h3>导入数据</h3>
          <button class="close-btn" @click="showImportModal = false">×</button>
        </div>
        <div class="modal-body">
          <div class="import-instructions">
            <p>选择一个AIDE数据备份文件进行导入。导入将覆盖当前数据。</p>
            <p class="warning">警告：此操作不可撤销，请确保您已备份当前数据。</p>
          </div>
          
          <div class="file-upload">
            <label class="file-upload-label">
              <span class="upload-icon">📂</span>
              <span class="upload-text">选择备份文件</span>
              <input type="file" class="file-input" accept=".json,.aide" @change="handleFileSelect">
            </label>
            <div v-if="selectedFile" class="selected-file">
              已选择: {{ selectedFile.name }} ({{ formatFileSize(selectedFile.size) }})
            </div>
          </div>
        </div>
        <div class="modal-footer">
          <button class="btn btn-secondary" @click="showImportModal = false">取消</button>
          <button 
            class="btn btn-primary" 
            @click="importData" 
            :disabled="!selectedFile"
          >
            导入
          </button>
        </div>
      </div>
    </div>
    
    <!-- 清除数据确认模态框 -->
    <div v-if="showClearDataModal" class="modal">
      <div class="modal-content">
        <div class="modal-header">
          <h3>清除所有数据</h3>
          <button class="close-btn" @click="showClearDataModal = false">×</button>
        </div>
        <div class="modal-body">
          <div class="danger-warning">
            <div class="warning-icon">⚠️</div>
            <h4>警告：此操作不可撤销</h4>
            <p>您即将删除所有应用数据，包括：</p>
            <ul>
              <li>所有聊天历史记录</li>
              <li>所有生成的图像</li>
              <li>所有知识库内容</li>
              <li>所有资源库内容</li>
              <li>所有脚本和自定义设置</li>
            </ul>
            <p>此操作完成后，数据将无法恢复。</p>
          </div>
          
          <div class="confirmation-input">
            <label>请输入 "DELETE" 以确认：</label>
            <input 
              type="text" 
              v-model="deleteConfirmation" 
              class="form-input"
              placeholder="DELETE"
            >
          </div>
        </div>
        <div class="modal-footer">
          <button class="btn btn-secondary" @click="showClearDataModal = false">取消</button>
          <button 
            class="btn btn-danger" 
            @click="clearAllData" 
            :disabled="deleteConfirmation !== 'DELETE'"
          >
            永久删除所有数据
          </button>
        </div>
      </div>
    </div>
  </div>
  
  <!-- 分类管理模态框 -->
  <div v-if="showCategoryModal" class="modal">
    <div class="modal-content">
      <div class="modal-header">
        <h3>{{ currentCategoryIndex === -1 ? '添加新分类' : '编辑分类' }}</h3>
        <button class="close-btn" @click="showCategoryModal = false">&times;</button>
      </div>
      <div class="modal-body">
        <div class="form-group">
          <label for="categoryName">分类名称</label>
          <input 
            type="text" 
            id="categoryName" 
            v-model="newCategoryName" 
            class="form-input"
            placeholder="输入分类名称"
            @keyup.enter="saveCategory"
          >
        </div>
      </div>
      <div class="modal-footer">
        <button class="btn btn-secondary" @click="showCategoryModal = false">取消</button>
        <button class="btn btn-primary" @click="saveCategory">保存</button>
      </div>
    </div>
  </div>
  
  <!-- 模型管理模态框 -->
  <div v-if="showModelModal" class="modal">
    <div class="modal-content">
      <div class="modal-header">
        <h3>{{ newModel.id ? '编辑模型' : '添加新模型' }}</h3>
        <button class="close-btn" @click="showModelModal = false">&times;</button>
      </div>
      <div class="modal-body">
        <div class="form-group">
          <label for="modelName">模型名称</label>
          <input 
            type="text" 
            id="modelName" 
            v-model="newModel.name" 
            class="form-input"
            placeholder="输入模型名称"
          >
        </div>
        
        <div class="form-group">
          <label for="modelType">模型类型</label>
          <select id="modelType" v-model="newModel.type" class="form-input">
            <option value="chat">聊天模型</option>
            <option value="image">图像生成模型</option>
          </select>
        </div>
        
        <div class="form-group">
          <label for="modelProvider">提供商</label>
          <input 
            type="text" 
            id="modelProvider" 
            v-model="newModel.provider" 
            class="form-input"
            placeholder="例如: OpenAI, Midjourney"
          >
        </div>
        
        <div class="form-group">
          <label for="modelApiKey">API密钥</label>
          <input 
            type="text" 
            id="modelApiKey" 
            v-model="newModel.apiKey" 
            class="form-input"
            placeholder="输入API密钥"
          >
        </div>
        
        <div class="form-group">
          <label for="modelApiEndpoint">API端点</label>
          <input 
            type="text" 
            id="modelApiEndpoint" 
            v-model="newModel.api_endpoint" 
            class="form-input"
            placeholder="https://api.example.com"
          >
        </div>
        
        <div class="form-group">
          <label for="modelSystemPrompt">系统提示词</label>
          <textarea 
            id="modelSystemPrompt" 
            v-model="newModel.system_prompt" 
            class="form-input"
            placeholder="输入系统提示词，用于定义AI模型的行为和角色..."
            rows="4"
          ></textarea>
          <div class="field-hint">
            系统提示词用于定义AI模型的基础行为和角色，例如："你是一个专业的建筑设计助手，擅长提供建筑设计和规划建议。"
          </div>
        </div>
        
        <div class="form-group">
          <label for="modelMaxTokens">最大令牌数 (max_tokens)</label>
          <input 
            type="number" 
            id="modelMaxTokens" 
            v-model.number="newModel.max_tokens" 
            class="form-input"
            placeholder="4096"
            min="1"
            max="32768"
          >
          <div class="field-hint">
            控制AI回复的最大长度，数值越大回复越详细但消耗更多token
          </div>
        </div>
        
        <div class="form-group">
          <label for="modelTemperature">温度 (temperature)</label>
          <input 
            type="number" 
            id="modelTemperature" 
            v-model.number="newModel.temperature" 
            class="form-input"
            placeholder="0.7"
            min="0"
            max="2"
            step="0.1"
          >
          <div class="field-hint">
            控制AI回复的创造性，0.0=确定性回复，2.0=高度创造性
          </div>
        </div>
        
        <div class="form-group">
          <label class="checkbox-label">
            <input 
              type="checkbox" 
              v-model="setAsDefaultOnSave"
            >
            <span>设为默认模型</span>
          </label>
          <span class="setting-description">将此模型设为对应类型的默认模型</span>
        </div>
      </div>
      <div class="modal-footer">
        <button class="btn btn-secondary" @click="showModelModal = false">取消</button>
        <button class="btn btn-primary" @click="saveModel">保存</button>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, watch, onMounted, computed, nextTick } from 'vue';
import UserManagementView from './UserManagementView.vue';
import { imageAPI, masterAPI, scriptAPI, modelsAPI, getCurrentApiBaseUrl, setApiBaseUrl, settingsAPI, userAPI } from '@/services/api';

// 接收父组件传递的activeSection
const props = defineProps({
  activeSection: {
    type: String,
    default: 'general'
  }
});

// 定义事件，用于更新父组件中的activeSection
const emit = defineEmits(['update:section']);

// 本地activeSection，与父组件同步
const localActiveSection = ref(props.activeSection);

// API配置相关
const apiBaseUrl = ref('');
const currentApiUrl = ref('');

// 初始化API配置
function initApiConfig() {
  currentApiUrl.value = getCurrentApiBaseUrl();
  apiBaseUrl.value = currentApiUrl.value;
}

// 保存API地址
function saveApiUrl() {
  if (apiBaseUrl.value && apiBaseUrl.value.trim()) {
    setApiBaseUrl(apiBaseUrl.value.trim());
  }
}

// 监听props变化，更新本地状态
watch(() => props.activeSection, (newValue) => {
  localActiveSection.value = newValue;
});

// 监听本地状态变化，通知父组件
watch(localActiveSection, (newValue) => {
  emit('update:section', newValue);
});

// 设置部分
const settingSections = [
  { id: 'general', name: '常规', icon: '🔧' },
  { id: 'users', name: '用户管理', icon: '👥' },
  { id: 'ai', name: 'AI设置', icon: '🤖' },
  { id: 'categories', name: '分类管理', icon: '📋' },
  { id: 'storage', name: '存储', icon: '💾' },
  { id: 'privacy', name: '隐私', icon: '🔒' },
  { id: 'about', name: '关于', icon: 'ℹ️' }
];

const activeSection = ref('general');

// 主题颜色选项
const themeColors = [
  { value: 'blue', hex: '#0077cc' },
  { value: 'green', hex: '#28a745' },
  { value: 'purple', hex: '#6f42c1' },
  { value: 'orange', hex: '#fd7e14' },
  { value: 'red', hex: '#dc3545' },
  { value: 'teal', hex: '#20c997' }
];

// AI模型数据
const aiModelsList = ref([]);

// 设置数据
const settings = ref({
  // 常规设置
  theme: 'light',
  primaryColor: 'blue',
  fontSize: 14,
  animations: true,
  language: 'zh-CN',
  dateFormat: 'YYYY-MM-DD',
  desktopNotifications: true,
  soundNotifications: true,
  
  // AI设置
  defaultChatModel: 'gpt-4',
  defaultImageModel: 'dall-e-3',
  apiKey: '',
  api_endpoint: 'https://api.openai.com/v1',
  
  // 存储设置
  storageLocation: 'local',
  autoBackup: true,
  backupFrequency: 'weekly',
  
  // 隐私设置
  usageAnalytics: true,
  crashReports: true,
  chatHistoryRetention: 'forever'
});

// 分类管理数据
const activeCategoryType = ref('master'); // 当前选中的分类类型
const masterCategories = ref([]);
const imageCategories = ref([]);
const scriptCategories = ref([]);

// 新增：提示词分类相关状态
const promptCategories = ref([]);
const newPromptCategoryName = ref('');
const editingCategoryId = ref(null);
const editingCategoryName = ref('');

// 新增：拖拽排序相关状态
const draggingIndex = ref(null);
const draggedOverIndex = ref(null);
const promptCategoriesListRef = ref(null);

// 按默认模型优先排序的模型列表
const sortedAiModelsList = computed(() => {
  return [...aiModelsList.value].sort((a, b) => {
    const aIsDefault = isDefaultModel(a);
    const bIsDefault = isDefaultModel(b);
    
    if (aIsDefault && !bIsDefault) return -1;
    if (!aIsDefault && bIsDefault) return 1;
    
    return a.name.localeCompare(b.name);
  });
});

// 加载AI模型列表
async function loadModels() {
  console.log('正在尝试加载AI模型列表...');
  try {
    const response = await modelsAPI.getAllModels();
    if (response.success) {
      aiModelsList.value = response.data;
    } else {
      console.error('加载AI模型列表失败:', response.message);
    }
  } catch (err) {
    console.error('加载AI模型列表时出错:', err);
  }
}

// 加载分类数据
async function loadMasterCategories() {
  try {
    const response = await masterAPI.getMasterCategories();
    masterCategories.value = response.data || [];
  } catch (err) {
    console.error('加载大师库分类失败:', err);
    masterCategories.value = [];
  }
}

async function loadImageCategories() {
  try {
    const response = await imageAPI.getImageCategories();
    imageCategories.value = response.data || [];
  } catch (err) {
    console.error('加载图片库分类失败:', err);
    imageCategories.value = [];
  }
}

async function loadScriptCategories() {
  try {
    const response = await scriptAPI.getScriptCategories();
    scriptCategories.value = response.data || [];
  } catch (err) {
    console.error('加载话术库分类失败:', err);
    scriptCategories.value = [];
  }
}

// 组件挂载时加载分类数据
onMounted(async () => {
  // 初始化API配置
  initApiConfig();
  
  // 加载保存的设置
  loadSettings();
  
  // 加载分类数据
  await loadMasterCategories();
  await loadImageCategories();
  await loadScriptCategories();
  await fetchPromptCategories();
  await loadModels();

  // 检查本地RAG状态
  await checkLocalRAGStatus();

  // 加载和检查Midjourney配置
  loadMidjourneyConfig();
  await checkMidjourneyStatus();
});

// 加载设置
function loadSettings() {
  try {
    const savedSettings = localStorage.getItem('appSettings');
    if (savedSettings) {
      const parsedSettings = JSON.parse(savedSettings);
      // 合并保存的设置和默认设置
      settings.value = { ...settings.value, ...parsedSettings };
    }
  } catch (error) {
    console.warn('加载设置失败:', error);
  }
}

// 保存设置
function saveSettings() {
  try {
    localStorage.setItem('appSettings', JSON.stringify(settings.value));
  } catch (error) {
    console.error('保存设置失败:', error);
    alert('保存设置失败，请检查浏览器存储空间');
  }
}

// 分类管理模态框状态
const showCategoryModal = ref(false);
const currentCategoryType = ref('');
const currentCategoryIndex = ref(-1);
const newCategoryName = ref('');

// 模态框状态
const showImportModal = ref(false);
const showClearDataModal = ref(false);
const selectedFile = ref(null);
const deleteConfirmation = ref('');

// 模型管理模态框状态
const showModelModal = ref(false);
const newModel = ref({
  id: '',
  name: '',
  type: 'chat',
  provider: '',
  apiKey: '',
  api_endpoint: '',
  system_prompt: '',
  max_tokens: 4096,
  temperature: 0.7,
  usage: { totalCalls: 0, monthlyCalls: 0, totalTokens: 0 }
});
const setAsDefaultOnSave = ref(false);

// 存储统计数据
const storageStats = ref({
  total: 10 * 1024 * 1024 * 1024, // 10GB
  used: 5 * 1024 * 1024 * 1024, // 5GB
  available: 5 * 1024 * 1024 * 1024 // 5GB
});

// 本地RAG状态
const localRagStatus = ref({
  available: false,
  ollama: {
    available: false,
    models: [],
    url: 'http://localhost:11434'
  },
  dependencies: {
    available: false,
    message: ''
  },
  config: {
    exists: false,
    path: ''
  }
});

const localRagInitializing = ref(false);
const localRagChecking = ref(false);
const ragServiceStarting = ref(false);
const ragServiceStopping = ref(false);
const ollamaStarting = ref(false);
const ollamaStopping = ref(false);

// Midjourney状态
const midjourneyStatus = ref({
  configured: false,
  available: false,
  config: {
    hasToken: false,
    hasChannelId: false,
    hasServerId: false
  },
  message: ''
});

const midjourneyConfig = ref({
  discordToken: '',
  channelId: '',
  serverId: ''
});

const midjourneyTesting = ref(false);
const midjourneyChecking = ref(false);

// 方法
function formatStorage(bytes) {
  if (bytes < 1024) {
    return bytes + ' B';
  } else if (bytes < 1024 * 1024) {
    return (bytes / 1024).toFixed(2) + ' KB';
  } else if (bytes < 1024 * 1024 * 1024) {
    return (bytes / (1024 * 1024)).toFixed(2) + ' MB';
  } else {
    return (bytes / (1024 * 1024 * 1024)).toFixed(2) + ' GB';
  }
}

function formatFileSize(bytes) {
  return formatStorage(bytes);
}

function clearCache() {
  alert('缓存已清除');
}

function backupNow() {
  alert('数据备份已开始');
}

function exportData() {
  alert('数据导出功能已触发');
  // 在实际应用中，这里会创建一个包含所有应用数据的JSON文件并提供下载
}

function handleFileSelect(event) {
  const files = event.target.files;
  if (files.length > 0) {
    selectedFile.value = files[0];
  }
}

function importData() {
  if (selectedFile.value) {
    alert(`正在导入文件: ${selectedFile.value.name}`);
    // 在实际应用中，这里会解析上传的文件并导入数据
    showImportModal.value = false;
    selectedFile.value = null;
  }
}

function clearAllData() {
  if (deleteConfirmation.value === 'DELETE') {
    alert('所有数据已被清除');
    showClearDataModal.value = false;
    deleteConfirmation.value = '';
  }
}

function checkForUpdates() {
  alert('正在检查更新...');
  // 在实际应用中，这里会检查是否有新版本可用
}

function openDocumentation() {
  alert('打开文档');
  // 在实际应用中，这里会打开应用文档页面
}

// 本地RAG相关方法
async function checkLocalRAGStatus() {
  localRagChecking.value = true;
  try {
    const response = await fetch('/api/local-rag/status');

    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`);
    }

    const text = await response.text();
    if (!text) {
      throw new Error('服务器返回空响应');
    }

    const data = JSON.parse(text);

    if (data.success) {
      localRagStatus.value = data.status;
    } else {
      console.error('检查本地RAG状态失败:', data.error);
    }
  } catch (error) {
    console.error('检查本地RAG状态失败:', error);
    alert('检查本地RAG状态失败: ' + error.message);
    localRagStatus.value = {
      available: false,
      ollama: { available: false, models: [] },
      dependencies: { available: false, message: '检查失败' },
      config: { exists: false }
    };
  } finally {
    localRagChecking.value = false;
  }
}

async function initLocalRAG() {
  localRagInitializing.value = true;
  try {
    const response = await fetch('/api/local-rag/init', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      }
    });

    const data = await response.json();

    if (data.success) {
      alert('本地RAG环境初始化成功！');
      // 重新检查状态
      await checkLocalRAGStatus();
    } else {
      alert('本地RAG环境初始化失败: ' + data.error);
    }
  } catch (error) {
    console.error('初始化本地RAG失败:', error);
    alert('初始化本地RAG失败: ' + error.message);
  } finally {
    localRagInitializing.value = false;
  }
}

// Ollama服务控制方法
async function startOllamaService() {
  ollamaStarting.value = true;
  try {
    const response = await fetch('/api/local-rag/ollama/start', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      }
    });

    const data = await response.json();

    if (data.success) {
      alert('Ollama服务启动成功！');
      // 重新检查状态
      await checkLocalRAGStatus();
    } else {
      alert('Ollama服务启动失败: ' + data.error);
    }
  } catch (error) {
    console.error('启动Ollama服务失败:', error);
    alert('启动Ollama服务失败: ' + error.message);
  } finally {
    ollamaStarting.value = false;
  }
}

async function stopOllamaService() {
  ollamaStopping.value = true;
  try {
    const response = await fetch('/api/local-rag/ollama/stop', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      }
    });

    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`);
    }

    const text = await response.text();
    if (!text) {
      throw new Error('服务器返回空响应');
    }

    const data = JSON.parse(text);

    if (data.success) {
      alert('Ollama服务停止成功！');
      // 重新检查状态
      await checkLocalRAGStatus();
    } else {
      alert('Ollama服务停止失败: ' + data.error);
    }
  } catch (error) {
    console.error('停止Ollama服务失败:', error);
    alert('停止Ollama服务失败: ' + error.message);
  } finally {
    ollamaStopping.value = false;
  }
}

// RAG服务控制方法
async function startRAGService() {
  ragServiceStarting.value = true;
  try {
    const response = await fetch('/api/local-rag/start', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      }
    });

    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`);
    }

    const text = await response.text();
    if (!text) {
      throw new Error('服务器返回空响应');
    }

    const data = JSON.parse(text);

    if (data.success) {
      alert('RAG服务启动成功！');
      // 重新检查状态
      await checkLocalRAGStatus();
    } else {
      alert('RAG服务启动失败: ' + data.error);
    }
  } catch (error) {
    console.error('启动RAG服务失败:', error);
    alert('启动RAG服务失败: ' + error.message);
  } finally {
    ragServiceStarting.value = false;
  }
}

async function stopRAGService() {
  ragServiceStopping.value = true;
  try {
    const response = await fetch('/api/local-rag/stop', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      }
    });

    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`);
    }

    const text = await response.text();
    if (!text) {
      throw new Error('服务器返回空响应');
    }

    const data = JSON.parse(text);

    if (data.success) {
      alert('RAG服务停止成功！');
      // 重新检查状态
      await checkLocalRAGStatus();
    } else {
      alert('RAG服务停止失败: ' + data.error);
    }
  } catch (error) {
    console.error('停止RAG服务失败:', error);
    alert('停止RAG服务失败: ' + error.message);
  } finally {
    ragServiceStopping.value = false;
  }
}

// Midjourney相关方法
async function checkMidjourneyStatus() {
  midjourneyChecking.value = true;
  try {
    const response = await fetch('/api/midjourney/status');
    const data = await response.json();

    if (data.success) {
      midjourneyStatus.value = data.status;
    } else {
      console.error('检查Midjourney状态失败:', data.error);
    }
  } catch (error) {
    console.error('检查Midjourney状态失败:', error);
    midjourneyStatus.value = {
      configured: false,
      available: false,
      config: { hasToken: false, hasChannelId: false, hasServerId: false },
      message: '检查失败'
    };
  } finally {
    midjourneyChecking.value = false;
  }
}

async function testMidjourneyConnection() {
  midjourneyTesting.value = true;
  try {
    const response = await fetch('/api/midjourney/test', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        token: midjourneyConfig.value.discordToken,
        channelId: midjourneyConfig.value.channelId,
        serverId: midjourneyConfig.value.serverId
      })
    });

    const data = await response.json();

    if (data.success) {
      alert('Midjourney连接测试成功！');
      // 重新检查状态
      await checkMidjourneyStatus();
    } else {
      alert('Midjourney连接测试失败: ' + data.error);
    }
  } catch (error) {
    console.error('测试Midjourney连接失败:', error);
    alert('测试Midjourney连接失败: ' + error.message);
  } finally {
    midjourneyTesting.value = false;
  }
}

function saveMidjourneyConfig() {
  // 保存到localStorage
  localStorage.setItem('midjourneyConfig', JSON.stringify(midjourneyConfig.value));

  // 更新环境变量（实际应用中可能需要重启服务）
  if (midjourneyConfig.value.discordToken) {
    process.env.DISCORD_TOKEN = midjourneyConfig.value.discordToken;
  }
  if (midjourneyConfig.value.channelId) {
    process.env.MIDJOURNEY_CHANNEL_ID = midjourneyConfig.value.channelId;
  }
  if (midjourneyConfig.value.serverId) {
    process.env.MIDJOURNEY_SERVER_ID = midjourneyConfig.value.serverId;
  }

  console.log('Midjourney配置已保存');
}

function loadMidjourneyConfig() {
  try {
    const saved = localStorage.getItem('midjourneyConfig');
    if (saved) {
      midjourneyConfig.value = JSON.parse(saved);
    }
  } catch (error) {
    console.error('加载Midjourney配置失败:', error);
  }
}

// 检查是否为默认模型
function isDefaultModel(model) {
  if (model.type === 'chat') {
    return settings.value.defaultChatModel === model.id;
  } else if (model.type === 'image') {
    return settings.value.defaultImageModel === model.id;
  }
  return false;
}

// 设置为默认模型
async function setAsDefault(model) {
  try {
    // 验证模型是否存在于数据库中
    const response = await modelsAPI.getModelById(model.id);
    if (!response.success) {
      alert('模型不存在于数据库中，无法设置为默认');
      return;
    }
    
    if (model.type === 'chat') {
      settings.value.defaultChatModel = model.id;
    } else if (model.type === 'image') {
      settings.value.defaultImageModel = model.id;
    }
    
    // 保存设置到本地存储
    saveSettings();
    
    // 更新用户设置到数据库
    try {
      await userAPI.updateSettings({
        defaultChatModel: settings.value.defaultChatModel,
        defaultImageModel: settings.value.defaultImageModel
      });
    } catch (error) {
      console.warn('保存默认模型设置到数据库失败:', error);
    }
    
    alert('默认模型设置成功！');
  } catch (error) {
    console.error('设置默认模型失败:', error);
    alert('设置默认模型失败: ' + error.message);
  }
}

// 编辑模型
function editModel(model) {
  newModel.value = JSON.parse(JSON.stringify(model));
  setAsDefaultOnSave.value = isDefaultModel(model);
  showModelModal.value = true;
}

// 删除模型
async function deleteModel(model) {
  if (confirm(`确定要删除模型 ${model.name} 吗？`)) {
    try {
      const response = await modelsAPI.deleteModel(model.id);
      if (response.success) {
        if (isDefaultModel(model)) {
          if (model.type === 'chat') {
            settings.value.defaultChatModel = '';
          } else {
            settings.value.defaultImageModel = '';
          }
        }
        
        await loadModels();
        alert('模型删除成功！');
      } else {
        alert(`删除失败: ${response.message}`);
      }
    } catch (error) {
      console.error('删除模型时出错:', error);
      alert(`删除模型时出错: ${error.message || '请查看控制台'}`);
    }
  }
}

// 显示添加模型模态框
function showAddModelModal() {
  newModel.value = {
    id: '', 
    name: '',
    type: 'chat',
    provider: '',
    apiKey: '',
    api_endpoint: '',
    system_prompt: '',
    max_tokens: 4096,
    temperature: 0.7,
    usage: { totalCalls: 0, monthlyCalls: 0, totalTokens: 0 }
  };
  setAsDefaultOnSave.value = false;
  showModelModal.value = true;
}

// 保存模型
async function saveModel() {
  if (!newModel.value.name) {
    alert('请输入模型名称');
    return;
  }
  
  if (!newModel.value.apiKey) {
    alert('请输入API密钥');
    return;
  }
  
  try {
    let response;
    const modelData = JSON.parse(JSON.stringify(newModel.value));

    // 确保API密钥被包含在数据中
    if (!modelData.apiKey) {
      modelData.apiKey = newModel.value.apiKey;
    }

    if (modelData.id) {
      // 更新现有模型
      response = await modelsAPI.updateModel(modelData.id, modelData);
    } else {
      // 添加新模型
      // 后端需要一个ID，所以我们在前端生成一个
      modelData.id = 'model-' + Date.now();
      response = await modelsAPI.createModel(modelData);
    }

    if (response.success) {
      const savedModel = response.data;
      
      // 如果设置为默认模型，更新设置
      if (setAsDefaultOnSave.value) {
        if (savedModel.type === 'chat') {
          settings.value.defaultChatModel = savedModel.id;
        } else if (savedModel.type === 'image') {
          settings.value.defaultImageModel = savedModel.id;
        }
        
        // 保存设置到本地存储
        saveSettings();
      }
      
      await loadModels();
      showModelModal.value = false;
      alert('模型保存成功！');
    } else {
      alert(`保存失败: ${response.message}`);
    }
  } catch (error) {
    console.error('保存模型时出错:', error);
    alert(`保存模型时出错: ${error.message || '请查看控制台'}`);
  }
}

// 分类管理相关方法
// 显示添加分类模态框
function showAddCategoryModal(type) {
  currentCategoryType.value = type;
  currentCategoryIndex.value = -1;
  newCategoryName.value = '';
  showCategoryModal.value = true;
}

// 编辑分类
function editCategory(type, index) {
  currentCategoryType.value = type;
  currentCategoryIndex.value = index;
  
  let categories;
  if (type === 'master') categories = masterCategories.value;
  else if (type === 'image') categories = imageCategories.value;
  else if (type === 'script') categories = scriptCategories.value;
  
  newCategoryName.value = categories[index].name;
  showCategoryModal.value = true;
}

// 删除分类
async function deleteCategory(type, index) {
  let category;
  if (type === 'master') {
    category = masterCategories.value[index];
  } else if (type === 'image') {
    // TODO: Implement image category deletion
    console.warn('Image category deletion not implemented');
    return;
  } else if (type === 'script') {
    // TODO: Implement script category deletion
    console.warn('Script category deletion not implemented');
    return;
  }

  if (confirm(`确定要删除分类 "${category.name}" 吗？`)) {
    try {
      if (type === 'master') {
        const response = await masterAPI.deleteMasterCategory(category.id);
        if (response.success) {
          await loadMasterCategories();
          alert('分类删除成功');
        } else {
          alert(`删除失败: ${response.message}`);
        }
      }
    } catch (error) {
      console.error('删除分类时出错:', error);
      alert(`删除分类时出错: ${error.message || '请查看控制台'}`);
    }
  }
}

// 保存分类
async function saveCategory() {
  if (!newCategoryName.value.trim()) return;

  const type = currentCategoryType.value;
  const index = currentCategoryIndex.value;
  const newCategoryData = {
    name: newCategoryName.value.trim(),
    // description can be added if needed in the form
  };

  try {
    let response;
    if (type === 'master') {
      if (index === -1) {
        // 添加新分类
        response = await masterAPI.createMasterCategory(newCategoryData);
      } else {
        // 更新现有分类
        const categoryId = masterCategories.value[index].id;
        response = await masterAPI.updateMasterCategory(categoryId, newCategoryData);
      }

      if (response.success) {
        await loadMasterCategories();
        showCategoryModal.value = false;
        alert('分类保存成功！');
      } else {
        alert(`保存失败: ${response.message}`);
      }
    } else if (type === 'image') {
      // TODO: Implement image category saving
      console.warn('Image category saving not implemented');
    } else if (type === 'script') {
      // TODO: Implement script category saving
      console.warn('Script category saving not implemented');
    }
  } catch (error) {
    console.error('保存分类时出错:', error);
    alert(`保存分类时出错: ${error.message || '请查看控制台'}`);
  }
}

// 新增：提示词分类管理函数
const fetchPromptCategories = async () => {
  try {
    const response = await settingsAPI.getPromptCategories();
    if (response.success) {
      promptCategories.value = response.data;
    }
  } catch (error) {
    console.error('获取提示词分类失败:', error);
  }
};

const addPromptCategory = async () => {
  if (!newPromptCategoryName.value.trim()) return;
  try {
    const response = await settingsAPI.addPromptCategory({ name: newPromptCategoryName.value });
    if (response.success) {
      promptCategories.value.push(response.data);
      newPromptCategoryName.value = '';
    }
  } catch (error) {
    console.error('添加提示词分类失败:', error);
  }
};

const deletePromptCategory = async (id) => {
  if (!confirm('确定要删除这个分类吗？')) return;
  try {
    await settingsAPI.deletePromptCategory(id);
    promptCategories.value = promptCategories.value.filter(c => c.id !== id);
  } catch (error) {
    alert(error.response?.data?.message || '删除失败');
    console.error('删除提示词分类失败:', error);
  }
};

const startCategoryEdit = (category) => {
  editingCategoryId.value = category.id;
  editingCategoryName.value = category.name;
};

const cancelCategoryEdit = () => {
  editingCategoryId.value = null;
  editingCategoryName.value = '';
};

const saveCategoryEdit = async (id) => {
  if (!editingCategoryName.value.trim()) return;
  try {
    const response = await settingsAPI.updatePromptCategory(id, { name: editingCategoryName.value });
    if (response.success) {
      const index = promptCategories.value.findIndex(c => c.id === id);
      if (index !== -1) {
        promptCategories.value[index] = response.data;
      }
      cancelCategoryEdit();
    }
  } catch (error) {
    console.error('更新提示词分类失败:', error);
  }
};

// 新增：拖拽排序相关函数
const handleDragStart = (index) => {
  draggingIndex.value = index;
};

const handleDrop = (targetIndex) => {
  if (draggingIndex.value === null) return;

  const itemToMove = promptCategories.value.splice(draggingIndex.value, 1)[0];
  promptCategories.value.splice(targetIndex, 0, itemToMove);
  
  draggedOverIndex.value = null;
};

const handleDragEnd = async () => {
  if (draggingIndex.value !== null) {
    draggingIndex.value = null;
    const orderedIds = promptCategories.value.map(c => c.id);
    try {
      await settingsAPI.updatePromptCategoryOrder({ orderedIds });
    } catch (error) {
      console.error('更新分类排序失败:', error);
      // 可选：如果失败，恢复到原始顺序
      fetchPromptCategories(); 
    }
  }
};

function fetchCategories(type) {
  switch (type) {
    case 'master':
      loadMasterCategories();
      break;
    case 'image':
      loadImageCategories();
      break;
    case 'script':
      loadScriptCategories();
      break;
    case 'prompt':
      fetchPromptCategories();
      break;
  }
}
</script>

<style scoped>
.settings-view {
  padding: 12px 20px;
  height: 100%;
  /* 移除overflow-y: auto，避免双滚动条问题 */
}

.page-title {
  font-size: 1.3rem;
  margin-bottom: 0.2rem;
  color: var(--color-heading);
}

.page-description {
  color: var(--color-text-light);
  margin-bottom: 1rem;
  font-size: 0.85rem;
}

.settings-container {
  display: flex;
  background-color: var(--color-background-soft);
  border-radius: 8px;
  padding: 0;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  height: 100%;
}

.settings-content {
  flex: 1;
  padding: 30px;
  width: 100%;
  overflow-y: auto;
}

.section-title {
  font-size: 1.5rem;
  margin-bottom: 1.5rem;
  color: var(--color-heading);
  padding-bottom: 0.5rem;
  border-bottom: 1px solid var(--color-border);
}

.setting-group {
  margin-bottom: 2rem;
  background-color: white;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
}

.group-title {
  font-size: 1.1rem;
  margin-bottom: 1rem;
  color: var(--color-heading);
}

.setting-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 0;
  border-bottom: 1px solid var(--color-border-light);
}

.setting-item:last-child {
  border-bottom: none;
}

.setting-label {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.setting-description {
  font-size: 0.85rem;
  color: var(--color-text-light);
}

.setting-control {
  min-width: 200px;
}

/* 颜色选择器 */
.color-picker {
  display: flex;
  gap: 10px;
}

.color-option {
  width: 24px;
  height: 24px;
  border-radius: 50%;
  cursor: pointer;
  transition: transform 0.2s;
  border: 2px solid transparent;
}

.color-option:hover {
  transform: scale(1.1);
}

.color-option.active {
  border-color: var(--color-text);
  transform: scale(1.1);
}

/* 范围控制 */
.range-control {
  display: flex;
  align-items: center;
  gap: 10px;
}

.range-control input[type="range"] {
  flex: 1;
}

.range-value {
  min-width: 40px;
  text-align: right;
}

/* 开关控件 */
.toggle {
  position: relative;
  display: inline-block;
  width: 50px;
  height: 24px;
}

.toggle input {
  opacity: 0;
  width: 0;
  height: 0;
}

.toggle-slider {
  position: absolute;
  cursor: pointer;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: #ccc;
  transition: .4s;
  border-radius: 24px;
}

.toggle-slider:before {
  position: absolute;
  content: "";
  height: 16px;
  width: 16px;
  left: 4px;
  bottom: 4px;
  background-color: white;
  transition: .4s;
  border-radius: 50%;
}

input:checked + .toggle-slider {
  background-color: var(--color-primary);
}

input:checked + .toggle-slider:before {
  transform: translateX(26px);
}

/* 存储统计 */
.storage-stats {
  display: flex;
  justify-content: space-between;
  margin-bottom: 15px;
}

.stat-item {
  text-align: center;
  flex: 1;
}

.stat-label {
  font-size: 0.9rem;
  color: var(--color-text-light);
  margin-bottom: 5px;
}

.stat-value {
  font-size: 1.2rem;
  font-weight: 500;
}

.storage-usage {
  margin-bottom: 20px;
}

.usage-bar {
  height: 8px;
  background-color: var(--color-background-mute);
  border-radius: 4px;
  overflow: hidden;
  margin-bottom: 5px;
}

.usage-fill {
  height: 100%;
  background-color: var(--color-primary);
  border-radius: 4px;
}

.usage-text {
  text-align: right;
  font-size: 0.9rem;
  color: var(--color-text-light);
}

/* 分类管理样式 */
.category-tabs {
  display: flex;
  gap: 10px;
  margin-bottom: 20px;
  overflow-x: auto;
  padding-bottom: 5px;
}

.tab-btn {
  padding: 8px 16px;
  border: none;
  border-radius: 4px;
  background-color: var(--color-background-mute);
  color: var(--color-text);
  cursor: pointer;
  white-space: nowrap;
  transition: background-color 0.2s, color 0.2s;
}

.tab-btn.active {
  background-color: var(--primary-color);
  color: white;
}

.categories-list {
  display: flex;
  flex-direction: column;
  gap: 10px;
  margin-top: 10px;
}

.category-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px;
  border: 1px solid #ddd;
  border-radius: 8px;
  margin-bottom: 8px;
  background: white;
  transition: all 0.2s ease;
}

.category-item:hover {
  border-color: #007bff;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.category-info {
  display: flex;
  align-items: center;
  flex: 1;
  min-width: 0;
}

.drag-handle {
  cursor: grab;
  margin-right: 12px;
  color: #aaa;
  font-size: 16px;
  flex-shrink: 0;
  width: 20px;
  text-align: center;
}

.category-content {
  display: flex;
  align-items: center;
  gap: 15px;
  flex: 1;
  min-width: 0;
}

.category-name {
  font-weight: 500;
  flex: 1;
  min-width: 0;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.category-count {
  color: #666;
  font-size: 0.9em;
  flex-shrink: 0;
  white-space: nowrap;
}

.category-actions {
  display: flex;
  gap: 8px;
  flex-shrink: 0;
}

.btn-icon {
  background: none;
  border: none;
  cursor: pointer;
  padding: 6px;
  border-radius: 4px;
  transition: background-color 0.2s;
  font-size: 14px;
}

.btn-icon:hover {
  background-color: #f0f0f0;
}

.category-item.dragging {
  opacity: 0.5;
  background: #f0f8ff;
  transform: rotate(2deg);
}

.edit-input {
  flex: 1;
  margin-right: 10px;
}

.categories-list-container {
  margin-top: 15px;
}

.add-category-form {
  display: flex;
  gap: 10px;
  margin-bottom: 15px;
}

.add-category-form input {
  flex: 1;
}

.empty-state {
  text-align: center;
  padding: 20px;
  color: #666;
  font-style: italic;
}

.usage-stats {
  display: flex;
  justify-content: space-between;
  margin-bottom: 20px;
}

.modal-stats {
  background-color: var(--color-background-soft);
  padding: 15px;
  border-radius: 8px;
  margin-top: 10px;
}

.usage-chart {
  background-color: var(--color-background-soft);
  border-radius: 8px;
  padding: 20px;
  height: 200px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--color-text-light);
  border: 1px dashed var(--color-border);
}

.chart-placeholder {
  font-style: italic;
}

.btn-add:hover {
  border-color: var(--color-primary);
  color: var(--color-primary);
  background-color: rgba(0, 119, 204, 0.05);
}

.btn-add span {
  font-size: 1.2rem;
  font-weight: bold;
}

.form-group {
  margin-bottom: 15px;
}

.form-group h4 {
  margin-bottom: 10px;
  font-size: 1rem;
  color: var(--color-heading);
}

.form-group label {
  display: block;
  margin-bottom: 8px;
  font-weight: 500;
}

.checkbox-label {
  display: flex;
  align-items: center;
  gap: 8px;
  cursor: pointer;
}

.checkbox-label input[type="checkbox"] {
  margin: 0;
}

.form-input {
  width: 100%;
  padding: 8px 12px;
  border: 1px solid var(--color-border);
  border-radius: 4px;
  font-size: 1rem;
  transition: border-color 0.2s;
}

.form-input:focus {
  border-color: var(--color-primary);
  outline: none;
}

.btn-primary {
  background-color: #0077cc;
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 4px;
  cursor: pointer;
  transition: background-color 0.2s;
}

.btn-primary:hover {
  background-color: #0066b3;
}

.btn-secondary {
  background-color: #f0f0f0;
  color: var(--color-text);
  border: 1px solid var(--color-border);
  padding: 8px 16px;
  border-radius: 4px;
  cursor: pointer;
  transition: background-color 0.2s;
}

.btn-secondary:hover {
  background-color: #e0e0e0;
}

.storage-actions, .setting-actions, .about-actions {
  display: flex;
  gap: 10px;
  margin-top: 15px;
}

/* 关于页面 */
.about-app {
  text-align: center;
  margin-bottom: 30px;
}

.app-logo {
  font-size: 3rem;
  font-weight: bold;
  color: var(--color-primary);
  margin-bottom: 10px;
}

.app-name {
  font-size: 1.2rem;
  margin-bottom: 5px;
}

.app-version {
  color: var(--color-text-light);
  margin-bottom: 5px;
}

.about-section {
  margin-bottom: 20px;
}

.about-section h3 {
  font-size: 1.1rem;
  margin-bottom: 10px;
  color: var(--color-heading);
}

.tech-stack {
  list-style-type: disc;
  padding-left: 20px;
}

.tech-stack li {
  margin-bottom: 5px;
}

/* 默认标签样式 */
.default-tag {
  display: inline-block;
  width: 40px;
  height: 24px;
  border-radius: 4px;
  background-color: transparent;
  text-align: center;
  line-height: 24px;
  border: none;
}

.default-tag.is-default {
  background-color: var(--color-primary, #4a6baf);
  color: white;
  border-color: var(--color-primary, #4a6baf);
}

/* 模态框样式 */
.modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.modal-content {
  background-color: white;
  border-radius: 8px;
  width: 90%;
  max-width: 600px;
  max-height: 90vh;
  overflow-y: auto;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px 20px;
  border-bottom: 1px solid var(--color-border);
}

.modal-body {
  padding: 20px;
}

.modal-footer {
  padding: 15px 20px;
  border-top: 1px solid var(--color-border);
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}

.close-btn {
  background: none;
  border: none;
  font-size: 1.5rem;
  cursor: pointer;
  color: var(--color-text-light);
}

/* 导入数据模态框 */
.import-instructions {
  margin-bottom: 20px;
}

.warning {
  color: var(--color-danger);
  font-weight: 500;
}

.file-upload {
  margin-bottom: 20px;
}

.file-upload-label {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 30px;
  border: 2px dashed var(--color-border);
  border-radius: 8px;
  cursor: pointer;
  transition: border-color 0.2s, background-color 0.2s;
}

.file-upload-label:hover {
  border-color: var(--color-primary);
  background-color: rgba(0, 119, 204, 0.05);
}

.upload-icon {
  font-size: 2rem;
  margin-bottom: 10px;
}

.file-input {
  display: none;
}

.selected-file {
  margin-top: 10px;
  padding: 10px;
  background-color: var(--color-background-soft);
  border-radius: 4px;
}

/* 清除数据确认模态框 */
.danger-warning {
  text-align: center;
  margin-bottom: 20px;
}

.warning-icon {
  font-size: 3rem;
  margin-bottom: 10px;
  color: var(--color-danger);
}

.danger-warning h4 {
  color: var(--color-danger);
  margin-bottom: 10px;
}

.danger-warning ul {
  text-align: left;
  margin: 10px 0;
  padding-left: 20px;
}

.confirmation-input {
  margin-top: 20px;
}

.confirmation-input label {
  display: block;
  margin-bottom: 10px;
  font-weight: 500;
}

.btn-danger {
  background-color: #dc3545;
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 4px;
  cursor: pointer;
  transition: background-color 0.2s;
}

.btn-danger:hover {
  background-color: #bd2130;
}

.btn-danger:disabled {
  background-color: #dc354580;
  cursor: not-allowed;
}

/* 响应式调整 */
@media (max-width: 768px) {
  .settings-container {
    flex-direction: column;
  }
  
  .settings-sidebar {
    width: 100%;
    padding: 10px;
  }
  
  .settings-nav {
    flex-direction: row;
    overflow-x: auto;
    padding-bottom: 10px;
  }
  
  .nav-item {
    padding: 10px 15px;
    border-left: none;
    border-bottom: 3px solid transparent;
  }
  
  .nav-item.active {
    border-left-color: transparent;
    border-bottom-color: var(--color-primary);
  }
  
  .settings-content {
    max-width: 100%;
    padding: 20px;
  }
  
  .setting-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 10px;
  }
  
  .setting-control {
    width: 100%;
  }
  
  .storage-stats {
    flex-direction: column;
    gap: 10px;
  }
  
  .stat-item {
    text-align: left;
  }
}

/* API配置样式 */
.api-url-control {
  display: flex;
  gap: 10px;
  align-items: center;
  margin-bottom: 10px;
}

.api-url-control .form-input {
  flex: 1;
}

.api-url-info {
  font-size: 0.9rem;
  color: var(--color-text-light);
  background-color: var(--color-background-soft);
  padding: 10px;
  border-radius: 4px;
}

.api-url-info p {
  margin: 5px 0;
}

.api-url-info ul {
  margin: 5px 0;
  padding-left: 20px;
}

.api-url-info li {
  margin: 2px 0;
}

.drag-handle {
  cursor: grab;
  margin-right: 10px;
  color: #aaa;
  font-size: 14px;
  flex-shrink: 0;
}

.category-item.dragging {
  opacity: 0.5;
  background: #f0f8ff;
  transform: rotate(2deg);
}

.edit-input {
  flex-grow: 1;
  margin-right: 10px;
}

.categories-list-container {
  margin-top: 15px;
}

.add-category-form {
  display: flex;
  gap: 10px;
  margin-bottom: 15px;
}

.add-category-form input {
  flex: 1;
}

.empty-state {
  text-align: center;
  padding: 20px;
  color: #666;
  font-style: italic;
}

.category-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px;
  border: 1px solid #ddd;
  border-radius: 4px;
  margin-bottom: 8px;
  background: white;
  transition: all 0.2s ease;
}

.category-item:hover {
  border-color: #007bff;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.category-info {
  display: flex;
  align-items: center;
  flex: 1;
}

.category-content {
  display: flex;
  align-items: center;
  gap: 10px;
  flex: 1;
}

.category-name {
  font-weight: 500;
}

.category-count {
  color: #666;
  font-size: 0.9em;
}

.category-actions {
  display: flex;
  gap: 5px;
}

.btn-icon {
  background: none;
  border: none;
  cursor: pointer;
  padding: 5px;
  border-radius: 3px;
  transition: background-color 0.2s;
}

.btn-icon:hover {
  background-color: #f0f0f0;
}

/* AI设置页面样式 */
.group-description {
  margin: 0 0 20px 0;
  color: var(--text-secondary);
  font-size: 14px;
  line-height: 1.5;
}

.api-status {
  margin-top: 16px;
  padding: 12px;
  background: var(--bg-secondary);
  border-radius: 8px;
  border-left: 4px solid var(--primary-color);
}

.status-indicator {
  font-weight: 500;
  color: var(--text-secondary);
}

.status-indicator.configured {
  color: var(--success-color, #22c55e);
}

.status-hint {
  display: block;
  margin-top: 8px;
  color: var(--text-secondary);
  font-size: 12px;
  line-height: 1.4;
}

.models-list-header {
  display: grid;
  grid-template-columns: 2fr 1fr 1.5fr 1fr;
  gap: 10px;
  padding: 0 10px 10px 10px;
  border-bottom: 1px solid var(--color-border-light);
  font-weight: 500;
  color: var(--color-text-light);
}

.model-item {
  display: grid;
  grid-template-columns: 2fr 1fr 1.5fr 1fr;
  gap: 10px;
  padding: 15px 10px;
  border-bottom: 1px solid var(--color-border-light);
  align-items: center;
}

.model-item:last-child {
  border-bottom: none;
}

.model-name {
  font-weight: 500;
}

.model-type {
  color: var(--color-text-light);
}

.model-default {
  text-align: center;
  display: flex;
  flex-direction: column;
  gap: 5px;
  align-items: center;
}

.default-tag {
  display: inline-block;
  padding: 4px 12px;
  border-radius: 12px;
  font-size: 0.9rem;
  cursor: pointer;
  transition: all 0.2s ease;
  background-color: var(--color-background-soft);
  color: var(--color-text-light);
}

.default-tag.is-default {
  background-color: var(--color-primary);
  color: white;
  font-weight: 500;
}

.btn-set-default {
  background: var(--color-primary);
  color: white;
  border: none;
  border-radius: 4px;
  padding: 4px 8px;
  font-size: 0.8rem;
  cursor: pointer;
  transition: background-color 0.2s;
}

.btn-set-default:hover {
  background: var(--color-primary-dark);
}

.model-actions {
  display: flex;
  justify-content: flex-end;
  gap: 5px;
}

.add-model {
  padding: 15px 10px;
  text-align: center;
}

.btn-add {
  background: none;
  border: 2px dashed var(--color-border);
  border-radius: 4px;
  padding: 8px 16px;
  color: var(--color-primary);
  cursor: pointer;
  transition: all 0.2s;
  width: 100%;
}

.btn-add:hover {
  background-color: rgba(0, 119, 204, 0.05);
  border-color: var(--color-primary);
}

/* 本地RAG样式 */
.local-rag-status {
  margin: 20px 0;
}

.status-card {
  border: 1px solid var(--color-border);
  border-radius: 8px;
  padding: 20px;
  background: var(--color-background-soft);
}

.status-card.status-available {
  border-color: #52c41a;
  background: rgba(82, 196, 26, 0.05);
}

.status-card.status-unavailable {
  border-color: #faad14;
  background: rgba(250, 173, 20, 0.05);
}

.status-header {
  display: flex;
  align-items: center;
  gap: 10px;
  margin-bottom: 15px;
  font-weight: 500;
  font-size: 16px;
}

.status-icon {
  font-size: 18px;
}

.status-details {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 10px;
}

.status-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 0;
}

.status-label {
  color: var(--text-secondary);
  font-size: 14px;
}

.status-value {
  font-weight: 500;
  font-size: 14px;
}

.status-value.available {
  color: #52c41a;
}

.status-value.unavailable {
  color: #faad14;
}

.local-rag-actions {
  display: flex;
  gap: 10px;
  margin: 20px 0;
}

.btn-primary, .btn-secondary {
  padding: 10px 20px;
  border-radius: 6px;
  border: none;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  transition: all 0.2s ease;
}

.btn-primary {
  background: var(--color-primary);
  color: white;
}

.btn-primary:hover:not(:disabled) {
  background: var(--color-primary-dark);
}

.btn-primary:disabled {
  background: #ccc;
  cursor: not-allowed;
}

.btn-secondary {
  background: var(--color-background-soft);
  color: var(--color-text);
  border: 1px solid var(--color-border);
}

.btn-secondary:hover:not(:disabled) {
  background: var(--color-border-light);
}

.btn-secondary:disabled {
  background: #f5f5f5;
  color: #ccc;
  cursor: not-allowed;
}

.local-rag-features {
  margin-top: 20px;
}

.local-rag-features h4 {
  margin: 0 0 10px 0;
  color: var(--color-text);
  font-size: 16px;
  font-weight: 500;
}

.features-list {
  list-style: none;
  padding: 0;
  margin: 0;
}

.features-list li {
  padding: 8px 0;
  color: var(--text-secondary);
  font-size: 14px;
  line-height: 1.5;
}

/* Midjourney样式 */
.midjourney-status {
  margin: 20px 0;
}

.midjourney-config {
  margin: 20px 0;
}

.config-item {
  margin-bottom: 15px;
}

.config-item label {
  display: block;
  margin-bottom: 5px;
  font-weight: 500;
  color: var(--color-text);
}

.config-item input {
  width: 100%;
  padding: 10px;
  border: 1px solid var(--color-border);
  border-radius: 6px;
  font-size: 14px;
  background: var(--color-background);
  color: var(--color-text);
}

.config-item input:focus {
  outline: none;
  border-color: var(--color-primary);
  box-shadow: 0 0 0 2px rgba(0, 119, 204, 0.1);
}

.midjourney-actions {
  display: flex;
  gap: 10px;
  margin: 20px 0;
}

.midjourney-features {
  margin-top: 20px;
}

.midjourney-guide {
  margin-top: 20px;
  padding: 15px;
  background: var(--color-background-soft);
  border-radius: 8px;
  border-left: 4px solid var(--color-primary);
}

.midjourney-guide h4 {
  margin: 0 0 10px 0;
  color: var(--color-text);
  font-size: 16px;
  font-weight: 500;
}

.guide-list {
  list-style: decimal;
  padding-left: 20px;
  margin: 0;
}

.guide-list li {
  padding: 5px 0;
  color: var(--text-secondary);
  font-size: 14px;
  line-height: 1.5;
}
.demo-mode-notice {
  margin-top: 20px;
  padding: 15px;
  background: #e8f4fd;
  border: 1px solid #bee5eb;
  border-radius: 8px;
  border-left: 4px solid #17a2b8;
}

.demo-mode-notice h4 {
  margin: 0 0 10px 0;
  color: #0c5460;
  font-size: 16px;
  font-weight: 500;
}

.demo-mode-notice p {
  margin: 0 0 10px 0;
  color: #0c5460;
  font-size: 14px;
  line-height: 1.5;
}

.demo-mode-notice ul {
  margin: 10px 0;
  padding-left: 20px;
}

.demo-mode-notice li {
  margin: 5px 0;
  color: #0c5460;
  font-size: 14px;
}
</style>