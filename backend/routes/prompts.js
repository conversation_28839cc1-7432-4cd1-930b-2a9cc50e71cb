/**
 * 提示词库管理路由
 * 
 * 功能说明：
 * - 提示词信息管理
 * - 提示词分类管理
 */

const express = require('express');
const router = express.Router();

const db = require('../config/database');
const { validate, promptItemSchema, paginationSchema } = require('../middleware/validation');
const { authenticateToken, requireAdmin } = require('../middleware/auth');
const { asyncHand<PERSON>, ValidationError, NotFoundError, AuthorizationError, createSuccessResponse } = require('../middleware/errorHandler');

// ==================== 分类管理 ====================

/**
 * @route GET /api/prompts/categories
 * @desc 获取提示词库分类列表
 * @access Private
 */
router.get('/categories', authenticateToken, asyncHandler(async (req, res) => {
  const { categoryId } = req.query;
  let categories;
  if (categoryId) {
    categories = await db.execute(
      `SELECT pc.id, pc.name, pc.description, pc.created_at, pc.updated_at,
              COUNT(pi.id) as prompt_count
       FROM prompt_categories pc
       LEFT JOIN prompt_items pi ON pc.id = pi.category_id
       WHERE pc.id = ?
       GROUP BY pc.id
       ORDER BY pc.list_order ASC`,
      [categoryId]
    );
  } else {
    categories = await db.execute(
      `SELECT pc.id, pc.name, pc.description, pc.created_at, pc.updated_at,
              COUNT(pi.id) as prompt_count
       FROM prompt_categories pc
       LEFT JOIN prompt_items pi ON pc.id = pi.category_id
       GROUP BY pc.id
       ORDER BY pc.list_order ASC`
    );
  }
  res.json(createSuccessResponse(categories));
}));

/**
 * @route POST /api/prompts/categories
 * @desc 创建提示词库分类
 * @access Private (Admin only)
 */
router.post('/categories', authenticateToken, requireAdmin, asyncHandler(async (req, res) => {
  const { name, description } = req.body;
  
  if (!name || name.trim().length === 0) {
    throw new ValidationError('分类名称不能为空');
  }
  
  // 检查分类名称是否已存在
  const existing = await db.execute(
    'SELECT id FROM prompt_categories WHERE name = ?',
    [name.trim()]
  );
  
  if (existing.length > 0) {
    throw new ValidationError('分类名称已存在');
  }
  
  // 创建分类
  const result = await db.execute(
    'INSERT INTO prompt_categories (id, name, description, created_at, updated_at) VALUES (?, ?, ?, NOW(), NOW())',
    [name.toLowerCase().replace(/\s+/g, '_'), name.trim(), description || '']
  );
  
  // 获取创建的分类信息
  const categories = await db.execute(
    'SELECT id, name, description, created_at, updated_at FROM prompt_categories WHERE id = ?',
    [name.toLowerCase().replace(/\s+/g, '_')]
  );
  
  res.status(201).json(createSuccessResponse(categories[0], '分类创建成功'));
}));

/**
 * @route PUT /api/prompts/categories/:id
 * @desc 更新提示词库分类
 * @access Private (Admin only)
 */
router.put('/categories/:id', authenticateToken, requireAdmin, asyncHandler(async (req, res) => {
  const categoryId = req.params.id;
  const { name, description } = req.body;
  
  // 检查分类是否存在
  const categories = await db.execute(
    'SELECT id FROM prompt_categories WHERE id = ?',
    [String(categoryId)],
  );
  
  if (categories.length === 0) {
    throw new NotFoundError('分类不存在');
  }
  
  // 构建更新语句
  const updateFields = [];
  const updateValues = [];
  
  if (name !== undefined) {
    updateFields.push('name = ?');
    updateValues.push(name.trim());
  }
  
  if (description !== undefined) {
    updateFields.push('description = ?');
    updateValues.push(description);
  }
  
  if (updateFields.length > 0) {
    updateFields.push('updated_at = NOW()');
    updateValues.push(categoryId);
    
    await db.execute(
      `UPDATE prompt_categories SET ${updateFields.join(', ')} WHERE id = ?`,
      updateValues
    );
  }
  
  res.json(createSuccessResponse(null, '分类更新成功'));
}));

/**
 * @route DELETE /api/prompts/categories/:id
 * @desc 删除提示词库分类
 * @access Private (Admin only)
 */
router.delete('/categories/:id', authenticateToken, requireAdmin, asyncHandler(async (req, res) => {
  const categoryId = req.params.id;
  
  // 检查分类是否存在
  const categories = await db.execute(
    'SELECT id FROM prompt_categories WHERE id = ?',
    [String(categoryId)],
  );
  
  if (categories.length === 0) {
    throw new NotFoundError('分类不存在');
  }
  
  // 检查是否有关联的提示词
  const prompts = await db.execute(
    'SELECT COUNT(*) as count FROM prompt_items WHERE category_id = ?',
    [String(categoryId)],
  );
  
  if (prompts[0].count > 0) {
    throw new ValidationError('无法删除包含提示词的分类');
  }
  
  // 删除分类
  await db.execute(
    'DELETE FROM prompt_categories WHERE id = ?',
    [String(categoryId)],
  );
  
  res.json(createSuccessResponse(null, '分类删除成功'));
}));

// ==================== 提示词信息管理 ====================

/**
 * @route GET /api/prompts
 * @desc 获取提示词列表
 * @access Private
 */
router.get('/', authenticateToken, validate(paginationSchema, 'query'), asyncHandler(async (req, res) => {
  const { page, limit, sortBy, sortOrder, search, categoryId } = req.query;
  const offset = (page - 1) * limit;
  
  // --- 调试日志 ---
  console.log('[DEBUG] Request to /api/prompts');
  console.log('[DEBUG] Query Parameters:', req.query);
  console.log(`[DEBUG] Received categoryId: ${categoryId} (Type: ${typeof categoryId})`);
  
  // 构建查询条件
  let whereClause = 'WHERE 1=1';
  let queryParams = [];
  
  if (search) {
    whereClause += ' AND (pi.title LIKE ? OR pi.description LIKE ? OR pi.content LIKE ?)';
    queryParams.push(`%${search}%`, `%${search}%`, `%${search}%`);
  }
  
  if (categoryId) {
    whereClause += ' AND pi.category_id = ?';
    queryParams.push(categoryId);
  }
  
  // --- 调试日志 ---
  console.log(`[DEBUG] SQL Where Clause: ${whereClause}`);
  console.log(`[DEBUG] SQL Query Params: ${JSON.stringify(queryParams)}`);

  // 获取提示词列表
  const prompts = await db.execute(
    `SELECT pi.id, pi.title, pi.description, pi.content, pi.is_public, pi.usage_count, pi.created_at, pi.updated_at,
            pc.name as category_name,
            u.display_name as creator_name
     FROM prompt_items pi
     LEFT JOIN prompt_categories pc ON pi.category_id = pc.id
     LEFT JOIN users u ON pi.created_by = u.id
     ${whereClause}
     ORDER BY pi.${sortBy} ${sortOrder}
     LIMIT ? OFFSET ?`,
     [...queryParams, String(limit), String(offset)]
  );
  
  // 获取总数
  const countResult = await db.execute(
    `SELECT COUNT(*) as total
     FROM prompt_items pi
     ${whereClause}`,
    queryParams
  );
  
  const total = countResult[0].total;
  const totalPages = Math.ceil(total / limit);
  
  res.json(createSuccessResponse(prompts, '获取提示词列表成功', {
    pagination: {
      page,
      limit,
      total,
      totalPages,
      hasNext: page < totalPages,
      hasPrev: page > 1
    }
  }));
}));

/**
 * @route GET /api/prompts/:id
 * @desc 获取提示词详情
 * @access Private
 */
router.get('/:id', authenticateToken, asyncHandler(async (req, res) => {
  const promptId = req.params.id;
  
  // 获取提示词详情
  const prompts = await db.execute(
    `SELECT pi.id, pi.title, pi.description, pi.content, pi.is_public, pi.usage_count, pi.category_id, pi.created_by, pi.created_at, pi.updated_at,
            pc.name as category_name,
            u.display_name as creator_name
     FROM prompt_items pi
     LEFT JOIN prompt_categories pc ON pi.category_id = pc.id
     LEFT JOIN users u ON pi.created_by = u.id
     WHERE pi.id = ?`,
    [promptId]
  );
  
  if (prompts.length === 0) {
    throw new NotFoundError('提示词不存在');
  }
  
  const prompt = prompts[0];
  
  // 检查访问权限
  if (!prompt.is_public && prompt.created_by !== req.user.id && req.user.role !== 'admin') {
    throw new AuthorizationError('无权访问此提示词');
  }
  
  // 增加使用次数
  await db.execute(
    'UPDATE prompt_items SET usage_count = usage_count + 1 WHERE id = ?',
    [promptId]
  );
  
  res.json(createSuccessResponse(prompt));
}));

/**
 * @route POST /api/prompts
 * @desc 创建提示词
 * @access Private
 */
router.post('/', authenticateToken, validate(promptItemSchema), asyncHandler(async (req, res) => {
  const { title, categoryId, description, content, isPublic } = req.body;
  
  // 验证分类是否存在
  const categories = await db.execute(
    'SELECT id FROM prompt_categories WHERE id = ?',
    [String(categoryId)],
  );
  
  if (categories.length === 0) {
    throw new ValidationError('指定的分类不存在');
  }
  
  // 检查提示词标题是否已存在（同一用户下）
  const existing = await db.execute(
    'SELECT id FROM prompt_items WHERE title = ? AND created_by = ?',
    [title, req.user.id]
  );
  
  if (existing.length > 0) {
    throw new ValidationError('您已有同名的提示词');
  }
  
  // 创建提示词
  const result = await db.execute(
    `INSERT INTO prompt_items (title, category_id, description, content, is_public, created_by, created_at, updated_at)
     VALUES (?, ?, ?, ?, ?, ?, NOW(), NOW())`,
    [title, categoryId, description, content, isPublic || false, req.user.id]
  );
  
  const promptId = result.insertId;
  
  // 获取创建的提示词信息
  const prompts = await db.execute(
    `SELECT pi.id, pi.title, pi.description, pi.content, pi.is_public, pi.usage_count, pi.created_at, pi.updated_at,
            pc.name as category_name,
            u.display_name as creator_name
     FROM prompt_items pi
     LEFT JOIN prompt_categories pc ON pi.category_id = pc.id
     LEFT JOIN users u ON pi.created_by = u.id
     WHERE pi.id = ?`,
    [promptId]
  );
  
  res.status(201).json(createSuccessResponse(prompts[0], '提示词创建成功'));
}));

/**
 * @route PUT /api/prompts/:id
 * @desc 更新提示词
 * @access Private
 */
router.put('/:id', authenticateToken, asyncHandler(async (req, res) => {
  const promptId = req.params.id;
  const { title, categoryId, description, content, isPublic } = req.body;
  
  // 检查提示词是否存在和权限
  const prompts = await db.execute(
    'SELECT created_by FROM prompt_items WHERE id = ?',
    [promptId]
  );
  
  if (prompts.length === 0) {
    throw new NotFoundError('提示词不存在');
  }
  
  if (prompts[0].created_by !== req.user.id && req.user.role !== 'admin') {
    throw new AuthorizationError('无权修改此提示词');
  }
  
  // 验证分类是否存在
  if (categoryId) {
    const [categories] = await db.execute(
      'SELECT id FROM prompt_categories WHERE id = ?',
      [String(categoryId)],
    );
    
    if (categories.length === 0) {
      throw new ValidationError('指定的分类不存在');
    }
  }
  
  // 检查标题是否与其他提示词冲突（同一用户下）
  if (title) {
    const existing = await db.execute(
      'SELECT id FROM prompt_items WHERE title = ? AND created_by = ? AND id != ?',
      [title, req.user.id, promptId]
    );
    
    if (existing.length > 0) {
      throw new ValidationError('您已有同名的提示词');
    }
  }
  
  // 更新提示词
  const updateFields = [];
  const updateValues = [];
  
  if (title !== undefined) {
    updateFields.push('title = ?');
    updateValues.push(title);
  }
  
  if (categoryId !== undefined) {
    updateFields.push('category_id = ?');
    updateValues.push(categoryId);
  }
  
  if (description !== undefined) {
    updateFields.push('description = ?');
    updateValues.push(description);
  }
  
  if (content !== undefined) {
    updateFields.push('content = ?');
    updateValues.push(content);
  }
  
  if (isPublic !== undefined) {
    updateFields.push('is_public = ?');
    updateValues.push(isPublic);
  }
  
  if (updateFields.length > 0) {
    updateFields.push('updated_at = NOW()');
    updateValues.push(promptId);
    
    await db.execute(
      `UPDATE prompt_items SET ${updateFields.join(', ')} WHERE id = ?`,
      updateValues
    );
  }
  
  res.json(createSuccessResponse(null, '提示词更新成功'));
}));

/**
 * @route DELETE /api/prompts/:id
 * @desc 删除提示词
 * @access Private
 */
router.delete('/:id', authenticateToken, asyncHandler(async (req, res) => {
  const promptId = req.params.id;
  
  // 检查提示词是否存在和权限
  const prompts = await db.execute(
    'SELECT created_by FROM prompt_items WHERE id = ?',
    [promptId]
  );
  
  if (prompts.length === 0) {
    throw new NotFoundError('提示词不存在');
  }
  
  if (prompts[0].created_by !== req.user.id && req.user.role !== 'admin') {
    throw new AuthorizationError('无权删除此提示词');
  }
  
  // 开始事务
  const connection = await db.getConnection();
  await db.beginTransaction(connection);
  
  try {
    // 删除消息引用
    await db.execute(
      'DELETE FROM message_references WHERE reference_type = "prompt" AND reference_id = ?',
      [promptId],
      connection
    );
    
    // 删除提示词
    await db.execute(
      'DELETE FROM prompt_items WHERE id = ?',
      [promptId],
      connection
    );
    
    await db.commitTransaction(connection);
    
    res.json(createSuccessResponse(null, '提示词删除成功'));
  } catch (error) {
    await db.rollbackTransaction(connection);
    throw error;
  }
}));

/**
 * @route POST /api/prompts/:id/copy
 * @desc 复制提示词
 * @access Private
 */
router.post('/:id/copy', authenticateToken, asyncHandler(async (req, res) => {
  const promptId = req.params.id;
  const { title } = req.body;
  
  // 获取原提示词信息
  const [prompts] = await db.execute(
    'SELECT title, category_id, description, content FROM prompt_items WHERE id = ? AND (is_public = true OR created_by = ?)',
    [promptId, req.user.id]
  );
  
  if (prompts.length === 0) {
    throw new NotFoundError('提示词不存在或无权访问');
  }
  
  const originalPrompt = prompts[0];
  const newTitle = title || `${originalPrompt.title} - 副本`;
  
  // 检查新标题是否已存在（同一用户下）
  const existing = await db.execute(
    'SELECT id FROM prompt_items WHERE title = ? AND created_by = ?',
    [newTitle, req.user.id]
  );
  
  if (existing.length > 0) {
    throw new ValidationError('您已有同名的提示词');
  }
  
  // 创建副本
  const result = await db.execute(
    `INSERT INTO prompt_items (title, category_id, description, content, is_public, created_by, created_at, updated_at)
     VALUES (?, ?, ?, ?, false, ?, NOW(), NOW())`,
    [newTitle, originalPrompt.category_id, originalPrompt.description, originalPrompt.content, req.user.id]
  );
  
  const newPromptId = result.insertId;
  
  // 获取创建的提示词信息
  const newPrompts = await db.execute(
    `SELECT pi.id, pi.title, pi.description, pi.content, pi.is_public, pi.usage_count, pi.created_at, pi.updated_at,
            pc.name as category_name,
            u.display_name as creator_name
     FROM prompt_items pi
     LEFT JOIN prompt_categories pc ON pi.category_id = pc.id
     LEFT JOIN users u ON pi.created_by = u.id
     WHERE pi.id = ?`,
    [newPromptId]
  );
  
  res.status(201).json(createSuccessResponse(newPrompts[0], '提示词复制成功'));
}));

/**
 * @route GET /api/prompts/stats
 * @desc 获取提示词库统计信息
 * @access Private
 */
router.get('/stats', authenticateToken, asyncHandler(async (req, res) => {
  const userId = req.user.id;
  
  // 获取统计信息
  const stats = await db.execute(
    `SELECT 
       (SELECT COUNT(*) FROM prompt_items WHERE is_public = true OR created_by = ?) as total_prompts,
       (SELECT COUNT(*) FROM prompt_items WHERE created_by = ?) as my_prompts,
       (SELECT COUNT(*) FROM prompt_items WHERE is_public = true) as public_prompts,
       (SELECT COUNT(*) FROM prompt_categories) as total_categories`,
    [userId, userId]
  );
  
  // 获取分类统计
  const categoryStats = await db.execute(
    `SELECT pc.name, COUNT(pi.id) as prompt_count
     FROM prompt_categories pc
     LEFT JOIN prompt_items pi ON pc.id = pi.category_id AND (pi.is_public = true OR pi.created_by = ?)
     GROUP BY pc.id
     ORDER BY pc.list_order ASC
     LIMIT 10`,
    [userId]
  );
  
  // 获取最近创建的提示词
  const recentPrompts = await db.execute(
    `SELECT pi.id, pi.title, pi.is_public, pi.created_at,
            pc.name as category_name,
            u.display_name as creator_name
     FROM prompt_items pi
     LEFT JOIN prompt_categories pc ON pi.category_id = pc.id
     LEFT JOIN users u ON pi.created_by = u.id
     WHERE pi.is_public = true OR pi.created_by = ?
     ORDER BY pi.created_at DESC
     LIMIT 5`,
    [userId]
  );
  
  // 获取热门提示词
  const popularPrompts = await db.execute(
    `SELECT pi.id, pi.title, pi.usage_count, pi.is_public,
            pc.name as category_name,
            u.display_name as creator_name
     FROM prompt_items pi
     LEFT JOIN prompt_categories pc ON pi.category_id = pc.id
     LEFT JOIN users u ON pi.created_by = u.id
     WHERE pi.is_public = true OR pi.created_by = ?
     ORDER BY pi.usage_count DESC
     LIMIT 5`,
    [userId]
  );
  
  res.json(createSuccessResponse({
    overview: stats[0],
    categoryStats,
    recentPrompts,
    popularPrompts
  }));
}));

module.exports = router;