<template>
  <div class="app">
    <!-- 登录页面不显示侧边栏布局 -->
    <div v-if="isLoginPage" class="login-layout">
      <router-view />
    </div>
    
    <!-- 已登录用户显示完整布局 -->
    <div v-else class="app-layout">
      <nav class="sidebar-left">
        <div class="sidebar-header">
          <h1 class="app-title">AIDE</h1>
          <p class="app-subtitle">AI-Integrated Design Environment</p>
        </div>
        <ul class="main-menu">
          <li>
            <router-link to="/" class="menu-item" exact-active-class="router-link-exact-active">
              <span class="menu-icon">🏠</span>
              <span class="menu-text">首页</span>
            </router-link>
          </li>
          <li>
            <router-link to="/chat" class="menu-item" active-class="router-link-active">
              <span class="menu-icon">💬</span>
              <span class="menu-text">聊天</span>
            </router-link>
          </li>
          <li>
            <router-link to="/image-generation" class="menu-item" active-class="router-link-active">
              <span class="menu-icon">🖼️</span>
              <span class="menu-text">生图</span>
            </router-link>
          </li>
          <li>
            <router-link to="/midjourney" class="menu-item" active-class="router-link-active">
              <span class="menu-icon">🎨</span>
              <span class="menu-text">Midjourney</span>
            </router-link>
          </li>
          <li>
            <router-link to="/knowledge-base" class="menu-item" active-class="router-link-active">
              <span class="menu-icon">📚</span>
              <span class="menu-text">知识库</span>
            </router-link>
          </li>
          <li>
            <router-link to="/master-library" class="menu-item" active-class="router-link-active">
              <span class="menu-icon">👨‍🎨</span>
              <span class="menu-text">大师库</span>
            </router-link>
          </li>
          <li>
            <router-link to="/image-library" class="menu-item" active-class="router-link-active">
              <span class="menu-icon">🏞️</span>
              <span class="menu-text">图片库</span>
            </router-link>
          </li>
          <li>
            <router-link to="/prompt-library" class="menu-item" active-class="router-link-active">
              <span class="menu-icon">💭</span>
              <span class="menu-text">提示词库</span>
            </router-link>
          </li>
          <li>
            <router-link to="/script-library" class="menu-item" active-class="router-link-active">
              <span class="menu-icon">📝</span>
              <span class="menu-text">话术库</span>
            </router-link>
          </li>
          <li>
            <router-link to="/settings" class="menu-item" active-class="router-link-active">
              <span class="menu-icon">⚙️</span>
              <span class="menu-text">设置</span>
            </router-link>
          </li>
        </ul>
        
        <!-- 用户信息和菜单 -->
        <div v-if="currentUser" class="user-profile">
          <div class="user-info" @click="toggleUserMenu">
            <div class="user-avatar" v-if="currentUser.avatar" :style="{ backgroundImage: `url(${currentUser.avatar})` }"></div>
            <div class="user-avatar" v-else>{{ currentUser.username.charAt(0).toUpperCase() }}</div>
            <div class="user-details">
              <div class="user-name">{{ currentUser.displayName }}</div>
              <div class="user-role">{{ currentUser.isAdmin ? '管理员' : '普通用户' }}</div>
            </div>
            <div class="toggle-icon">
              <span>{{ showUserMenu ? '▲' : '▼' }}</span>
            </div>
          </div>
          
          <!-- 用户菜单弹窗 -->
          <div v-if="showUserMenu" class="user-menu">
            <router-link to="/profile" class="menu-item" active-class="router-link-active" @click="showUserMenu = false">
              <span class="menu-icon">👤</span>
              <span>编辑账户资料</span>
            </router-link>
            <button class="menu-item logout-item" @click="logout">
              <span class="menu-icon">🚪</span>
              <span>退出登录</span>
            </button>
          </div>
        </div>
      </nav>
      
      <!-- 设置页面的二级边栏 -->
      <aside class="sidebar-second" v-if="showSecondSidebar">
        <div class="settings-nav">
          <div class="settings-nav-header">
            <h3>设置菜单</h3>
          </div>
          <div 
            v-for="section in settingsSections" 
            :key="section.id"
            class="settings-nav-item" 
            :class="{ active: activeSettingsSection === section.id }"
            @click="activeSettingsSection = section.id"
          >
            <span class="settings-nav-icon">{{ section.icon }}</span>
            <span>{{ section.name }}</span>
          </div>
        </div>
      </aside>
      
      <main class="main-content" :class="{ 'with-second-sidebar': showSecondSidebar }">
        <router-view 
          :key="'settings-' + $route.path" 
          v-if="route.path === '/settings'"
          @update:section="activeSettingsSection = $event" 
          :active-section="activeSettingsSection" 
        />
        <router-view v-else :key="'default-' + $route.path" />
      </main>
      
      <aside class="sidebar-right">
        <!-- 右侧边栏内容，根据当前页面动态显示 -->
      </aside>
    </div>
  </div>
</template>

<script setup>
// Gemini code assistant test
import { ref, computed, watch, onMounted } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { authAPI, getCurrentUserFromCache, clearCurrentUserCache } from './services/api.js';

const route = useRoute();
const router = useRouter();
const showSecondSidebar = ref(false);
const currentSettingSection = ref('general');
const activeSettingsSection = ref('general');

// 当前登录用户
const currentUser = ref(null);
// 用户菜单显示状态
const showUserMenu = ref(false);

// 判断当前是否为登录或注册页面
const isLoginPage = computed(() => {
  return route.path === '/login' || route.path === '/register';
});

// 切换用户菜单显示/隐藏
function toggleUserMenu() {
  showUserMenu.value = !showUserMenu.value;
}

// 点击页面其他区域关闭用户菜单
function handleClickOutside(event) {
  const userProfile = document.querySelector('.user-profile');
  if (showUserMenu.value && userProfile && !userProfile.contains(event.target)) {
    showUserMenu.value = false;
  }
}

// 获取当前用户信息
function getCurrentUserInfo() {
  const user = getCurrentUserFromCache();
  if (user) {
    currentUser.value = user;
  }
}

// 初始化
onMounted(() => {
  // 获取当前登录用户信息
  getCurrentUserInfo();
  
  // 添加全局点击事件监听器
  document.addEventListener('click', handleClickOutside);
});

// 监听路由变化，更新用户信息并重置菜单状态
watch(() => route.path, () => {
  getCurrentUserInfo();
  // 确保用户菜单始终处于收缩状态
  showUserMenu.value = false;
});

// 退出登录
async function logout() {
  if (confirm('确定要退出登录吗？')) {
    try {
      await authAPI.logout();
    } catch (error) {
      console.error('登出失败:', error);
    } finally {
      clearCurrentUserCache();
      currentUser.value = null;
      router.push('/login');
    }
  }
}

// 设置页面的二级菜单项
const settingsSections = [
  { id: 'general', name: '常规', icon: '🔧' },
  { id: 'ai', name: 'AI设置', icon: '🤖' },
  { id: 'categories', name: '分类管理', icon: '📋' },
  { id: 'storage', name: '存储', icon: '💾' },
  { id: 'privacy', name: '隐私', icon: '🔒' },
  { id: 'users', name: '用户管理', icon: '👥' },
  { id: 'about', name: '关于', icon: 'ℹ️' }
];

// 监听路由变化，只在设置页面显示二级边栏
watch(() => route.path, (newPath) => {
  showSecondSidebar.value = newPath === '/settings';
  if (newPath !== '/settings') {
    activeSettingsSection.value = 'general';
  }
}, { immediate: true });
</script>

<style>
/* 全局样式 */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: 'Helvetica Neue', Arial, sans-serif;
  color: #333;
  background-color: #f5f5f5;
}

.app {
  display: flex;
  min-height: 100vh;
}

.app-layout {
  display: flex;
  flex: 1;
  height: 100vh;
}

/* 登录页面布局 */
.login-layout {
  width: 100%;
  height: 100vh;
  display: flex;
  justify-content: center;
  align-items: center;
}

.sidebar-header {
  background-color: #4a6baf;
  color: white;
  padding: 1.5rem 1rem;
  text-align: center;
  border-bottom: 1px solid #3d5a8f;
}

.app-title {
  font-size: 1.8rem;
  margin-bottom: 0.5rem;
}

.app-subtitle {
  font-size: 0.8rem;
  opacity: 0.8;
  white-space: nowrap;
}

.sidebar-left {
  width: 250px;
  background-color: #2c3e50;
  color: white;
  display: flex;
  flex-direction: column;
  box-shadow: 2px 0 5px rgba(0, 0, 0, 0.1);
}

.main-menu {
  list-style: none;
  margin-top: 1rem;
  flex: 1;
}

.menu-item {
  display: flex;
  align-items: center;
  padding: 1rem 1.5rem;
  color: rgba(255, 255, 255, 0.6);
  text-decoration: none;
  transition: all 0.3s;
  border-left: 4px solid transparent;
  position: relative;
}

.menu-item:hover {
  background-color: #34495e;
  color: white;
  border-left-color: rgba(93, 132, 215, 0.3);
}

.sidebar-left .main-menu li .router-link-active, 
.sidebar-left .main-menu li .router-link-exact-active {
  background-color: #5d84d7 !important; /* 使用更亮的蓝色，确保与侧边栏形成强烈对比 */
  border-left-color: #ffffff !important; /* 白色边框增强对比 */
  border-left-width: 4px !important;
  color: white !important;
  font-weight: 600 !important;
  box-shadow: inset 0 0 0 1px rgba(255, 255, 255, 0.5) !important;
}

.menu-icon {
  margin-right: 0.8rem;
  font-size: 1.2rem;
}

.main-content {
  flex: 1;
  padding: 2rem;
  /* 移除overflow-y: auto，避免与内层滚动冲突 */
  overflow-y: hidden;
}

.sidebar-right {
  width: 300px;
  background-color: #f8f9fa;
  padding: 1rem;
  border-left: 1px solid #e9ecef;
  display: none; /* 默认隐藏，在特定页面显示 */
}

/* 在特定页面显示右侧边栏 */
.show-right-sidebar .sidebar-right {
  display: block;
}

/* 二级边栏样式 */
.sidebar-second {
  width: 220px;
  background-color: #f0f2f5;
  border-right: 1px solid #e0e0e0;
  transition: width 0.3s ease;
  overflow: hidden;
  box-shadow: 2px 0 5px rgba(0, 0, 0, 0.05);
}

.settings-nav {
  display: flex;
  flex-direction: column;
  padding: 0;
}

.settings-nav-header {
  padding: 1.2rem 1.5rem;
  background-color: #e6e8eb;
  border-bottom: 1px solid #ddd;
}

.settings-nav-header h3 {
  font-size: 1.1rem;
  color: #333;
  margin: 0;
}

.settings-nav-item {
  display: flex;
  align-items: center;
  padding: 0.9rem 1.5rem;
  background: none;
  border: none;
  text-align: left;
  cursor: pointer;
  color: #333;
  transition: all 0.2s ease;
  border-left: 3px solid transparent;
  font-size: 0.95rem;
}

.settings-nav-item:hover {
  background-color: #e6e8eb;
}

.settings-nav-item.active {
  background-color: #e6e8eb;
  border-left-color: #4a6baf;
  font-weight: 500;
  color: #4a6baf;
}

.settings-nav-icon {
  margin-right: 0.8rem;
  font-size: 1.2rem;
  min-width: 24px;
  text-align: center;
}

/* 调整主内容区域，适应二级边栏 */
.main-content.with-second-sidebar {
  margin-left: 0;
  max-width: calc(100% - 220px);
}

/* 用户信息和菜单 */
.user-profile {
  margin-top: auto;
  padding: 15px;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
  position: relative;
}

.user-info {
  display: flex;
  align-items: center;
  gap: 10px;
  cursor: pointer;
  padding: 5px;
  border-radius: 4px;
  transition: background-color 0.2s;
}

.user-info:hover {
  background-color: rgba(255, 255, 255, 0.1);
}

.user-avatar {
  width: 36px;
  height: 36px;
  border-radius: 50%;
  background-color: #4a6baf;
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
  flex-shrink: 0;
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
}

.user-details {
  display: flex;
  flex-direction: column;
  flex: 1;
  min-width: 0;
}

.user-name {
  font-weight: 500;
  font-size: 0.9rem;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.user-role {
  font-size: 0.75rem;
  opacity: 0.8;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.toggle-icon {
  margin-left: auto;
  font-size: 0.7rem;
  opacity: 0.8;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 16px;
}

/* 用户菜单弹窗 */
.user-menu {
  position: absolute;
  bottom: 100%;
  left: 0;
  right: 0;
  background-color: #34495e;
  border-radius: 4px;
  box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.2);
  overflow: hidden;
  z-index: 100;
  margin: 0 5px 5px 5px;
}

.menu-item {
  display: flex;
  align-items: center;
  padding: 12px 15px;
  color: white;
  text-decoration: none;
  transition: background-color 0.2s;
  cursor: pointer;
  border: none;
  background: none;
  width: 100%;
  text-align: left;
  font-size: 0.9rem;
}

.menu-item:hover {
  background-color: #2c3e50;
}

.menu-icon {
  margin-right: 10px;
  font-size: 1.1rem;
  width: 20px;
  text-align: center;
}

.logout-item {
  border-top: 1px solid rgba(255, 255, 255, 0.1);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .sidebar-left {
    width: 60px;
  }
  
  .menu-text, .app-subtitle {
    display: none;
  }
  
  .app-title {
    font-size: 1.2rem;
  }
  
  .sidebar-header {
    padding: 1rem 0.5rem;
  }
  
  .menu-item {
    justify-content: center;
    padding: 1rem;
  }
  
  .menu-icon {
    margin-right: 0;
  }
  
  /* 小屏幕下的用户菜单样式 */
  .user-details {
    display: none;
  }
  
  .toggle-icon {
    display: none;
  }
  
  .user-menu {
    position: fixed;
    bottom: auto;
    left: 60px;
    top: auto;
    width: 180px;
    margin: 0;
  }
  
  .menu-item {
    justify-content: flex-start;
    padding: 12px 15px;
  }
  
  .menu-item .menu-icon {
    margin-right: 10px;
  }
  
  .sidebar-second {
    width: 180px;
  }
  
  .settings-nav-item span:not(.settings-nav-icon) {
    font-size: 0.9rem;
  }
  
  .main-content.with-second-sidebar {
    max-width: calc(100% - 180px);
    padding: 1.5rem;
  }
}
</style>