#!/usr/bin/env python3
"""
简化版本地RAG - 仅使用嵌入模型进行文档检索
不依赖Ollama，适合快速测试和演示
"""

import sys
import os
import json
import pickle
from pathlib import Path
from typing import List, Dict, Any

# 添加RAG-Anything路径
sys.path.insert(0, '/opt/RAG-Anything')

try:
    from sentence_transformers import SentenceTransformer
    import numpy as np
    from sklearn.metrics.pairwise import cosine_similarity
    print("✅ 依赖库导入成功")
except ImportError as e:
    print(f"❌ 依赖库导入失败: {e}")
    sys.exit(1)

class SimpleLocalRAG:
    """简化版本地RAG系统"""
    
    def __init__(self, working_dir: str, model_name: str = "all-MiniLM-L6-v2"):
        self.working_dir = Path(working_dir)
        self.working_dir.mkdir(parents=True, exist_ok=True)
        
        self.model_name = model_name
        self.model = None
        
        # 存储文件
        self.documents_file = self.working_dir / "documents.json"
        self.embeddings_file = self.working_dir / "embeddings.pkl"
        
        # 文档和嵌入
        self.documents = []
        self.embeddings = None
        
        self._load_model()
        self._load_data()
    
    def _load_model(self):
        """加载嵌入模型"""
        try:
            print(f"🔄 加载嵌入模型: {self.model_name}")
            self.model = SentenceTransformer(self.model_name)
            print(f"✅ 模型加载成功")
        except Exception as e:
            print(f"❌ 模型加载失败: {e}")
            raise
    
    def _load_data(self):
        """加载已存储的文档和嵌入"""
        try:
            if self.documents_file.exists():
                with open(self.documents_file, 'r', encoding='utf-8') as f:
                    self.documents = json.load(f)
                print(f"📚 加载了 {len(self.documents)} 个文档")
            
            if self.embeddings_file.exists():
                with open(self.embeddings_file, 'rb') as f:
                    self.embeddings = pickle.load(f)
                print(f"📊 加载了嵌入向量: {self.embeddings.shape}")
        except Exception as e:
            print(f"⚠️ 加载数据失败: {e}")
            self.documents = []
            self.embeddings = None
    
    def _save_data(self):
        """保存文档和嵌入"""
        try:
            with open(self.documents_file, 'w', encoding='utf-8') as f:
                json.dump(self.documents, f, ensure_ascii=False, indent=2)
            
            if self.embeddings is not None:
                with open(self.embeddings_file, 'wb') as f:
                    pickle.dump(self.embeddings, f)
            
            print("💾 数据保存成功")
        except Exception as e:
            print(f"❌ 数据保存失败: {e}")
    
    def add_document(self, content: str, metadata: Dict[str, Any] = None):
        """添加文档到知识库"""
        try:
            # 创建文档对象
            doc = {
                'id': len(self.documents),
                'content': content,
                'metadata': metadata or {}
            }
            
            # 生成嵌入
            embedding = self.model.encode([content])
            
            # 添加到列表
            self.documents.append(doc)
            
            # 更新嵌入矩阵
            if self.embeddings is None:
                self.embeddings = embedding
            else:
                self.embeddings = np.vstack([self.embeddings, embedding])
            
            # 保存数据
            self._save_data()
            
            print(f"✅ 文档添加成功，ID: {doc['id']}")
            return doc['id']
            
        except Exception as e:
            print(f"❌ 文档添加失败: {e}")
            raise
    
    def query(self, query_text: str, top_k: int = 3) -> List[Dict[str, Any]]:
        """查询知识库"""
        try:
            if not self.documents or self.embeddings is None:
                return []
            
            # 生成查询嵌入
            query_embedding = self.model.encode([query_text])
            
            # 计算相似度
            similarities = cosine_similarity(query_embedding, self.embeddings)[0]
            
            # 获取top-k结果
            top_indices = np.argsort(similarities)[::-1][:top_k]
            
            results = []
            for idx in top_indices:
                if similarities[idx] > 0.1:  # 相似度阈值
                    result = {
                        'document': self.documents[idx],
                        'similarity': float(similarities[idx]),
                        'content': self.documents[idx]['content']
                    }
                    results.append(result)
            
            return results
            
        except Exception as e:
            print(f"❌ 查询失败: {e}")
            return []
    
    def get_stats(self) -> Dict[str, Any]:
        """获取统计信息"""
        return {
            'document_count': len(self.documents),
            'model_name': self.model_name,
            'embedding_dim': self.embeddings.shape[1] if self.embeddings is not None else 0,
            'working_dir': str(self.working_dir)
        }

def main():
    """主函数"""
    if len(sys.argv) < 3:
        print("用法: python simple_local_rag.py <working_dir> <command> [args...]")
        print("命令:")
        print("  query <query_text>     - 查询知识库")
        print("  add <content>          - 添加文档")
        print("  stats                  - 显示统计信息")
        sys.exit(1)
    
    working_dir = sys.argv[1]
    command = sys.argv[2]
    
    try:
        rag = SimpleLocalRAG(working_dir)
        
        if command == "query":
            if len(sys.argv) < 4:
                print("❌ 缺少查询文本")
                sys.exit(1)
            
            query_text = sys.argv[3]
            results = rag.query(query_text)
            
            if results:
                print(f"🔍 查询: {query_text}")
                print(f"📄 找到 {len(results)} 个相关文档:")
                
                best_result = results[0]
                response = f"根据知识库内容，{best_result['content'][:200]}..."
                
                print("LOCAL_RAG_RESULT:", response)
            else:
                print("LOCAL_RAG_RESULT: 未找到相关信息")
        
        elif command == "add":
            if len(sys.argv) < 4:
                print("❌ 缺少文档内容")
                sys.exit(1)
            
            content = sys.argv[3]
            doc_id = rag.add_document(content)
            print("LOCAL_RAG_RESULT:", f"文档添加成功，ID: {doc_id}")
        
        elif command == "stats":
            stats = rag.get_stats()
            print("LOCAL_RAG_RESULT:", json.dumps(stats, ensure_ascii=False))
        
        else:
            print(f"❌ 未知命令: {command}")
            sys.exit(1)
    
    except Exception as e:
        print(f"❌ 执行失败: {e}")
        print("LOCAL_RAG_ERROR:", str(e))
        sys.exit(1)

if __name__ == "__main__":
    main()
