/**
 * 向量化服务
 * 
 * 功能说明：
 * - 文档向量化处理
 * - 向量检索
 * - 文档分块
 */

const db = require('../config/database');
const fs = require('fs');
const path = require('path');
const { createSuccessResponse } = require('../middleware/errorHandler');

// 文档分块配置
const CHUNK_SIZE = 1000; // 每个块的最大字符数
const CHUNK_OVERLAP = 200; // 块之间的重叠字符数

class VectorService {
  /**
   * 文档分块
   * @param {string} content 文档内容
   * @returns {Array} 分块后的文档块
   */
  static chunkDocument(content) {
    const chunks = [];
    let start = 0;
    
    while (start < content.length) {
      const end = Math.min(start + CHUNK_SIZE, content.length);
      let chunk = content.substring(start, end);
      
      // 尝试在句子边界分割
      if (end < content.length) {
        const lastPeriod = chunk.lastIndexOf('。');
        const lastExclamation = chunk.lastIndexOf('！');
        const lastQuestion = chunk.lastIndexOf('？');
        const lastNewline = chunk.lastIndexOf('\n');
        
        const splitPoint = Math.max(lastPeriod, lastExclamation, lastQuestion, lastNewline);
        
        if (splitPoint > start + CHUNK_SIZE * 0.7) {
          chunk = chunk.substring(0, splitPoint + 1);
          start = start + splitPoint + 1;
        } else {
          start = end;
        }
      } else {
        start = end;
      }
      
      if (chunk.trim()) {
        chunks.push(chunk.trim());
      }
    }
    
    return chunks;
  }

  /**
   * 生成向量嵌入（模拟）
   * @param {string} text 文本内容
   * @returns {Array} 向量数据
   */
  static async generateEmbedding(text) {
    // 这里应该调用实际的嵌入模型API
    // 目前使用模拟的向量数据
    const vectorSize = 1536; // OpenAI text-embedding-ada-002 的向量维度
    const vector = [];
    
    for (let i = 0; i < vectorSize; i++) {
      vector.push(Math.random() * 2 - 1); // 生成-1到1之间的随机数
    }
    
    // 模拟API调用延迟
    await new Promise(resolve => setTimeout(resolve, 100));
    
    return vector;
  }

  /**
   * 计算向量相似度（余弦相似度）
   * @param {Array} vector1 向量1
   * @param {Array} vector2 向量2
   * @returns {number} 相似度分数
   */
  static cosineSimilarity(vector1, vector2) {
    if (vector1.length !== vector2.length) {
      throw new Error('向量维度不匹配');
    }
    
    let dotProduct = 0;
    let norm1 = 0;
    let norm2 = 0;
    
    for (let i = 0; i < vector1.length; i++) {
      dotProduct += vector1[i] * vector2[i];
      norm1 += vector1[i] * vector1[i];
      norm2 += vector2[i] * vector2[i];
    }
    
    norm1 = Math.sqrt(norm1);
    norm2 = Math.sqrt(norm2);
    
    if (norm1 === 0 || norm2 === 0) {
      return 0;
    }
    
    return dotProduct / (norm1 * norm2);
  }

  /**
   * 处理文档向量化
   * @param {number} itemId 知识库项目ID
   * @param {string} filePath 文件路径
   * @returns {Object} 处理结果
   */
  static async processDocumentVectorization(itemId, filePath) {
    try {
      // 读取文件内容
      const content = fs.readFileSync(filePath, 'utf8');
      
      // 文档分块
      const chunks = this.chunkDocument(content);
      
      // 开始事务
      const connection = await db.getConnection();
      await db.beginTransaction(connection);
      
      try {
        // 删除现有的向量数据
        await db.execute(
          'DELETE FROM knowledge_vectors WHERE item_id = ?',
          [itemId],
          connection
        );
        
        // 处理每个文档块
        for (let i = 0; i < chunks.length; i++) {
          const chunk = chunks[i];
          const chunkId = `${itemId}_chunk_${i}`;
          
          // 生成向量嵌入
          const vector = await this.generateEmbedding(chunk);
          
          // 保存到数据库
          await db.execute(
            `INSERT INTO knowledge_vectors 
             (item_id, chunk_id, content, vector_data, chunk_index, token_count, status) 
             VALUES (?, ?, ?, ?, ?, ?, ?)`,
            [
              itemId,
              chunkId,
              chunk,
              JSON.stringify(vector),
              i,
              chunk.length, // 简化的token计数
              'completed'
            ],
            connection
          );
        }
        
        await db.commitTransaction(connection);
        
        return {
          success: true,
          chunksCount: chunks.length,
          message: `文档向量化完成，共处理 ${chunks.length} 个文档块`
        };
      } catch (error) {
        await db.rollbackTransaction(connection);
        throw error;
      }
    } catch (error) {
      console.error('文档向量化失败:', error);
      return {
        success: false,
        message: `文档向量化失败: ${error.message}`
      };
    }
  }

  /**
   * 向量检索
   * @param {string} query 查询文本
   * @param {number} limit 返回结果数量限制
   * @param {number} userId 用户ID
   * @returns {Array} 检索结果
   */
  static async vectorSearch(query, limit = 5, userId = null) {
    const startTime = Date.now();
    
    try {
      // 生成查询向量
      const queryVector = await this.generateEmbedding(query);
      
      // 获取所有向量数据
      const [vectors] = await db.execute(
        `SELECT kv.id, kv.item_id, kv.content, kv.vector_data, kv.chunk_index,
                ki.title, ki.description
         FROM knowledge_vectors kv
         LEFT JOIN knowledge_items ki ON kv.item_id = ki.id
         WHERE kv.status = 'completed'`
      );
      
      // 计算相似度并排序
      const results = [];
      for (const vector of vectors) {
        const storedVector = JSON.parse(vector.vector_data);
        const similarity = this.cosineSimilarity(queryVector, storedVector);
        
        results.push({
          id: vector.id,
          itemId: vector.item_id,
          content: vector.content,
          chunkIndex: vector.chunk_index,
          title: vector.title,
          description: vector.description,
          similarity: similarity
        });
      }
      
      // 按相似度排序并限制结果数量
      results.sort((a, b) => b.similarity - a.similarity);
      const topResults = results.slice(0, limit);
      
      // 记录搜索日志
      if (userId) {
        const searchTime = (Date.now() - startTime) / 1000;
        await db.execute(
          'INSERT INTO vector_search_logs (user_id, query, results_count, search_time) VALUES (?, ?, ?, ?)',
          [userId, query, topResults.length, searchTime]
        );
      }
      
      return topResults;
    } catch (error) {
      console.error('向量检索失败:', error);
      return [];
    }
  }

  /**
   * 获取向量化进度
   * @param {number} itemId 知识库项目ID
   * @returns {Object} 进度信息
   */
  static async getVectorizationProgress(itemId) {
    try {
      const [vectors] = await db.execute(
        `SELECT status, COUNT(*) as count
         FROM knowledge_vectors 
         WHERE item_id = ?
         GROUP BY status`,
        [itemId]
      );
      
      const [total] = await db.execute(
        'SELECT COUNT(*) as total FROM knowledge_vectors WHERE item_id = ?',
        [itemId]
      );
      
      const progress = {
        total: total[0].total,
        completed: 0,
        processing: 0,
        failed: 0,
        pending: 0
      };
      
      for (const vector of vectors) {
        progress[vector.status] = vector.count;
      }
      
      return progress;
    } catch (error) {
      console.error('获取向量化进度失败:', error);
      return null;
    }
  }
}

module.exports = VectorService; 