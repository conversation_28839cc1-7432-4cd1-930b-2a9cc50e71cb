/**
 * 聊天功能路由
 * 
 * 功能说明：
 * - 发送聊天消息
 * - 获取聊天历史
 * - 管理聊天会话
 */

const express = require('express');
const axios = require('axios');
const router = express.Router();
const { Transform } = require('stream');

const db = require('../config/database');
const { validate, chatMessageSchema } = require('../middleware/validation');
const { authenticateToken } = require('../middleware/auth');
const { asyncHandler, ValidationError, NotFoundError, createSuccessResponse } = require('../middleware/errorHandler');
const VectorService = require('../services/vectorService');

// ==================== 流式聊天 ====================

/**
 * @route POST /api/chat/stream
 * @desc 以流式方式发送聊天消息
 * @access Private
 */
router.post('/stream', authenticateToken, validate(chatMessageSchema), asyncHandler(async (req, res) => {
  const { modelId, prompt, message, sessionId } = req.body;
  const userId = req.user.id;
  let connection;
  let currentSessionId = sessionId;

  console.log('流式聊天请求参数:', { modelId, prompt: prompt?.substring(0, 50) + '...', sessionId, userId });

  // 设置响应头
  res.setHeader('Content-Type', 'text/event-stream');
  res.setHeader('Cache-Control', 'no-cache');
  res.setHeader('Connection', 'keep-alive');
  res.setHeader('X-Accel-Buffering', 'no'); // 禁用Nginx缓冲
  res.flushHeaders();

  // 发送事件的工具函数
  const sendEvent = (event, data) => {
    if (!res.writableEnded) {
      res.write(`event: ${event}\ndata: ${JSON.stringify(data)}\n\n`);
      res.flush(); // 立即刷新数据
    }
  };

  // 错误处理函数
  const handleError = async (error, errorMessage) => {
    console.error(errorMessage, error);
    if (connection) {
      try {
        await db.rollbackTransaction(connection);
      } catch (rollbackError) {
        console.error('回滚事务失败:', rollbackError);
      } finally {
        connection.release();
      }
    }
    sendEvent('error', { message: errorMessage });
    res.end();
  };

  try {
    // 获取数据库连接
    connection = await db.getConnection();
    await db.beginTransaction(connection);

    // 验证并获取模型信息
    const [model] = await db.execute(
      'SELECT id, name, type, api_endpoint, api_key, system_prompt, max_tokens, temperature FROM ai_models WHERE id = ? AND is_active = 1 AND type = "chat"',
      [modelId],
      connection
    );

    console.log('流式聊天查询到的模型信息:', model ? { id: model.id, name: model.name, type: model.type } : '未找到模型');

    if (!model) {
      throw new ValidationError(`指定的聊天模型不存在或不可用 (modelId: ${modelId})`);
    }

    // 创建或获取会话
    if (!currentSessionId) {
      const [sessionResult] = await db.execute(
        'INSERT INTO chat_sessions (user_id, title, model_id, created_at, updated_at) VALUES (?, ?, ?, NOW(), NOW())',
        [userId, `新会话 ${new Date().toLocaleString()}`, modelId],
        connection
      );
      currentSessionId = sessionResult.insertId;
      console.log('流式聊天创建新会话:', currentSessionId);
      sendEvent('sessionCreated', { sessionId: currentSessionId });
    }

    // 保存用户消息
    await db.execute(
      'INSERT INTO messages (session_id, user_id, content, type, role, created_at) VALUES (?, ?, ?, "text", "user", NOW())',
      [currentSessionId, userId, prompt],
      connection
    );

    // 构建消息历史
    const messagesWithSystemPrompt = Array.isArray(message) ? [...message] : [];

    // 添加当前用户消息
    messagesWithSystemPrompt.push({ role: 'user', content: prompt });

    // 添加系统提示词到开头
    if (model.system_prompt) {
      messagesWithSystemPrompt.unshift({ role: 'system', content: model.system_prompt });
    }

    console.log('流式聊天API请求数据:', {
      endpoint: model.api_endpoint,
      model: model.name,
      messageCount: messagesWithSystemPrompt.length,
      hasApiKey: !!model.api_key
    });

    // 设置请求超时
    const timeout = 60000; // 增加到60秒
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), timeout);

    try {
      // 准备API请求数据
      const apiPayload = {
        model: model.name,
        messages: messagesWithSystemPrompt,
        stream: true,
        max_tokens: parseInt(model.max_tokens) || 4096,
        temperature: parseFloat(model.temperature) || 0.7,
      };

      // 特殊处理deepseek模型的兼容性
      if (model.provider === 'deepseek') {
        // 根据模型名称自动选择正确的API模型名
        if (model.name === 'deepseek-reasoner' || model.id === 'deepseek-reasoner') {
          apiPayload.model = 'deepseek-reasoner';
          // 推理模式需要更高的max_tokens
          apiPayload.max_tokens = Math.max(apiPayload.max_tokens, 8192);
          console.log('使用deepseek推理模式:', apiPayload.model);
        } else {
          apiPayload.model = 'deepseek-chat';
          console.log('使用deepseek聊天模式:', apiPayload.model);
        }
      }

      console.log('发送API请求:', {
        url: model.api_endpoint,
        model: apiPayload.model,
        temperature: apiPayload.temperature,
        max_tokens: apiPayload.max_tokens,
        messageCount: messagesWithSystemPrompt.length
      });

      // 调用AI API
      const apiResponse = await axios.post(model.api_endpoint, apiPayload, {
        headers: {
          'Authorization': `Bearer ${model.api_key}`,
          'Content-Type': 'application/json'
        },
        responseType: 'stream',
        signal: controller.signal,
        timeout: timeout
      });

      clearTimeout(timeoutId);
      const stream = apiResponse.data;

      console.log('流式聊天API响应状态:', apiResponse.status);

      // 处理客户端断开连接
      req.on('close', async () => {
        console.log('客户端断开连接，清理资源');
        stream.destroy();
        if (connection) {
          try {
            await db.rollbackTransaction(connection);
          } finally {
            connection.release();
          }
        }
      });

      let aiReply = '';
      let lastChunkTime = Date.now();

      // 处理流数据
      stream.on('data', chunk => {
        lastChunkTime = Date.now();
        const chunkStr = chunk.toString('utf-8');
        console.log('收到流数据块:', chunkStr.substring(0, 200) + (chunkStr.length > 200 ? '...' : ''));

        const lines = chunkStr.split('\n').filter(line => line.trim() !== '');

        for (const line of lines) {
          if (line.startsWith('data: ')) {
            const data = line.substring(6).trim();

            if (data === '[DONE]') {
              console.log('流式聊天收到结束信号');
              sendEvent('end', { message: '流结束' });
              return;
            }

            try {
              const parsed = JSON.parse(data);

              if (parsed.choices && parsed.choices[0].delta) {
                const delta = parsed.choices[0].delta;

                // 处理deepseek-reasoner的特殊结构
                if (delta.reasoning_content) {
                  // 推理过程内容，发送给前端显示
                  const reasoningContent = delta.reasoning_content;
                  console.log('推理过程:', reasoningContent.substring(0, 50) + '...');
                  console.log('发送推理事件给前端');
                  sendEvent('reasoning', { content: reasoningContent });
                } else if (delta.content && delta.content !== null) {
                  // 最终回复内容，发送给前端
                  const content = delta.content;
                  console.log('发送内容给前端:', content);
                  aiReply += content;
                  sendEvent('message', { content });
                } else if (delta.role === 'assistant' && delta.content === null && !delta.reasoning_content) {
                  // 这是开始标记，不需要处理
                  console.log('收到助手开始标记');
                } else {
                  // 打印完整的delta结构来调试
                  console.log('未知的delta结构:', JSON.stringify(delta, null, 2));
                }
              }
            } catch (error) {
              console.error('解析流数据失败:', error, '原始数据:', data.substring(0, 100));
            }
          }
        }
      });

      // 设置心跳检测
      const heartbeatInterval = setInterval(() => {
        if (Date.now() - lastChunkTime > 5000) {
          sendEvent('heartbeat', { timestamp: Date.now() });
        }
      }, 5000);

      // 处理流结束
      stream.on('end', async () => {
        clearInterval(heartbeatInterval);
        console.log('流式聊天流结束，AI回复长度:', aiReply.length);

        // 重试机制处理数据库死锁
        let retryCount = 0;
        const maxRetries = 3;

        while (retryCount < maxRetries) {
          try {
            if (aiReply.trim()) {
              await db.execute(
                'INSERT INTO messages (session_id, user_id, content, type, role, created_at) VALUES (?, ?, ?, "text", "assistant", NOW())',
                [currentSessionId, userId, aiReply],
                connection
              );
            }
            await db.execute(
              'UPDATE chat_sessions SET updated_at = NOW() WHERE id = ?',
              [currentSessionId],
              connection
            );
            await db.commitTransaction(connection);
            console.log('流式聊天事务提交成功');
            break; // 成功则跳出循环
          } catch (error) {
            if (error.code === 'ER_LOCK_DEADLOCK' && retryCount < maxRetries - 1) {
              retryCount++;
              console.log(`数据库死锁，重试第${retryCount}次...`);
              // 等待随机时间后重试
              await new Promise(resolve => setTimeout(resolve, Math.random() * 100 + 50));
              continue;
            } else {
              await handleError(error, '保存AI回复时出错');
              return;
            }
          }
        }

        if (connection) connection.release();
        res.end();
      });

      // 处理流错误
      stream.on('error', async (error) => {
        clearInterval(heartbeatInterval);
        console.error('流式聊天流错误:', error);
        await handleError(error, '处理AI响应流时出错');
      });

    } catch (error) {
      clearTimeout(timeoutId);
      console.error('流式聊天API调用错误:', error);
      if (error.name === 'AbortError') {
        await handleError(error, 'AI响应超时');
      } else {
        await handleError(error, '调用AI API时出错');
      }
    }

  } catch (error) {
    console.error('流式聊天处理请求时出错:', error);
    await handleError(error, '处理请求时出错');
  }
}));

// ==================== 聊天消息 ====================

/**
 * @route POST /api/chat/send
 * @desc 发送聊天消息
 * @access Private
 */
router.post('/send', authenticateToken, validate(chatMessageSchema), asyncHandler(async (req, res) => {
  const { modelId, prompt, message, sessionId } = req.body;
  const userId = req.user.id;
  let connection;
  
  console.log('聊天请求参数:', { modelId, prompt: prompt?.substring(0, 50) + '...', sessionId, userId });
  
  // 验证模型是否存在且可用，并获取system_prompt
  const [model] = await db.execute(
    'SELECT id, name, type, api_endpoint, api_key, system_prompt, max_tokens, temperature FROM ai_models WHERE id = ? AND is_active = 1 AND type = "chat"',
    [modelId]
  );

  console.log('查询到的模型信息:', model ? { id: model.id, name: model.name, type: model.type } : '未找到模型');

  if (!model) {
    throw new ValidationError(`指定的聊天模型不存在或不可用 (modelId: ${modelId})`);
  }

  try {
    // 开始事务
    connection = await db.getConnection();
    await db.beginTransaction(connection);

    let currentSessionId = sessionId;

    // 如果没有会话ID，创建新会话
    if (!currentSessionId) {
      const [sessionResult] = await db.execute(
        'INSERT INTO chat_sessions (user_id, title, model_id, created_at, updated_at) VALUES (?, ?, ?, NOW(), NOW())',
        [userId, `新会话 ${new Date().toLocaleString()}`, modelId],
        connection
      );
      currentSessionId = sessionResult.insertId;
      console.log('创建新会话:', currentSessionId);
    }

    // 保存用户消息
    const [userMessageResult] = await db.execute(
      `INSERT INTO messages (session_id, user_id, content, type, role, created_at) 
       VALUES (?, ?, ?, 'text', 'user', NOW())`,
      [currentSessionId, userId, prompt],
      connection
    );

    let aiReply = '';

    try {
      // 合并系统提示词到消息列表中
      const messagesWithSystemPrompt = Array.isArray(message) ? [...message] : [];

      // 添加当前用户消息
      messagesWithSystemPrompt.push({ role: 'user', content: prompt });

      // 添加系统提示词到开头
      if (model.system_prompt) {
        messagesWithSystemPrompt.unshift({ role: 'system', content: model.system_prompt });
      }

      // 准备发送到AI模型API的数据
      const apiPayload = {
        model: model.name, // 或者模型需要的其他标识符
        messages: messagesWithSystemPrompt,
        max_tokens: parseInt(model.max_tokens) || 4096,
        temperature: parseFloat(model.temperature) || 0.7,
        // 根据模型API的要求可能需要其他参数
        // stream: false,
      };

      // 特殊处理deepseek模型的兼容性
      if (model.provider === 'deepseek') {
        // 根据模型名称自动选择正确的API模型名
        if (model.name === 'deepseek-reasoner' || model.id === 'deepseek-reasoner') {
          apiPayload.model = 'deepseek-reasoner';
          // 推理模式需要更高的max_tokens
          apiPayload.max_tokens = Math.max(apiPayload.max_tokens, 8192);
          console.log('使用deepseek推理模式:', apiPayload.model);
        } else {
          apiPayload.model = 'deepseek-chat';
          console.log('使用deepseek聊天模式:', apiPayload.model);
        }
      }

      console.log('API请求数据:', {
        endpoint: model.api_endpoint,
        model: apiPayload.model,
        messageCount: messagesWithSystemPrompt.length,
        hasApiKey: !!model.api_key
      });

      // 关键修改：优先使用环境变量中的DeepSeek API Key
      const apiKey = model.api_key;
      if (!apiKey) {
        throw new Error('未找到可用的API密钥');
      }

      // 调用AI模型API
      const apiResponse = await axios.post(model.api_endpoint, apiPayload, {
        headers: {
          'Authorization': `Bearer ${apiKey}`,
          'Content-Type': 'application/json'
        },
        timeout: 60000 // 增加到60秒超时
      });

      console.log('API响应状态:', apiResponse.status);

      // 从API响应中提取回复
      if (apiResponse.data && apiResponse.data.choices && apiResponse.data.choices.length > 0) {
        aiReply = apiResponse.data.choices[0].message.content;
        console.log('AI回复长度:', aiReply.length);
      } else {
        aiReply = 'AI模型返回了意想不到的响应格式。';
        console.log('API响应格式异常:', apiResponse.data);
      }

    } catch (error) {
      console.error(`调用AI模型API时出错 (${model.name}):`, error.response ? error.response.data : error.message);
      aiReply = `抱歉，与 ${model.name} 通信时发生错误。请稍后再试。`;
    }

    // 保存AI回复
    const [aiMessageResult] = await db.execute(
      `INSERT INTO messages (session_id, user_id, content, type, role, created_at) 
       VALUES (?, ?, ?, 'text', 'assistant', NOW())`,
      [currentSessionId, userId, aiReply],
      connection
    );

    // 更新会话的最后更新时间
    await db.execute(
      'UPDATE chat_sessions SET updated_at = NOW() WHERE id = ?',
      [currentSessionId],
      connection
    );

    await db.commitTransaction(connection);

    console.log('聊天请求处理完成:', { sessionId: currentSessionId, replyLength: aiReply.length });

    res.json(createSuccessResponse({
      sessionId: currentSessionId,
      messageId: userMessageResult.insertId,
      reply: aiReply,
      model: {
        id: model.id,
        name: model.name
      }
    }, '消息发送成功'));
  } catch (error) {
    console.error('聊天请求处理失败:', error);
    // 确保在错误时回滚事务并释放连接
    if (connection) {
      try {
        await db.rollbackTransaction(connection);
      } catch (rollbackError) {
        console.error('回滚事务失败:', rollbackError);
      } finally {
        connection.release();
      }
    }
    throw error;
  }
}));

// ==================== 聊天历史 ====================

/**
 * @route GET /api/chat/history
 * @desc 获取聊天历史
 * @access Private
 */
router.get('/history', authenticateToken, asyncHandler(async (req, res) => {
  const userId = req.user.id;
  const { page = 1, limit = 20, sessionId } = req.query;
  
  const offset = (page - 1) * limit;
  
  let whereClause = 'WHERE m.user_id = ?';
  let queryParams = [userId];
  
  if (sessionId) {
    whereClause += ' AND m.session_id = ?';
    queryParams.push(sessionId);
  }
  
  // 获取聊天消息列表
  const messages = await db.execute(
    `SELECT m.id, m.content, m.type, m.role, m.created_at,
            cs.title as session_title, cs.id as session_id,
            am.name as model_name, am.id as model_id
     FROM messages m
     LEFT JOIN chat_sessions cs ON m.session_id = cs.id
     LEFT JOIN ai_models am ON cs.model_id = am.id
     ${whereClause}
     ORDER BY m.created_at DESC
     LIMIT ? OFFSET ?`,
    [...queryParams, parseInt(limit), offset]
  );
  
  // 获取总数
  const countResult = await db.execute(
    `SELECT COUNT(*) as total FROM messages m ${whereClause}`,
    queryParams
  );
  
  const total = countResult[0].total;
  
  res.json(createSuccessResponse({
    messages,
    pagination: {
      page: parseInt(page),
      limit: parseInt(limit),
      total,
      pages: Math.ceil(total / limit)
    }
  }));
}));

// ==================== 聊天会话管理 ====================

/**
 * @route GET /api/chat/sessions
 * @desc 获取当前用户的所有聊天会话
 * @access Private
 */
router.get('/sessions', authenticateToken, asyncHandler(async (req, res) => {
  const userId = req.user.id;
  const sessions = await db.execute(
    'SELECT id, title, model_id, created_at, updated_at FROM chat_sessions WHERE user_id = ? ORDER BY updated_at DESC',
    [userId]
  );
  res.json(createSuccessResponse(sessions));
}));

/**
 * @route GET /api/chat/sessions/:id/messages
 * @desc 获取指定聊天会话的所有消息
 * @access Private
 */
router.get('/sessions/:id/messages', authenticateToken, asyncHandler(async (req, res) => {
  const { id } = req.params;
  const userId = req.user.id;
  const messages = await db.execute(
    'SELECT m.id, m.role, m.content, m.created_at FROM messages m JOIN chat_sessions s ON m.session_id = s.id WHERE m.session_id = ? AND s.user_id = ? ORDER BY m.created_at ASC',
    [id, userId]
  );
  if (!messages) {
    throw new NotFoundError('未找到会话或消息');
  }
  res.json(createSuccessResponse(messages));
}));

/**
 * @route POST /api/chat/sessions
 * @desc 创建一个新的聊天会话
 * @access Private
 */
router.post('/sessions', authenticateToken, asyncHandler(async (req, res) => {
  const { title, modelId } = req.body;
  const userId = req.user.id;
  
  if (!title || !modelId) {
    throw new ValidationError('标题和模型ID是必需的');
  }
  
  const result = await db.execute(
    'INSERT INTO chat_sessions (user_id, title, model_id, created_at, updated_at) VALUES (?, ?, ?, NOW(), NOW())',
    [userId, title, modelId]
  );
  
  const session = await db.execute('SELECT * FROM chat_sessions WHERE id = ?', [result.insertId]);

  res.status(201).json(createSuccessResponse(session[0]));
}));

/**
 * @route PUT /api/chat/sessions/:id
 * @desc 更新聊天会话（例如，重命名）
 * @access Private
 */
router.put('/sessions/:id', authenticateToken, asyncHandler(async (req, res) => {
  const { id } = req.params;
  const { title } = req.body;
  const userId = req.user.id;

  if (!title) {
    throw new ValidationError('标题是必需的');
  }

  const result = await db.execute(
    'UPDATE chat_sessions SET title = ?, updated_at = NOW() WHERE id = ? AND user_id = ?',
    [title, id, userId]
  );

  if (result.affectedRows === 0) {
    throw new NotFoundError('未找到会话或权限不足');
  }
  
  const session = await db.execute('SELECT * FROM chat_sessions WHERE id = ?', [id]);
  res.json(createSuccessResponse(session[0]));
}));

/**
 * @route DELETE /api/chat/sessions/:id
 * @desc 删除一个聊天会话及其所有消息
 * @access Private
 */
router.delete('/sessions/:id', authenticateToken, asyncHandler(async (req, res) => {
  const { id } = req.params;
  const userId = req.user.id;
  const connection = await db.getConnection();
  
  await db.beginTransaction(connection);
  try {
    const sessionResult = await connection.execute('SELECT id FROM chat_sessions WHERE id = ? AND user_id = ?', [id,userId]);
    if (sessionResult.length === 0) {
      throw new NotFoundError('未找到会话或权限不足');
    }
    
    await connection.execute('DELETE FROM messages WHERE session_id = ?', [id]);
    await connection.execute('DELETE FROM chat_sessions WHERE id = ?', [id]);
    
    await db.commitTransaction(connection);
    res.json(createSuccessResponse({ message: '会话已成功删除' }));
  } catch (error) {
    await db.rollbackTransaction(connection);
    throw error;
  } finally {
    connection.release();
  }
}));

// ==================== 向量数据库集成（示例） ====================

/**
 * @route POST /api/chat/vector-search
 * @desc 向量数据库集成示例
 * @access Private
 */
router.post('/vector-search', authenticateToken, asyncHandler(async (req, res) => {
  const { query, topK } = req.body;
  const userId = req.user.id;
  
  if (!query || !topK) {
    throw new ValidationError('查询文本和topK是必需的');
  }
  
  try {
    const searchResults = await VectorService.vectorSearch(query, topK, userId);
    res.json(createSuccessResponse(searchResults));
  } catch (error) {
    throw error;
  }
}));

// ==================== PPT大纲生成 ====================

/**
 * @route POST /api/chat/generate-outline
 * @desc 基于AI回答内容生成PPT大纲
 * @access Private
 */
router.post('/generate-outline', authenticateToken, asyncHandler(async (req, res) => {
  try {
    const { content, messageId } = req.body;

    if (!content) {
      return res.status(400).json({ error: '内容不能为空' });
    }

    console.log('生成大纲请求:', { messageId, contentLength: content.length });

    // 使用AI生成大纲
    const outlinePrompt = `请基于以下内容生成一个结构化的PPT大纲。要求：
1. 提取主要观点和关键信息
2. 按逻辑层次组织内容
3. 每个要点简洁明了
4. 适合制作PPT演示
5. 返回JSON格式，包含title（标题）和slides（幻灯片数组）
6. 每个slide包含title（幻灯片标题）、keywords（标题关键词数组）和points（要点数组）
7. 每个point可以是字符串，或者包含text（文本）和keywords（关键词数组）的对象
8. keywords应该是需要在PPT中突出显示的重要词汇

内容：
${content}

请返回JSON格式的大纲，示例格式：
{
  "title": "演示标题",
  "slides": [
    {
      "title": "幻灯片标题",
      "keywords": ["关键词1", "关键词2"],
      "points": [
        {
          "text": "要点文本内容",
          "keywords": ["要点关键词"]
        },
        "简单要点文本"
      ]
    }
  ]
}`;

    // 获取DeepSeek模型的API密钥
    const [models] = await db.execute(
      'SELECT api_key FROM ai_models WHERE name = ? OR id = ? LIMIT 1',
      ['deepseek-chat', 'deepseek-chat']
    );

    if (!models || !models.api_key) {
      throw new Error('未找到DeepSeek API密钥配置');
    }

    // 调用AI API生成大纲
    const aiResponse = await fetch('https://api.deepseek.com/v1/chat/completions', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${models.api_key}`
      },
      body: JSON.stringify({
        model: 'deepseek-chat',
        messages: [
          {
            role: 'user',
            content: outlinePrompt
          }
        ],
        temperature: 0.3,
        max_tokens: 2000
      })
    });

    if (!aiResponse.ok) {
      throw new Error(`AI API请求失败: ${aiResponse.status}`);
    }

    const aiData = await aiResponse.json();
    const outlineText = aiData.choices[0].message.content;

    console.log('AI生成的大纲文本:', outlineText);

    // 尝试解析JSON格式的大纲
    let outline;
    try {
      // 提取JSON部分（去除可能的markdown格式）
      const jsonMatch = outlineText.match(/```json\s*([\s\S]*?)\s*```/) ||
                       outlineText.match(/\{[\s\S]*\}/);
      const jsonText = jsonMatch ? jsonMatch[1] || jsonMatch[0] : outlineText;
      outline = JSON.parse(jsonText);
    } catch (parseError) {
      console.log('JSON解析失败，使用文本解析:', parseError.message);
      // 如果JSON解析失败，使用文本解析生成大纲
      outline = parseTextToOutline(outlineText);
    }

    // 验证大纲格式
    if (!outline.title || !Array.isArray(outline.slides)) {
      outline = {
        title: outline.title || '演示文稿',
        slides: Array.isArray(outline.slides) ? outline.slides : [
          {
            title: '主要内容',
            points: Array.isArray(outline.points) ? outline.points : ['内容概述']
          }
        ]
      };
    }

    console.log('生成的大纲结构:', JSON.stringify(outline, null, 2));

    res.json(createSuccessResponse({
      outline: outline
    }, '大纲生成成功'));

  } catch (error) {
    console.error('生成大纲失败:', error);
    res.status(500).json({
      error: '生成大纲失败',
      message: error.message
    });
  }
}));

// 创建带有红色关键字的文本数组（用于pptxgenjs）
function createTextWithRedKeywords(text, userKeywords = [], baseFontSize = 17) {
  // 在编辑模式下，只使用用户指定的关键词，不添加默认关键词
  console.log(`🎨 处理文本关键词高亮: "${text}", 用户关键词:`, userKeywords);

  // 只使用用户提供的关键词
  const keywords = userKeywords || [];

  // 如果没有用户关键词，返回普通文本
  if (keywords.length === 0) {
    return [{
      text: text,
      options: {
        color: '000000',
        fontSize: baseFontSize,
        fontFace: 'Microsoft YaHei'
      }
    }];
  }

  // 按长度排序，优先匹配长关键字
  keywords.sort((a, b) => b.length - a.length);

  // 计算关键字字体大小（比基础字体大20%）
  const keywordFontSize = Math.round(baseFontSize * 1.3);

  const textParts = [];
  let remainingText = text;

  while (remainingText.length > 0) {
    let foundKeyword = false;

    // 查找用户指定的关键字
    for (const keyword of keywords) {
      const index = remainingText.toLowerCase().indexOf(keyword.toLowerCase());
      if (index === 0) {
        // 找到关键字，添加为红色文本，字体大20%
        textParts.push({
          text: remainingText.substring(0, keyword.length),
          options: {
            color: 'cc0000',
            bold: true,
            fontSize: keywordFontSize,
            fontFace: 'Microsoft YaHei'
          }
        });
        remainingText = remainingText.substring(keyword.length);
        foundKeyword = true;
        console.log(`✅ 找到用户关键词: "${keyword}"`);
        break;
      }
    }

    if (!foundKeyword) {
      // 查找下一个关键字的位置
      let nextKeywordIndex = remainingText.length;

      for (const keyword of keywords) {
        const index = remainingText.toLowerCase().indexOf(keyword.toLowerCase());
        if (index > 0 && index < nextKeywordIndex) {
          nextKeywordIndex = index;
        }
      }

      // 添加普通文本
      textParts.push({
        text: remainingText.substring(0, nextKeywordIndex),
        options: {
          color: '000000',
          fontSize: baseFontSize,
          fontFace: 'Microsoft YaHei'
        }
      });
      remainingText = remainingText.substring(nextKeywordIndex);
    }
  }

  console.log(`🎨 文本处理完成，生成 ${textParts.length} 个文本片段`);
  return textParts;
}

// 兼容性函数（保留原有接口）
function highlightKeywords(text) {
  // 简单返回原文本，实际高亮在PPT生成时处理
  return text;
}

// 解析标题，识别关键字并设置样式
function parseTitle(title) {
  // 定义关键字模式（可以根据需要扩展）
  const keywordPatterns = [
    /(\d+)/g,                    // 数字
    /(AI|人工智能|智能|技术|创新|发展|未来|数字化|自动化)/gi,  // 技术相关
    /(重要|关键|核心|主要|首要|优势|特色|亮点)/gi,           // 重要性词汇
    /(解决方案|策略|方法|方式|模式|系统|平台|框架)/gi,       // 方案类
    /(提升|优化|改进|增强|加强|推进|促进)/gi,               // 动作词
  ];

  const parts = [];
  let lastIndex = 0;
  const matches = [];

  // 收集所有关键字匹配
  keywordPatterns.forEach(pattern => {
    let match;
    while ((match = pattern.exec(title)) !== null) {
      matches.push({
        text: match[0],
        start: match.index,
        end: match.index + match[0].length
      });
    }
  });

  // 按位置排序
  matches.sort((a, b) => a.start - b.start);

  // 构建文本部分数组
  matches.forEach(match => {
    // 添加关键字前的普通文本
    if (match.start > lastIndex) {
      parts.push({
        text: title.substring(lastIndex, match.start),
        options: { fontSize: 44, bold: true, color: '2D3748' }
      });
    }

    // 添加关键字（红色、加粗、稍大）
    parts.push({
      text: match.text,
      options: { fontSize: 48, bold: true, color: 'E53E3E' }
    });

    lastIndex = match.end;
  });

  // 添加剩余的普通文本
  if (lastIndex < title.length) {
    parts.push({
      text: title.substring(lastIndex),
      options: { fontSize: 44, bold: true, color: '2D3748' }
    });
  }

  // 如果没有找到关键字，返回整个标题
  if (parts.length === 0) {
    parts.push({
      text: title,
      options: { fontSize: 44, bold: true, color: '2D3748' }
    });
  }

  return parts;
}

// 解析内容，识别关键字并设置样式
function parseContent(content) {
  const keywordPatterns = [
    /(\d+%|\d+倍|\d+年|\d+个|\d+项|\d+种)/g,              // 数据
    /(提高|增加|减少|降低|优化|改善|提升)/gi,               // 效果词
    /(重要|关键|核心|主要|显著|明显|突出)/gi,               // 强调词
    /(技术|方案|系统|平台|工具|方法|模式)/gi,               // 技术词
  ];

  const parts = [];
  let lastIndex = 0;
  const matches = [];

  keywordPatterns.forEach(pattern => {
    let match;
    while ((match = pattern.exec(content)) !== null) {
      matches.push({
        text: match[0],
        start: match.index,
        end: match.index + match[0].length
      });
    }
  });

  matches.sort((a, b) => a.start - b.start);

  matches.forEach(match => {
    if (match.start > lastIndex) {
      parts.push({
        text: content.substring(lastIndex, match.start),
        options: { fontSize: 20, color: '2D3748' }
      });
    }

    parts.push({
      text: match.text,
      options: { fontSize: 20, bold: true, color: 'E53E3E' }
    });

    lastIndex = match.end;
  });

  if (lastIndex < content.length) {
    parts.push({
      text: content.substring(lastIndex),
      options: { fontSize: 20, color: '2D3748' }
    });
  }

  if (parts.length === 0) {
    parts.push({
      text: content,
      options: { fontSize: 20, color: '2D3748' }
    });
  }

  return parts;
}

// 文本解析为大纲的辅助函数
function parseTextToOutline(text) {
  const lines = text.split('\n').filter(line => line.trim());
  const outline = {
    title: '演示文稿',
    slides: []
  };

  let currentSlide = null;

  for (const line of lines) {
    const trimmed = line.trim();

    // 检测标题（以#开头或数字开头）
    if (trimmed.match(/^#+\s+/) || trimmed.match(/^\d+[\.\)]\s+/)) {
      if (currentSlide) {
        outline.slides.push(currentSlide);
      }
      currentSlide = {
        title: trimmed.replace(/^#+\s+/, '').replace(/^\d+[\.\)]\s+/, ''),
        points: []
      };
    }
    // 检测要点（以-或*开头）
    else if (trimmed.match(/^[-\*]\s+/)) {
      if (currentSlide) {
        currentSlide.points.push(trimmed.replace(/^[-\*]\s+/, ''));
      }
    }
    // 其他非空行作为要点
    else if (trimmed && currentSlide) {
      currentSlide.points.push(trimmed);
    }
  }

  if (currentSlide) {
    outline.slides.push(currentSlide);
  }

  // 如果没有解析到幻灯片，创建一个默认的
  if (outline.slides.length === 0) {
    outline.slides.push({
      title: '主要内容',
      points: ['内容概述']
    });
  }

  return outline;
}

/**
 * @route POST /api/chat/generate-ppt
 * @desc 基于大纲生成PPT文件
 * @access Private
 */
router.post('/generate-ppt', authenticateToken, asyncHandler(async (req, res) => {
  try {
    const { outline } = req.body;

    if (!outline || !outline.title || !Array.isArray(outline.slides)) {
      return res.status(400).json({ error: '大纲格式不正确' });
    }

    console.log('生成PPT请求:', { title: outline.title, slideCount: outline.slides.length });

    // 导入pptxgenjs
    const PptxGenJS = require('pptxgenjs');
    const pptx = new PptxGenJS();

    // 设置PPT属性
    pptx.author = 'SCAIA AI Assistant';
    pptx.company = 'SCAIA';
    pptx.title = outline.title;
    pptx.subject = '由AI生成的演示文稿';

    // 定义设计主题颜色
    const theme = {
      primary: 'cc0000',      // 关键字红色
      secondary: '000000',    // 黑色文字
      accent: 'cc0000',       // 强调色
      background: 'FFFFFF',   // 白色背景
      text: '000000',         // 黑色文字
      lightText: '666666'     // 浅灰文字
    };

    // 创建标题页
    const titleSlide = pptx.addSlide();

    // 添加背景色
    titleSlide.background = { color: 'FFFFFF' };

    // 主标题 - 右对齐布局，突出关键字
    const titleParts = createTextWithRedKeywords(outline.title, [], 36);
    titleSlide.addText(titleParts, {
      x: 3,      // 左边留白3英寸
      y: 1.5,
      w: 6.5,    // 右侧内容区域
      h: 2.5,
      fontSize: 36,
      bold: true,
      align: 'right',  // 右对齐
      fontFace: 'Microsoft YaHei',
      color: theme.text
    });

    // 副标题 - 右对齐，简洁设计
    titleSlide.addText('由 SCAIA AI 助手智能生成', {
      x: 3,
      y: 4.5,
      w: 6.5,
      h: 0.8,
      fontSize: 15,
      align: 'right',
      fontFace: 'Microsoft YaHei',
      color: theme.text,
      italic: true
    });

    titleSlide.addText(new Date().toLocaleDateString('zh-CN', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    }), {
      x: 3,
      y: 6.0,
      w: 6.5,
      h: 0.5,
      fontSize: 13,
      align: 'right',
      fontFace: 'Microsoft YaHei',
      color: theme.lightText
    });

    // 简洁设计，不添加装饰元素

    // 创建内容页
    outline.slides.forEach((slideData, index) => {
      const slide = pptx.addSlide();

      // 添加背景
      slide.background = { color: 'FFFFFF' };

      // 添加标题（右对齐，突出关键字）
      const titleParts = createTextWithRedKeywords(slideData.title, slideData.keywords || [], 27);
      slide.addText(titleParts, {
        x: 3,      // 左边留白
        y: 0.8,
        w: 6.5,    // 右侧内容区域
        h: 1.2,
        fontSize: 27,
        bold: true,
        align: 'right',
        fontFace: 'Microsoft YaHei',
        color: theme.text
      });

      // 添加要点（右对齐布局，简洁设计）
      if (slideData.points && slideData.points.length > 0) {
        slideData.points.forEach((point, pointIndex) => {
          const yPos = 2.5 + (pointIndex * 0.8);

          // 处理要点数据格式
          let pointText, pointKeywords;
          if (typeof point === 'string') {
            pointText = point;
            pointKeywords = [];
          } else if (point && typeof point === 'object') {
            pointText = point.text || point.toString();
            pointKeywords = point.keywords || [];
          } else {
            pointText = String(point);
            pointKeywords = [];
          }

          console.log(`要点 ${pointIndex + 1}:`, { pointText, pointKeywords });

          // 添加要点文本（右对齐，突出关键字）
          const pointParts = createTextWithRedKeywords(pointText, pointKeywords, 17);
          slide.addText(pointParts, {
            x: 3,      // 左边留白
            y: yPos,
            w: 6.5,    // 右侧内容区域
            h: 0.6,
            fontSize: 17,
            color: theme.text,
            lineSpacing: 25,
            align: 'right',  // 右对齐
            fontFace: 'Microsoft YaHei',
            bold: false
          });
        });
      }

      // 简洁的页脚
      slide.addText(`${index + 1} / ${outline.slides.length}`, {
        x: 8.5,
        y: 7.25,
        w: 1.4,
        h: 0.2,
        fontSize: 10,
        align: 'right',
        fontFace: 'Microsoft YaHei',
        color: theme.lightText,
        bold: true
      });

      // 添加SCAIA标识
      slide.addText('SCAIA', {
        x: 0.5,
        y: 7.25,
        w: 2,
        h: 0.2,
        fontSize: 8,
        align: 'left',
        fontFace: 'Microsoft YaHei',
        color: theme.primary,
        bold: true
      });
    });

    // 生成PPT文件
    const fileName = `${outline.title.replace(/[^\w\s-]/g, '').replace(/\s+/g, '_')}_${Date.now()}.pptx`;
    const filePath = `/tmp/${fileName}`;

    await pptx.writeFile({ fileName: filePath });

    console.log('PPT文件生成成功:', filePath);

    // 设置响应头并发送文件
    res.setHeader('Content-Type', 'application/vnd.openxmlformats-officedocument.presentationml.presentation');
    res.setHeader('Content-Disposition', `attachment; filename="${encodeURIComponent(fileName)}"`);

    // 读取文件并发送
    const fs = require('fs');
    const fileBuffer = fs.readFileSync(filePath);

    // 清理临时文件
    fs.unlinkSync(filePath);

    res.send(fileBuffer);

  } catch (error) {
    console.error('生成PPT失败:', error);
    res.status(500).json({
      error: '生成PPT失败',
      message: error.message
    });
  }
}));

// 导出路由
module.exports = router;