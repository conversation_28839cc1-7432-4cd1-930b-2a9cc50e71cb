/**
 * 数据验证中间件
 * 
 * 功能说明：
 * - 使用Joi进行数据验证
 * - 提供常用的验证规则
 * - 统一的错误处理
 * - 自定义验证器
 */

const Joi = require('joi');

/**
 * 创建验证中间件
 * @param {Object} schema - Joi验证模式
 * @param {string} source - 验证数据源 ('body', 'query', 'params')
 * @returns {Function} Express中间件函数
 */
const validate = (schema, source = 'body') => {
  return (req, res, next) => {
    const data = req[source];
    
    if (!schema || typeof schema.validate !== 'function') {
      throw new Error('Validation schema is undefined or does not have a validate method.');
    }
    const { error, value } = schema.validate(data, {
      abortEarly: false, // 返回所有验证错误
      allowUnknown: true, // 允许未知字段
      stripUnknown: true // 移除未知字段
    });
    
    if (error) {
      const errorDetails = error.details.map(detail => ({
        field: detail.path.join('.'),
        message: detail.message,
        value: detail.context.value
      }));
      
      return res.status(400).json({
        status: 'error',
        message: '数据验证失败',
        code: 'VALIDATION_ERROR',
        details: errorDetails
      });
    }
    
    // 将验证后的数据替换原始数据
    req[source] = value;
    next();
  };
};

// ==================== 用户相关验证模式 ====================

/**
 * 用户注册验证模式
 */
const userRegisterSchema = Joi.object({
  username: Joi.string()
    .alphanum()
    .min(3)
    .max(30)
    .required()
    .messages({
      'string.alphanum': '用户名只能包含字母和数字',
      'string.min': '用户名至少需要3个字符',
      'string.max': '用户名不能超过30个字符',
      'any.required': '用户名是必填项'
    }),
    
  password: Joi.string()
    .min(6)
    .max(128)
    .required()
    .messages({
      'string.min': '密码至少需要6个字符',
      'string.max': '密码不能超过128个字符',
      'any.required': '密码是必填项'
    }),
    
  displayName: Joi.string()
    .min(1)
    .max(100)
    .required()
    .messages({
      'string.min': '显示名称不能为空',
      'string.max': '显示名称不能超过100个字符',
      'any.required': '显示名称是必填项'
    }),
    
  role: Joi.string()
    .valid('user', 'admin')
    .default('user')
    .messages({
      'any.only': '角色只能是user或admin'
    })
});

/**
 * 用户登录验证模式
 */
const userLoginSchema = Joi.object({
  username: Joi.string()
    .required()
    .messages({
      'any.required': '用户名是必填项'
    }),
    
  password: Joi.string()
    .required()
    .messages({
      'any.required': '密码是必填项'
    })
});

/**
 * 用户更新验证模式
 */
const userUpdateSchema = Joi.object({
  displayName: Joi.string()
    .min(1)
    .max(100)
    .messages({
      'string.min': '显示名称不能为空',
      'string.max': '显示名称不能超过100个字符'
    }),
    
  avatarUrl: Joi.string()
    .uri()
    .allow('')
    .messages({
      'string.uri': '头像URL格式不正确'
    }),
    
  currentPassword: Joi.string()
    .when('newPassword', {
      is: Joi.exist(),
      then: Joi.required(),
      otherwise: Joi.optional()
    })
    .messages({
      'any.required': '修改密码时需要提供当前密码'
    }),
    
  newPassword: Joi.string()
    .min(6)
    .max(128)
    .messages({
      'string.min': '新密码至少需要6个字符',
      'string.max': '新密码不能超过128个字符'
    })
});

// ==================== 聊天相关验证模式 ====================

/**
 * 创建聊天会话验证模式
 */
const chatSessionCreateSchema = Joi.object({
  title: Joi.string()
    .min(1)
    .max(255)
    .required()
    .messages({
      'string.min': '会话标题不能为空',
      'string.max': '会话标题不能超过255个字符',
      'any.required': '会话标题是必填项'
    }),
    
  modelId: Joi.string()
    .max(50)
    .messages({
      'string.max': '模型ID不能超过50个字符'
    }),
    
  allowWebSearch: Joi.boolean()
    .default(false)
});

/**
 * 发送消息验证模式
 */
const chatMessageSchema = Joi.object({
  modelId: Joi.alternatives()
    .try(Joi.string(), Joi.number())
    .required()
    .messages({
      'any.required': '模型数据丢失'
   }),
  sessionId: Joi.alternatives()
    .try(Joi.string(), Joi.number(), Joi.valid(null))
    .optional(),
  prompt: Joi.string()
    .min(1)
    .max(10000)
    .required()
    .messages({
      'string.min': '消息内容不能为空',
      'string.max': '消息内容不能超过10000个字符',
      'any.required': '消息内容是必填项'
    }),
    
   message: Joi.array()
    .items(Joi.object({
      role: Joi.string(),
      timestamp: Joi.string().max(255),
      content: Joi.string().allow('')
    }))
    .default([])
});

// ==================== 知识库相关验证模式 ====================

/**
 * 知识库项目创建验证模式
 */
const knowledgeItemSchema = Joi.object({
  title: Joi.string()
    .min(1)
    .max(255)
    .required()
    .messages({
      'string.min': '标题不能为空',
      'string.max': '标题不能超过255个字符',
      'any.required': '标题是必填项'
    }),
    
  description: Joi.string()
    .max(1000)
    .allow('')
    .messages({
      'string.max': '描述不能超过1000个字符'
    }),
    
  categoryId: Joi.number()
    .integer()
    .positive()
    .messages({
      'number.positive': '分类ID必须是正整数'
    }),
    
  tags: Joi.array()
    .items(Joi.string().max(50))
    .default([])
    .messages({
      'string.max': '标签长度不能超过50个字符'
    })
});

// ==================== 大师库相关验证模式 ====================

/**
 * 大师信息验证模式
 */
const masterItemSchema = Joi.object({
  name: Joi.string()
    .min(1)
    .max(100)
    .required()
    .messages({
      'string.min': '大师姓名不能为空',
      'string.max': '大师姓名不能超过100个字符',
      'any.required': '大师姓名是必填项'
    }),
    
  categoryId: Joi.string()
    .max(50)
    .required()
    .messages({
      'string.max': '分类ID不能超过50个字符',
      'any.required': '分类是必填项'
    }),
    
  description: Joi.string()
    .min(1)
    .max(2000)
    .required()
    .messages({
      'string.min': '描述不能为空',
      'string.max': '描述不能超过2000个字符',
      'any.required': '描述是必填项'
    }),
    
  avatar_url: Joi.string()
    .uri()
    .allow('')
    .messages({
      'string.uri': '图片URL格式不正确'
    }),
    
  tags: Joi.array()
    .items(Joi.string().max(50))
    .default([])
    .messages({
      'string.max': '标签长度不能超过50个字符'
    }),
    
  properties: Joi.object()
    .pattern(Joi.string(), Joi.string())
    .default({})
});

// ==================== 图片库相关验证模式 ====================

/**
 * 图片项目验证模式
 */
const imageItemSchema = Joi.object({
  name: Joi.string()
    .min(1)
    .max(255)
    .required()
    .messages({
      'string.min': '图片名称不能为空',
      'string.max': '图片名称不能超过255个字符',
      'any.required': '图片名称是必填项'
    }),
    
  categoryId: Joi.number()
    .integer()
    .positive()
    .messages({
      'number.positive': '分类ID必须是正整数'
    }),
    
  url: Joi.string()
    .uri()
    .required()
    .messages({
      'string.uri': '图片URL格式不正确',
      'any.required': '图片URL是必填项'
    }),
    
  prompt: Joi.string()
    .max(2000)
    .allow('')
    .messages({
      'string.max': '提示词不能超过2000个字符'
    }),
    
  promptData: Joi.object()
    .default({}),
    
  tags: Joi.array()
    .items(Joi.string().max(50))
    .default([])
    .messages({
      'string.max': '标签长度不能超过50个字符'
    })
});

// ==================== 话术库相关验证模式 ====================

/**
 * 脚本验证模式
 */
const scriptSchema = Joi.object({
  title: Joi.string()
    .min(1)
    .max(255)
    .required()
    .messages({
      'string.min': '脚本名称不能为空',
      'string.max': '脚本名称不能超过255个字符',
      'any.required': '脚本名称是必填项'
    }),

  categoryId: Joi.string()
    .max(50)
    .required()
    .messages({
      'string.max': '分类ID不能超过50个字符',
      'any.required': '分类是必填项'
    }),
    
  description: Joi.string()
    .max(1000)
    .allow('')
    .messages({
      'string.max': '描述不能超过1000个字符'
    }),
    
  content: Joi.string()
    .min(1)
    .max(10000)
    .required()
    .messages({
      'string.min': '脚本内容不能为空',
      'string.max': '脚本内容不能超过10000个字符',
      'any.required': '脚本内容是必填项'
    }),
    
  isFunction: Joi.boolean()
    .default(false),
    
  parameters: Joi.array()
    .items(Joi.object({
      name: Joi.string().max(100).required(),
      type: Joi.string().valid('text', 'select', 'number').required(),
      defaultValue: Joi.string().allow(''),
      options: Joi.string().allow('')
    }))
    .default([]),
    
  tags: Joi.array()
    .items(Joi.string().max(50))
    .default([])
    .messages({
      'string.max': '标签长度不能超过50个字符'
    })
});

// ==================== 设置相关验证模式 ====================

/**
 * 用户设置验证模式
 */
const userSettingsSchema = Joi.object({
  theme: Joi.string()
    .valid('light', 'dark', 'system')
    .default('light')
    .messages({
      'any.only': '主题只能是light、dark或system'
    }),
    
  primaryColor: Joi.string()
    .max(20)
    .default('blue')
    .messages({
      'string.max': '主色调不能超过20个字符'
    }),
    
  fontSize: Joi.number()
    .integer()
    .min(12)
    .max(20)
    .default(14)
    .messages({
      'number.min': '字体大小不能小于12',
      'number.max': '字体大小不能大于20'
    }),
    
  animations: Joi.boolean()
    .default(true),
    
  language: Joi.string()
    .valid('zh-CN', 'en-US', 'ja-JP', 'ko-KR')
    .default('zh-CN')
    .messages({
      'any.only': '语言只能是zh-CN、en-US、ja-JP或ko-KR'
    }),
    
  dateFormat: Joi.string()
    .max(20)
    .default('YYYY-MM-DD')
    .messages({
      'string.max': '日期格式不能超过20个字符'
    }),
    
  desktopNotifications: Joi.boolean()
    .default(true),
    
  soundNotifications: Joi.boolean()
    .default(true),
    
  defaultChatModel: Joi.string()
    .max(50)
    .allow('')
    .messages({
      'string.max': '默认聊天模型ID不能超过50个字符'
    }),
    
  defaultImageModel: Joi.string()
    .max(50)
    .allow('')
    .messages({
      'string.max': '默认图像模型ID不能超过50个字符'
    })
});

// ==================== 分页和搜索验证模式 ====================

/**
 * 分页参数验证模式
 */
const paginationSchema = Joi.object({
  page: Joi.number()
    .integer()
    .min(1)
    .default(1)
    .messages({
      'number.min': '页码必须大于0'
    }),
    
  limit: Joi.number()
    .integer()
    .min(1)
    .max(100)
    .default(10)
    .messages({
      'number.min': '每页数量必须大于0',
      'number.max': '每页数量不能超过100'
    }),
    
  sortBy: Joi.string()
    .default('created_at')
    .messages({
      'string.base': '排序字段必须是字符串'
    }),
    
  sortOrder: Joi.string()
    .valid('ASC', 'DESC')
    .default('DESC')
    .messages({
      'any.only': '排序顺序只能是ASC或DESC'
    }),
    
  search: Joi.string()
    .allow('')
    .max(100)
    .messages({
      'string.max': '搜索内容不能超过100个字符'
    }),
  
  categoryId: Joi.string()
    .allow('')
    .max(50)
});

// ==================== 提示词库相关验证模式 ====================

/**
 * 提示词项目验证模式
 */
const promptItemSchema = Joi.object({
  title: Joi.string()
    .min(1)
    .max(255)
    .required()
    .messages({
      'string.min': '标题不能为空',
      'string.max': '标题不能超过255个字符',
      'any.required': '标题是必填项'
    }),
    
  categoryId: Joi.number()
    .integer()
    .positive()
    .required()
    .messages({
      'number.base': '分类ID必须是数字',
      'number.integer': '分类ID必须是整数',
      'number.positive': '分类ID必须是正数',
      'any.required': '分类是必填项'
    }),
    
  description: Joi.string()
    .max(1000)
    .allow('')
    .messages({
      'string.max': '描述不能超过1000个字符'
    }),
    
  content: Joi.string()
    .min(1)
    .max(10000)
    .required()
    .messages({
      'string.min': '内容不能为空',
      'string.max': '内容不能超过10000个字符',
      'any.required': '内容是必填项'
    }),
    
  isPublic: Joi.boolean()
    .default(false)
});

module.exports = {
  validate,
  userRegisterSchema,
  userLoginSchema,
  userUpdateSchema,
  chatSessionCreateSchema,
  chatMessageSchema,
  knowledgeItemSchema,
  masterItemSchema,
  imageItemSchema,
  scriptSchema,
  userSettingsSchema,
  paginationSchema,
  promptItemSchema
};