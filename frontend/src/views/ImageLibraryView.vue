<template>
  <div class="image-library-view">
    <h1 class="page-title">图像库</h1>
    <p class="page-description">管理您生成和收藏的设计图像，方便重用和参考</p>
    
    <div class="library-container">
      <div class="action-bar">
        <div class="filter-controls">
          <select v-model="filterCategory" class="form-input select-input">
            <option value="all">所有分类</option>
            <option v-for="category in categories" :key="category.id" :value="category.id">
              {{ category.name }}
            </option>
          </select>
          <select v-model="sortOption" class="form-input select-input">
            <option value="newest">最新优先</option>
            <option value="oldest">最早优先</option>
            <option value="name_asc">名称 A-Z</option>
            <option value="name_desc">名称 Z-A</option>
          </select>
        </div>
        <div class="search-box">
          <input 
            type="text" 
            v-model="searchQuery" 
            class="form-input" 
            placeholder="搜索图像库..."
          >
          <button class="search-btn" @click="searchImages">
            🔍
          </button>
        </div>
      </div>
      
      <div class="controls-row">
        <button class="btn btn-primary" @click="showUploadModal = true">
          📤 上传图片
        </button>
        <div class="view-controls">
          <button 
            class="view-btn" 
            :class="{ active: viewMode === 'grid' }" 
            @click="viewMode = 'grid'"
            title="网格视图"
          >
            📱
          </button>
          <button 
            class="view-btn" 
            :class="{ active: viewMode === 'list' }" 
            @click="viewMode = 'list'"
            title="列表视图"
          >
            📋
          </button>
        </div>
      </div>
      
      <div class="image-library-content">
        <div v-if="filteredImages.length === 0" class="empty-state">
          <div class="empty-icon">🖼️</div>
          <h3>图像库为空</h3>
          <p>在图像生成页面创建并保存图像到图像库</p>
        </div>
        
        <div v-else :class="['image-gallery', viewMode]">
          <!-- 网格视图 -->
          <div 
            v-if="viewMode === 'grid'" 
            v-for="image in filteredImages" 
            :key="image.id"
            class="image-item"
            @click="viewImageDetails(image)"
          >
            <div class="image-preview" :style="{ backgroundImage: `url(${image.url})` }">
              <div class="image-overlay">
                <div class="image-name">{{ image.name }}</div>
                <div class="image-category">{{ image.category }}</div>
              </div>
            </div>
          </div>
          
          <!-- 列表视图 -->
          <div 
            v-if="viewMode === 'list'" 
            v-for="image in filteredImages" 
            :key="image.id"
            class="image-list-item"
            @click="viewImageDetails(image)"
          >
            <div class="image-list-preview" :style="{ backgroundImage: `url(${image.url})` }"></div>
            <div class="image-list-content">
              <h3 class="image-list-name">{{ image.name }}</h3>
              <div class="image-list-meta">
                <span class="image-list-category">{{ image.category }}</span>
                <span class="image-list-date">{{ formatDate(image.createdAt) }}</span>
              </div>
              <div class="image-list-tags">
                <span 
                  v-for="(tag, tagIndex) in image.tags" 
                  :key="tagIndex"
                  class="tag"
                >
                  {{ tag }}
                </span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    
    <!-- 图像详情模态框 -->
    <div v-if="showDetailsModal" class="modal">
      <div class="modal-content image-detail-modal">
        <div class="modal-header">
          <h3>图像详情</h3>
          <button class="close-btn" @click="showDetailsModal = false">×</button>
        </div>
        <div class="modal-body">
          <div class="image-detail-container">
            <div class="image-detail-preview">
              <img :src="selectedImage.url" alt="图像预览" class="detail-image">
            </div>
            <div class="image-detail-info">
              <h2 class="detail-title">{{ selectedImage.name }}</h2>
              <p class="detail-meta">
                <span class="detail-category">{{ selectedImage.category }}</span>
                <span class="detail-date">创建于 {{ formatDate(selectedImage.createdAt) }}</span>
              </p>
              
              <div class="detail-section">

                <div v-if="selectedImage.promptFields && selectedImage.promptFields.length > 0" class="detail-prompt-fields">
                  <h4>提示词</h4>
                  <div v-for="(field, index) in selectedImage.promptFields" :key="index" class="prompt-field-display">
                    <span class="prompt-field-name">{{ field.name }}:</span>
                    <span class="prompt-field-value">{{ field.value }}</span>
                  </div>
                </div>
              </div>
              
              <div class="detail-section">
                <h4>标签</h4>
                <div class="detail-tags">
                  <span 
                    v-for="(tag, tagIndex) in selectedImage.tags" 
                    :key="tagIndex"
                    class="tag"
                  >
                    {{ tag }}
                  </span>
                  <span v-if="!selectedImage.tags || selectedImage.tags.length === 0" class="no-tags">无标签</span>
                </div>
              </div>
              

            </div>
          </div>
        </div>
        <div class="modal-footer">
          <button class="btn btn-danger" @click="deleteImage(selectedImage)">删除</button>
          <button class="btn btn-secondary" @click="showDetailsModal = false">关闭</button>
          <button class="btn btn-primary" @click="editImage(selectedImage)">编辑</button>
          <button class="btn btn-primary" @click="downloadImage(selectedImage)">
            <span class="btn-icon">💾</span> 下载
          </button>
          <button class="btn btn-primary" @click="useInGenerator(selectedImage)">
            <span class="btn-icon">🔄</span> 在生成器中使用
          </button>
        </div>
      </div>
    </div>
    
    <!-- 编辑图像模态框 -->
    <div v-if="showEditModal" class="modal">
      <div class="modal-content">
        <div class="modal-header">
          <h3>编辑图像</h3>
          <button class="close-btn" @click="showEditModal = false">×</button>
        </div>
        <div class="modal-body">
          <div class="form-group">
            <label class="form-label">名称</label>
            <input type="text" v-model="editData.name" class="form-input">
          </div>
          
          <div class="form-group">
            <label class="form-label">分类</label>
            <select v-model="editData.category" class="form-input">
              <option v-for="category in categories" :key="category.id" :value="category.id">
                {{ category.name }}
              </option>
            </select>
          </div>
          
          <div class="form-group">
            <label class="form-label">提示词</label>
            <div class="structured-prompt-inputs">
              <div v-for="(field, index) in editData.promptFields" :key="index" class="prompt-field">
                <div class="prompt-field-header">
                <span class="field-name-text">{{ field.name }}</span>
              </div>
                <input type="text" v-model="field.value" class="form-input" :placeholder="'输入' + field.name + '内容'">
              </div>
            </div>
            <div class="preview-prompt">
              <h4>所有提示词合集显示</h4>
              <div class="prompt-box">{{ fullPrompt }}</div>
            </div>
          </div>
          
          <div class="form-group">
            <label class="form-label">标签</label>
            <input type="text" v-model="editData.tagsString" class="form-input" placeholder="用逗号分隔多个标签">
          </div>
        </div>
        <div class="modal-footer">
          <button class="btn btn-secondary" @click="showEditModal = false">取消</button>
          <button class="btn btn-primary" @click="saveImageEdit">保存</button>
        </div>
      </div>
    </div>
  </div>
  
  <!-- 上传图片模态框 -->
  <div v-if="showUploadModal" class="modal">
    <div class="modal-content">
      <div class="modal-header">
        <h3>上传图片</h3>
        <button class="close-btn" @click="showUploadModal = false">×</button>
      </div>
      <div class="modal-body">
        <div class="form-group">
          <label class="form-label">名称</label>
          <input type="text" v-model="uploadData.name" class="form-input" placeholder="请输入图片名称">
        </div>
        
        <div class="form-group">
          <label class="form-label">分类</label>
          <select v-model="uploadData.category" class="form-input">
            <option v-for="category in categories" :key="category.id" :value="category.id">
              {{ category.name }}
            </option>
          </select>
        </div>
        
        <div class="form-group">
            <label class="form-label">提示词</label>
            <div class="structured-prompt-inputs">
              <div v-for="(field, index) in uploadData.promptFields" :key="index" class="prompt-field">
                <div class="prompt-field-header">
                <span class="field-name-text">{{ field.name }}</span>
              </div>
              <input type="text" v-model="field.value" class="form-input" :placeholder="'输入' + field.name + '内容'">
            </div>
          </div>
          <div class="preview-prompt">
            <h4>所有提示词合集显示</h4>
            <div class="prompt-box">{{ uploadFullPrompt }}</div>
          </div>
        </div>
        
        <div class="form-group">
          <label class="form-label">标签</label>
          <input type="text" v-model="uploadData.tagsString" class="form-input" placeholder="用逗号分隔多个标签">
        </div>
        
        <div class="form-group">
          <label class="form-label">选择图片</label>
          <div class="upload-area" @click="triggerFileInput" @dragover.prevent @drop.prevent="handleFileDrop">
            <input 
              type="file" 
              ref="fileInput" 
              @change="handleFileSelect" 
              accept="image/*" 
              style="display: none"
            >
            <div v-if="!uploadData.imagePreview" class="upload-placeholder">
              <div class="upload-icon">📁</div>
              <p>点击选择或拖放图片到此处</p>
              <p class="upload-hint">支持 JPG、PNG、GIF 格式</p>
            </div>
            <div v-else class="upload-preview">
              <img :src="uploadData.imagePreview" alt="预览图片">
              <button class="remove-image-btn" @click.stop="removeUploadImage">×</button>
            </div>
          </div>
        </div>
      </div>
      <div class="modal-footer">
        <button class="btn btn-secondary" @click="showUploadModal = false">取消</button>
        <button class="btn btn-primary" @click="uploadImage" :disabled="!canUpload">上传</button>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue';
import { useRouter } from 'vue-router';
import { imageAPI, fetchPromptCategories } from '@/services/api';

const router = useRouter();
const fileInput = ref(null);

// 数据状态
const categories = ref([]);
const promptCategories = ref([]);
const images = ref([]);
const loading = ref(false);
const error = ref('');

// 状态变量
const viewMode = ref('grid');
const filterCategory = ref('all');
const sortOption = ref('newest');
const searchQuery = ref('');
const showDetailsModal = ref(false);
const showEditModal = ref(false);
const showUploadModal = ref(false);
const selectedImage = ref({});

// 加载分类数据
async function loadCategories() {
  try {
    const response = await imageAPI.getImageCategories();
    console.log('分类API响应:',Array.isArray(response.data), response);
    // API返回格式是 { status: 'success', message: '操作成功', data: [...] }
    if (response && response.success  && response.data && Array.isArray(response.data)) {
      categories.value = response.data;
    } else {
      console.warn('API返回的分类数据格式不正确:', response);
      categories.value = [];
    }
    
    // 设置默认分类（如果有分类的话）
    if (categories.value.length > 0 && !uploadData.value.category) {
      uploadData.value.category = categories.value[0].id;
    }
  } catch (err) {
    console.error('加载分类失败:', err);
    categories.value = [];
  }
}

// 新增：加载提示词分类数据
async function loadPromptCategories() {
  try {
    const response = await fetchPromptCategories();
    console.log('提示词分类API响应:', response);
    
    if (response && response.success  && response.data && Array.isArray(response.data)) {
      promptCategories.value = response.data;
    } else {
      console.warn('API返回的提示词分类数据格式不正确:', response);
      promptCategories.value = [];
    }
  } catch (err) {
    console.error('加载提示词分类失败:', err);
    promptCategories.value = [];
  }
}

// 新增：根据提示词分类生成默认的 promptFields
function generateDefaultPromptFields() {
  if (!promptCategories.value || promptCategories.value.length === 0) {
    // 如果没有分类数据，返回空数组，让用户手动添加字段
    return [];
  }
  
  // 根据提示词分类生成字段
  return promptCategories.value.map(category => ({
    name: category.name,
    value: ''
  }));
}

// 新增：生成动态的提示词数据映射
function generatePromptDataMapping() {
  const mapping = {};
  
  // 如果有提示词分类数据，使用分类数据生成映射
  if (promptCategories.value && promptCategories.value.length > 0) {
    promptCategories.value.forEach(category => {
      // 使用分类的 id 作为映射键，如果没有 id 则使用名称的小写形式
      const fieldKey = category.id || category.name.toLowerCase().replace(/\s+/g, '_');
      mapping[category.name] = fieldKey;
    });
  }
  
  return mapping;
}

// 新增：处理提示词字段到 promptData 的转换
function processPromptFieldsToData(promptFields) {
  const mapping = generatePromptDataMapping();
  const promptData = {};
  
  // 动态初始化所有可能的字段
  const allPossibleFields = new Set();
  
  // 从映射中收集所有可能的字段键
  Object.values(mapping).forEach(fieldKey => {
    allPossibleFields.add(fieldKey);
  });
  
  // 初始化所有字段为空字符串
  allPossibleFields.forEach(field => {
    promptData[field] = '';
  });
  
  // 处理每个提示词字段
  promptFields.forEach(field => {
    const mappedField = mapping[field.name];
    if (mappedField) {
      promptData[mappedField] = field.value;
    } else if (field.value) {
      // 如果没有映射，将内容添加到第一个字段或创建一个通用字段
      const firstField = Object.keys(promptData)[0] || 'general';
      if (!promptData[firstField]) {
        promptData[firstField] = '';
      }
      promptData[firstField] = (promptData[firstField] || '') + 
        (promptData[firstField] ? ', ' + field.name + ': ' + field.value : field.name + ': ' + field.value);
    }
  });
  
  return promptData;
}

// 加载数据函数
async function loadImages() {
  try {
    loading.value = true
    const response = await imageAPI.getImageList()
    // API返回格式是 { status: 'success', message: '操作成功', data: { items: [...] } }
    if (response && response.success  && response.data && response.data.items) {
      images.value = response.data.items
    } else {
      console.warn('API返回的图片数据格式不正确:', response);
      images.value = []
    }
    
    // 更新分类计数
    updateCategoryCounts()
  } catch (err) {
    console.error('加载图片库失败:', err)
    error.value = '加载图片库失败'
    images.value = [] // Clear images on error
  } finally {
    loading.value = false
  }
}

// 更新分类计数
function updateCategoryCounts() {
  if (!Array.isArray(categories.value)) {
    console.warn('categories.value 不是数组:', categories.value);
    return;
  }
  
  categories.value.forEach(cat => {
    if (cat.id === 'all') {
      cat.count = images.value.length
    } else {
      cat.count = images.value.filter(img => img.category === cat.name).length
    }
  })
}

// 编辑数据
const editData = ref({
  name: '',
  category: '',
  prompt: '',
  tagsString: '',
  promptFields: []
});

// 上传数据
const uploadData = ref({
  name: '',
  category: null, // 不设置默认值，等分类加载后再设置
  tagsString: '',
  imageFile: null,
  imagePreview: null,
  promptFields: []
});

// 判断是否可以上传
const canUpload = computed(() => {
  return uploadData.value.name.trim() !== '' && 
         uploadData.value.category !== null && 
         uploadData.value.imageFile !== null;
});

// 计算完整的提示词
const fullPrompt = computed(() => {
  if (!editData.value.promptFields || editData.value.promptFields.length === 0) {
    return '';
  }
  
  return editData.value.promptFields
    .filter(field => field.value.trim() !== '')
    .map(field => `${field.name}: ${field.value}`)
    .join('\n');
});

// 计算上传图片的完整提示词
const uploadFullPrompt = computed(() => {
  if (!uploadData.value.promptFields || uploadData.value.promptFields.length === 0) {
    return '';
  }
  
  return uploadData.value.promptFields
    .filter(field => field.value.trim() !== '')
    .map(field => `${field.name}: ${field.value}`)
    .join('\n');
});

// 添加新的提示词字段
function addPromptField() {
  editData.value.promptFields.push({ name: '', value: '' });
}

// 删除提示词字段
function removePromptField(index) {
  editData.value.promptFields.splice(index, 1);
}

// 添加上传图片的提示词字段
function addUploadPromptField() {
  uploadData.value.promptFields.push({ name: '', value: '' });
}

// 删除上传图片的提示词字段
function removeUploadPromptField(index) {
  uploadData.value.promptFields.splice(index, 1);
}

// 触发文件选择
function triggerFileInput() {
  fileInput.value.click();
}

// 处理文件选择
function handleFileSelect(event) {
  const file = event.target.files[0];
  if (file) {
    processSelectedFile(file);
  }
}

// 处理文件拖放
function handleFileDrop(event) {
  const file = event.dataTransfer.files[0];
  if (file && file.type.startsWith('image/')) {
    processSelectedFile(file);
  }
}

// 处理选择的文件
function processSelectedFile(file) {
  uploadData.value.imageFile = file;
  
  // 创建文件预览
  const reader = new FileReader();
  reader.onload = (e) => {
    uploadData.value.imagePreview = e.target.result;
  };
  reader.readAsDataURL(file);
  
  // 如果没有输入名称，使用文件名作为默认名称
  if (!uploadData.value.name) {
    uploadData.value.name = file.name.split('.')[0];
  }
}

// 移除上传图片
function removeUploadImage() {
  uploadData.value.imageFile = null;
  uploadData.value.imagePreview = null;
  fileInput.value.value = '';
}

// 上传图片
async function uploadImage() {
  if (!canUpload.value) {
    return;
  }
  
  try {
    loading.value = true;
    
    // 准备上传数据
    const imageData = {
      name: uploadData.value.name,
      url: uploadData.value.imagePreview,
      categoryId: uploadData.value.category,
      prompt: uploadFullPrompt.value,
      promptFields: [...uploadData.value.promptFields],
      tags: uploadData.value.tagsString.split(',').map(tag => tag.trim()).filter(tag => tag)
    };
    
    // 添加结构化提示词数据
    const promptData = processPromptFieldsToData(uploadData.value.promptFields);
    
    imageData.promptData = promptData;
    
    await imageAPI.createImage(imageData);
    
    // 重新加载数据
    await loadImages();
    
    // 重置上传数据
    uploadData.value = {
      name: '',
      category: null,
      tagsString: '',
      imageFile: null,
      imagePreview: null,
      promptFields: generateDefaultPromptFields()
    };
    
    showUploadModal.value = false;
    alert('图像上传成功！');
  } catch (err) {
    console.error('上传图片失败:', err);
    alert('上传图片失败: ' + err.message);
  } finally {
    loading.value = false;
  }
}

// 计算属性：过滤和排序后的图像
const filteredImages = computed(() => {
  let result = [...images.value];
  
  // 分类过滤
  if (filterCategory.value !== 'all') {
    // 根据分类id找到分类名称，然后与图片的category字段比较
    const selectedCategory = categories.value.find(cat => cat.id === filterCategory.value);
    if (selectedCategory) {
      result = result.filter(image => image.category === selectedCategory.name);
    }
  }
  
  // 搜索过滤
  if (searchQuery.value) {
    const query = searchQuery.value.toLowerCase();
    result = result.filter(image => {
      return (
        image.name.toLowerCase().includes(query) ||
        (image.prompt && image.prompt.toLowerCase().includes(query)) ||
        (image.tags && image.tags.some(tag => tag.toLowerCase().includes(query)))
      );
    });
  }
  
  // 排序
  result.sort((a, b) => {
    switch (sortOption.value) {
      case 'newest':
        return new Date(b.createdAt) - new Date(a.createdAt);
      case 'oldest':
        return new Date(a.createdAt) - new Date(b.createdAt);
      case 'name_asc':
        return a.name.localeCompare(b.name);
      case 'name_desc':
        return b.name.localeCompare(a.name);
      default:
        return 0;
    }
  });
  
  return result;
});

// 方法
function searchImages() {
  // 搜索已通过计算属性实现
}

function formatDate(date) {
  return new Intl.DateTimeFormat('zh-CN', {
    year: 'numeric',
    month: 'long',
    day: 'numeric'
  }).format(new Date(date));
}

function formatParamKey(key) {
  // 将下划线转换为空格并首字母大写
  return key
    .split('_')
    .map(word => word.charAt(0).toUpperCase() + word.slice(1))
    .join(' ');
}

function viewImageDetails(image) {
  selectedImage.value = image;
  showDetailsModal.value = true;
}

function editImage(image) {
  // 根据分类名称找到分类id
  const categoryObj = categories.value.find(cat => cat.name === image.category);
  const categoryId = categoryObj ? categoryObj.id : (categories.value.length > 0 ? categories.value[0].id : null);

  // 1. 始终从 promptCategories 生成基础的字段结构
  const baseFields = generateDefaultPromptFields();

  // 2. 如果图像有已保存的 promptFields，则用其值填充基础结构
  if (image.promptFields && image.promptFields.length > 0) {
    const existingFieldsMap = new Map(image.promptFields.map(f => [f.name, f.value]));
    baseFields.forEach(field => {
      if (existingFieldsMap.has(field.name)) {
        field.value = existingFieldsMap.get(field.name);
      }
    });
  }

  // 设置 editData，完全移除对旧 prompt 字段的引用
  editData.value = {
    name: image.name,
    category: categoryId,
    tagsString: image.tags ? image.tags.join(', ') : '',
    promptFields: baseFields
  };
  
  showDetailsModal.value = false;
  showEditModal.value = true;
}

async function saveImageEdit() {
  try {
    loading.value = true;
    
    // 准备更新数据
    const updateData = {
      title: editData.value.name,
      // description 由后端根据 promptFields 生成，前端不再发送
      categoryId: editData.value.category,
      tags: editData.value.tagsString.split(',').map(tag => tag.trim()).filter(tag => tag),
      promptFields: editData.value.promptFields
    };
    
    await imageAPI.updateImage(selectedImage.value.id, updateData);
    
    // 重新加载数据
    await loadImages();
    
    showEditModal.value = false;
    alert('图像更新成功！');
  } catch (err) {
    console.error('更新图片失败:', err);
    alert('更新图片失败: ' + err.message);
  } finally {
    loading.value = false;
  }
}

async function deleteImage(image) {
  if (confirm(`确定要删除 "${image.name}" 吗？此操作不可撤销。`)) {
    try {
      loading.value = true;
      await imageAPI.deleteImage(image.id);
      
      // 重新加载数据
      await loadImages();
      
      showDetailsModal.value = false;
      alert('图像已删除！');
    } catch (err) {
      console.error('删除图片失败:', err);
      alert('删除图片失败: ' + err.message);
    } finally {
      loading.value = false;
    }
  }
}

function downloadImage(image) {
  // 在实际应用中，这里会创建一个下载链接
  alert(`下载图像: ${image.name}\n在实际应用中，这将触发图像下载。`);
}

function useInGenerator(image) {
  // 导航到图像生成页面并填充结构化提示词数据
  showDetailsModal.value = false;
  
  // 准备结构化提示词数据
  const promptFields = {};
  
  // 如果图像有结构化提示词字段，使用它们
  if (image.promptFields && image.promptFields.length > 0) {
    // 使用动态映射处理提示词字段
    const mapping = generatePromptDataMapping();
    
    image.promptFields.forEach(field => {
      const mappedField = mapping[field.name];
      if (mappedField) {
        promptFields[mappedField] = field.value;
      } else if (field.value) {
        // 对于不匹配的字段，添加到第一个映射字段或创建一个通用字段
        const firstMappedField = Object.values(mapping)[0] || 'general';
        if (!promptFields[firstMappedField]) {
          promptFields[firstMappedField] = '';
        }
        promptFields[firstMappedField] = (promptFields[firstMappedField] || '') + 
          (promptFields[firstMappedField] ? ', ' + field.name + ': ' + field.value : field.name + ': ' + field.value);
      }
    });
  } else if (image.prompt) {
    // 如果没有结构化字段但有完整提示词，将其放入第一个映射字段
    const mapping = generatePromptDataMapping();
    const firstMappedField = Object.values(mapping)[0] || 'general';
    promptFields[firstMappedField] = image.prompt;
  }
  
  // 导航到图像生成页面并传递结构化提示词数据
  router.push({
    name: 'ImageGeneration',
    params: {},
    query: {
      reference: image.id,
      prompt: image.prompt,
      promptData: JSON.stringify(promptFields)
    }
  });
}
// 组件挂载时加载数据
onMounted(async () => {
  await loadCategories();
  await loadPromptCategories();
  await loadImages();
  
  // 初始化 uploadData 的 promptFields
  uploadData.value.promptFields = generateDefaultPromptFields();
});
</script>

<style scoped>
.image-library-view {
  padding: 20px;
  height: 100%;
  /* 移除overflow-y: auto，避免与App.vue中的滚动冲突 */
}

.page-title {
  font-size: 2rem;
  margin-bottom: 0.5rem;
  color: var(--color-heading);
}

.page-description {
  color: var(--color-text-light);
  margin-bottom: 2rem;
}

.library-container {
  background-color: var(--color-background-soft);
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
}

.action-bar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.controls-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.filter-controls {
  display: flex;
  gap: 10px;
  align-items: center;
}

.upload-btn {
  display: flex !important;
  flex-direction: row !important;
  align-items: center !important;
  gap: 5px;
  margin-left: 10px;
}

.upload-btn span {
  display: inline-block !important;
}

.select-input {
  min-width: 120px;
}

.search-box {
  display: flex;
  align-items: center;
  width: 300px;
}

.search-btn {
  background-color: var(--primary-color);
  color: white;
  border: none;
  border-radius: 0 4px 4px 0;
  padding: 8px 12px;
  cursor: pointer;
}

.view-controls {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}

.view-btn {
  background: none;
  border: 1px solid var(--color-border);
  border-radius: 4px;
  padding: 5px 10px;
  cursor: pointer;
  font-size: 1.2rem;
}

.view-btn.active {
  background-color: var(--primary-color);
  color: white;
  border-color: var(--primary-color);
}

/* 网格视图 */
.image-gallery.grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
  gap: 20px;
}

.image-item {
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  cursor: pointer;
  transition: transform 0.2s, box-shadow 0.2s;
  position: relative;
}

.image-item:hover {
  transform: translateY(-3px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.image-preview {
  height: 200px;
  background-size: cover;
  background-position: center;
  position: relative;
}

.image-overlay {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background: linear-gradient(to top, rgba(0,0,0,0.7), transparent);
  padding: 15px;
  color: white;
}

.image-name {
  font-weight: 500;
  margin-bottom: 5px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.image-category {
  font-size: 0.8rem;
  opacity: 0.8;
}

/* 列表视图 */
.image-gallery.list {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.image-list-item {
  display: flex;
  background-color: white;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  cursor: pointer;
  transition: transform 0.2s, box-shadow 0.2s;
}

.image-list-item:hover {
  transform: translateY(-3px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.image-list-preview {
  width: 150px;
  height: 100px;
  background-size: cover;
  background-position: center;
  flex-shrink: 0;
}

.image-list-content {
  padding: 15px;
  flex: 1;
}

.image-list-name {
  font-size: 1.1rem;
  margin-bottom: 5px;
  color: var(--color-heading);
}

.image-list-meta {
  display: flex;
  gap: 15px;
  font-size: 0.8rem;
  color: var(--color-text-light);
  margin-bottom: 10px;
}

.image-list-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 5px;
}

.tag {
  background-color: var(--color-background-mute);
  color: var(--color-text);
  padding: 3px 8px;
  border-radius: 4px;
  font-size: 0.8rem;
}

/* 空状态 */
.empty-state {
  text-align: center;
  padding: 40px 20px;
  color: var(--color-text-light);
}

.empty-icon {
  font-size: 4rem;
  margin-bottom: 20px;
}

/* 模态框样式 */
.modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.modal-content {
  background-color: white;
  border-radius: 8px;
  width: 90%;
  max-width: 600px;
  max-height: 90vh;
  overflow-y: auto;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
}

.image-detail-modal {
  max-width: 900px;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px 20px;
  border-bottom: 1px solid var(--color-border);
}

.modal-body {
  padding: 20px;
}

.modal-footer {
  padding: 15px 20px;
  border-top: 1px solid var(--color-border);
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}

.close-btn {
  background: none;
  border: none;
  font-size: 1.5rem;
  cursor: pointer;
  color: var(--color-text-light);
}

/* 详情模态框样式 */
.image-detail-container {
  display: flex;
  gap: 30px;
}

.image-detail-preview {
  flex: 1;
  max-width: 50%;
}

.detail-image {
  width: 100%;
  height: auto;
  border-radius: 8px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
}

.image-detail-info {
  flex: 1;
}

.detail-title {
  margin-bottom: 5px;
  color: var(--color-heading);
}

.detail-meta {
  display: flex;
  gap: 15px;
  color: var(--color-text-light);
  font-size: 0.9rem;
  margin-bottom: 20px;
}

.detail-section {
  margin-bottom: 20px;
}

.detail-section h4 {
  margin-bottom: 10px;
  color: var(--color-heading);
  font-size: 1.1rem;
}

.detail-prompt {
  background-color: var(--color-background-mute);
  padding: 15px;
  border-radius: 4px;
  font-style: italic;
  margin-bottom: 15px;
}

.detail-prompt-fields {
  margin-top: 15px;
}

.detail-prompt-fields h4 {
  margin-bottom: 10px;
  color: var(--color-heading);
  font-size: 1rem;
}

.detail-prompt-fields {
  font-style: normal;
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.prompt-field-display {
  display: flex;
  align-items: flex-start;
}

.prompt-field-name {
  font-weight: 500;
  min-width: 120px;
  color: var(--primary-color);
}

.prompt-field-value {
  flex: 1;
}

.detail-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  margin-bottom: 15px;
}

.params-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(180px, 1fr));
  gap: 10px;
}

.param-item {
  background-color: var(--color-background-soft);
  padding: 8px 12px;
  border-radius: 4px;
}

.param-key {
  font-weight: 500;
  margin-right: 5px;
}

.no-tags, .no-params {
  color: var(--color-text-light);
  font-style: italic;
}

/* 表单样式 */
.form-group {
  margin-bottom: 15px;
}

/* 结构化提示词输入框样式 */
.structured-prompt-inputs {
  display: flex;
  flex-direction: column;
  gap: 10px;
  margin-bottom: 15px;
}

.prompt-field {
  border: 1px solid var(--color-border);
  border-radius: 4px;
  padding: 10px;
  background-color: var(--color-background-soft);
}

.prompt-field-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 5px;
}

.field-name-input {
  width: 70%;
  font-weight: 500;
}

.field-name-text {
  font-weight: 500;
  color: var(--color-heading);
  padding: 5px 0;
  display: block;
  font-size: 0.95rem;
}

.remove-field-btn {
  background-color: #f8d7da;
  color: #721c24;
  border: none;
  border-radius: 50%;
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  font-size: 16px;
  line-height: 1;
}

.add-field-btn {
  background-color: var(--color-background-mute);
  color: var(--primary-color);
  border: 1px dashed var(--color-border);
  border-radius: 4px;
  padding: 8px;
  cursor: pointer;
  text-align: center;
  margin-top: 5px;
  transition: background-color 0.2s;
}

.add-field-btn:hover {
  background-color: var(--color-background);
}

.preview-prompt {
  margin-top: 15px;
}

.preview-prompt h4 {
  margin-bottom: 8px;
  font-size: 0.9rem;
  color: var(--color-text-light);
}

.prompt-box {
  background-color: var(--color-background-mute);
  padding: 10px;
  border-radius: 4px;
  font-family: monospace;
  white-space: pre-wrap;
  font-size: 0.9rem;
  max-height: 150px;
  overflow-y: auto;
}

.form-label {
  display: block;
  margin-bottom: 5px;
  font-weight: 500;
}

.btn-danger {
  background-color: #dc3545;
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 4px;
  cursor: pointer;
  transition: background-color 0.2s;
}

.btn-danger:hover {
  background-color: #c82333;
}

/* 上传区域样式 */
.upload-area {
  border: 2px dashed var(--color-border);
  border-radius: 8px;
  padding: 20px;
  text-align: center;
  cursor: pointer;
  transition: background-color 0.2s, border-color 0.2s;
  min-height: 200px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.upload-area:hover {
  background-color: var(--color-background-soft);
  border-color: var(--primary-color);
}

.upload-placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: var(--color-text-light);
}

.upload-icon {
  font-size: 3rem;
  margin-bottom: 10px;
  color: var(--color-text-light);
}

.upload-hint {
  font-size: 0.8rem;
  margin-top: 5px;
  opacity: 0.7;
}

.upload-preview {
  width: 100%;
  position: relative;
}

.upload-preview img {
  max-width: 100%;
  max-height: 300px;
  border-radius: 4px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.remove-image-btn {
  position: absolute;
  top: -10px;
  right: -10px;
  background-color: #f8d7da;
  color: #721c24;
  border: none;
  border-radius: 50%;
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  font-size: 16px;
  line-height: 1;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

/* 响应式调整 */
@media (max-width: 768px) {
  .action-bar {
    flex-direction: column;
    gap: 10px;
    align-items: stretch;
  }
  
  .filter-controls {
    flex-direction: column;
  }
  
  .search-box {
    width: 100%;
  }
  
  .image-detail-container {
    flex-direction: column;
  }
  
  .image-detail-preview {
    max-width: 100%;
  }
  
  .params-grid {
    grid-template-columns: 1fr;
  }
  
  .image-list-preview {
    width: 100px;
  }
  
  .upload-btn {
    margin-left: 0;
    margin-top: 10px;
    flex-direction: row !important; /* 确保在移动设备上也保持横向排列 */
    justify-content: center !important; /* 在移动设备上居中显示 */
    display: flex !important;
  }
}
</style>