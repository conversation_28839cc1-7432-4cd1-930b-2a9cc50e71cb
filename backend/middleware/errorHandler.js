/**
 * 错误处理中间件
 * 
 * 功能说明：
 * - 统一的错误处理机制
 * - 错误日志记录
 * - 开发和生产环境的不同错误响应
 * - 自定义错误类型处理
 */

const fs = require('fs');
const path = require('path');

/**
 * 自定义错误类
 */
class AppError extends Error {
  constructor(message, statusCode, code = null, details = null) {
    super(message);
    this.statusCode = statusCode;
    this.code = code;
    this.details = details;
    this.isOperational = true;
    
    Error.captureStackTrace(this, this.constructor);
  }
}

/**
 * 业务逻辑错误类
 */
class ValidationError extends AppError {
  constructor(message, details = null) {
    super(message, 400, 'VALIDATION_ERROR', details);
  }
}

/**
 * 认证错误类
 */
class AuthenticationError extends AppError {
  constructor(message = '认证失败') {
    super(message, 401, 'AUTHENTICATION_ERROR');
  }
}

/**
 * 授权错误类
 */
class AuthorizationError extends AppError {
  constructor(message = '权限不足') {
    super(message, 403, 'AUTHORIZATION_ERROR');
  }
}

/**
 * 资源未找到错误类
 */
class NotFoundError extends AppError {
  constructor(message = '资源未找到') {
    super(message, 404, 'NOT_FOUND_ERROR');
  }
}

/**
 * 冲突错误类
 */
class ConflictError extends AppError {
  constructor(message = '资源冲突') {
    super(message, 409, 'CONFLICT_ERROR');
  }
}

/**
 * 速率限制错误类
 */
class RateLimitError extends AppError {
  constructor(message = '请求过于频繁') {
    super(message, 429, 'RATE_LIMIT_ERROR');
  }
}

/**
 * 服务器内部错误类
 */
class InternalServerError extends AppError {
  constructor(message = '服务器内部错误') {
    super(message, 500, 'INTERNAL_SERVER_ERROR');
  }
}

/**
 * 记录错误日志
 * @param {Error} error - 错误对象
 * @param {Object} req - Express请求对象
 */
const logError = (error, req = null) => {
  const timestamp = new Date().toISOString();
  const logDir = path.join(__dirname, '../logs');
  const logFile = path.join(logDir, 'error.log');
  
  // 确保日志目录存在
  if (!fs.existsSync(logDir)) {
    fs.mkdirSync(logDir, { recursive: true });
  }
  
  // 构建日志信息
  const logData = {
    timestamp,
    error: {
      name: error.name,
      message: error.message,
      stack: error.stack,
      statusCode: error.statusCode,
      code: error.code,
      details: error.details
    }
  };
  
  // 添加请求信息
  if (req) {
    logData.request = {
      method: req.method,
      url: req.url,
      headers: req.headers,
      body: req.body,
      params: req.params,
      query: req.query,
      ip: req.ip,
      userAgent: req.get('User-Agent'),
      userId: req.user ? req.user.id : null
    };
  }
  
  // 写入日志文件
  const logEntry = JSON.stringify(logData, null, 2) + '\n\n';
  fs.appendFileSync(logFile, logEntry);
  
  // 在控制台输出错误信息（开发环境）
  if (process.env.NODE_ENV === 'development') {
    console.error('\n=== ERROR LOG ===');
    console.error('Time:', timestamp);
    console.error('Error:', error.message);
    if (req) {
      console.error('Request:', `${req.method} ${req.url}`);
      console.error('User ID:', req.user ? req.user.id : 'Anonymous');
    }
    console.error('Stack:', error.stack);
    console.error('================\n');
  }
};

/**
 * 处理数据库错误
 * @param {Error} error - 数据库错误
 * @returns {AppError} 转换后的应用错误
 */
const handleDatabaseError = (error) => {
  // MySQL错误处理
  if (error.code) {
    switch (error.code) {
      case 'ER_DUP_ENTRY':
        const field = error.sqlMessage.match(/for key '(.+?)'/)?.[1] || 'unknown';
        return new ConflictError(`${field} 已存在`);
        
      case 'ER_NO_REFERENCED_ROW_2':
        return new ValidationError('引用的资源不存在');
        
      case 'ER_ROW_IS_REFERENCED_2':
        return new ConflictError('无法删除，存在关联数据');
        
      case 'ER_DATA_TOO_LONG':
        return new ValidationError('数据长度超出限制');
        
      case 'ER_BAD_NULL_ERROR':
        const column = error.sqlMessage.match(/Column '(.+?)'/)?.[1] || 'unknown';
        return new ValidationError(`${column} 不能为空`);
        
      case 'ECONNREFUSED':
        return new InternalServerError('数据库连接失败');
        
      case 'ER_ACCESS_DENIED_ERROR':
        return new InternalServerError('数据库访问被拒绝');
        
      default:
        return new InternalServerError('数据库操作失败');
    }
  }
  
  return new InternalServerError('数据库错误');
};

/**
 * 处理JWT错误
 * @param {Error} error - JWT错误
 * @returns {AppError} 转换后的应用错误
 */
const handleJWTError = (error) => {
  if (error.name === 'JsonWebTokenError') {
    return new AuthenticationError('无效的访问令牌');
  }
  
  if (error.name === 'TokenExpiredError') {
    return new AuthenticationError('访问令牌已过期');
  }
  
  if (error.name === 'NotBeforeError') {
    return new AuthenticationError('访问令牌尚未生效');
  }
  
  return new AuthenticationError('令牌验证失败');
};

/**
 * 处理文件上传错误
 * @param {Error} error - 文件上传错误
 * @returns {AppError} 转换后的应用错误
 */
const handleMulterError = (error) => {
  if (error.code === 'LIMIT_FILE_SIZE') {
    return new ValidationError('文件大小超出限制');
  }
  
  if (error.code === 'LIMIT_FILE_COUNT') {
    return new ValidationError('文件数量超出限制');
  }
  
  if (error.code === 'LIMIT_UNEXPECTED_FILE') {
    return new ValidationError('不支持的文件字段');
  }
  
  return new ValidationError('文件上传失败');
};

/**
 * 404错误处理中间件
 */
const notFoundHandler = (req, res, next) => {
  const error = new NotFoundError(`路由 ${req.method} ${req.originalUrl} 不存在`);
  next(error);
};

/**
 * 全局错误处理中间件
 */
const errorHandler = (error, req, res, next) => {
  let appError = error;
  
  // 如果不是自定义错误，则转换为自定义错误
  if (!error.isOperational) {
    // 处理不同类型的错误
    if (error.name === 'ValidationError' && error.details) {
      // Joi验证错误
      appError = new ValidationError(error.message, error.details);
    } else if (error.code && error.code.startsWith('ER_')) {
      // MySQL错误
      appError = handleDatabaseError(error);
    } else if (error.name && error.name.includes('JsonWebToken')) {
      // JWT错误
      appError = handleJWTError(error);
    } else if (error.code && error.code.startsWith('LIMIT_')) {
      // Multer文件上传错误
      appError = handleMulterError(error);
    } else if (error.type === 'entity.parse.failed') {
      // JSON解析错误
      appError = new ValidationError('请求数据格式错误');
    } else if (error.type === 'entity.too.large') {
      // 请求体过大
      appError = new ValidationError('请求数据过大');
    } else {
      // 其他未知错误
      appError = new InternalServerError('服务器内部错误');
    }
  }
  
  // 记录错误日志
  logError(appError, req);
  
  // 构建错误响应
  const errorResponse = {
    status: 'error',
    message: appError.message,
    code: appError.code || 'UNKNOWN_ERROR',
    timestamp: new Date().toISOString()
  };
  
  // 在开发环境中添加详细信息
  if (process.env.NODE_ENV === 'development') {
    errorResponse.stack = appError.stack;
    errorResponse.details = appError.details;
    
    // 添加原始错误信息（如果存在）
    if (error !== appError) {
      errorResponse.originalError = {
        name: error.name,
        message: error.message,
        stack: error.stack
      };
    }
  }
  
  // 添加详细信息（如果存在）
  if (appError.details) {
    errorResponse.details = appError.details;
  }
  
  // 发送错误响应
  res.status(appError.statusCode || 500).json(errorResponse);
};

/**
 * 异步错误包装器
 * 用于包装异步路由处理函数，自动捕获Promise rejection
 */
const asyncHandler = (fn) => {
  return (req, res, next) => {
    Promise.resolve(fn(req, res, next)).catch(next);
  };
};

/**
 * 创建错误响应
 * @param {string} message - 错误消息
 * @param {number} statusCode - HTTP状态码
 * @param {string} code - 错误代码
 * @param {*} details - 错误详情
 * @returns {Object} 错误响应对象
 */
const createErrorResponse = (message, statusCode = 500, code = null, details = null) => {
  return {
    status: 'error',
    message,
    code: code || 'UNKNOWN_ERROR',
    timestamp: new Date().toISOString(),
    ...(details && { details })
  };
};

/**
 * 创建成功响应
 * @param {*} data - 响应数据
 * @param {string} message - 成功消息
 * @param {Object} meta - 元数据（如分页信息）
 * @returns {Object} 成功响应对象
 */
const createSuccessResponse = (data = null, message = '操作成功', meta = null) => {
  const response = {
    status: 'success',
    message,
    timestamp: new Date().toISOString()
  };
  
  if (data !== null) {
    response.data = data;
  }
  
  if (meta) {
    response.meta = meta;
  }
  
  return response;
};

module.exports = {
  // 错误类
  AppError,
  ValidationError,
  AuthenticationError,
  AuthorizationError,
  NotFoundError,
  ConflictError,
  RateLimitError,
  InternalServerError,
  
  // 中间件
  notFoundHandler,
  errorHandler,
  asyncHandler,
  
  // 工具函数
  logError,
  createErrorResponse,
  createSuccessResponse
};