<template>
  <div class="midjourney-container">
    <div class="midjourney-header">
      <h1>🎨 Midjourney AI图片生成</h1>
      <p>使用Midjourney创建高质量的AI艺术作品</p>
    </div>

    <!-- 状态检查 -->
    <div v-if="!midjourneyStatus.configured" class="status-warning">
      <div class="warning-card">
        <span class="warning-icon">⚠️</span>
        <div class="warning-content">
          <h3>Midjourney未配置</h3>
          <p>请先在设置页面配置Discord Token和频道信息</p>
          <router-link to="/settings" class="btn-primary">前往设置</router-link>
        </div>
      </div>
    </div>

    <!-- 生成界面 -->
    <div v-else class="midjourney-workspace">
      <!-- 提示词输入 -->
      <div class="prompt-section">
        <h2>📝 图片描述</h2>
        <div class="prompt-input-container">
          <textarea
            v-model="prompt"
            placeholder="描述你想要生成的图片，例如：a beautiful sunset over mountains, digital art style"
            rows="4"
            class="prompt-input"
          ></textarea>
          <div class="prompt-actions">
            <button 
              @click="generateImage" 
              :disabled="generating || !prompt.trim()"
              class="btn-generate"
            >
              {{ generating ? '生成中...' : '🎨 生成图片' }}
            </button>
            <button @click="clearPrompt" class="btn-secondary">清空</button>
          </div>
        </div>
      </div>

      <!-- 参数设置 -->
      <div class="parameters-section">
        <h2>⚙️ 生成参数</h2>
        <div class="parameters-grid">
          <div class="param-item">
            <label>宽高比:</label>
            <select v-model="options.aspect_ratio">
              <option value="">默认</option>
              <option value="1:1">1:1 (正方形)</option>
              <option value="16:9">16:9 (宽屏)</option>
              <option value="9:16">9:16 (竖屏)</option>
              <option value="4:3">4:3 (标准)</option>
              <option value="3:4">3:4 (竖版标准)</option>
            </select>
          </div>
          
          <div class="param-item">
            <label>质量等级:</label>
            <select v-model="options.quality">
              <option value="">默认</option>
              <option value="0.25">0.25 (快速)</option>
              <option value="0.5">0.5 (平衡)</option>
              <option value="1">1 (标准)</option>
              <option value="2">2 (高质量)</option>
            </select>
          </div>
          
          <div class="param-item">
            <label>风格化程度:</label>
            <input 
              type="range" 
              v-model="options.stylize" 
              min="0" 
              max="1000" 
              step="50"
            />
            <span class="param-value">{{ options.stylize || 100 }}</span>
          </div>
          
          <div class="param-item">
            <label>随机性:</label>
            <input 
              type="range" 
              v-model="options.chaos" 
              min="0" 
              max="100" 
              step="10"
            />
            <span class="param-value">{{ options.chaos || 0 }}</span>
          </div>
        </div>
      </div>

      <!-- 生成状态 -->
      <div v-if="generating" class="generation-status">
        <div class="status-card">
          <div class="loading-spinner"></div>
          <div class="status-content">
            <h3>🎨 正在生成图片...</h3>
            <p>{{ generationMessage }}</p>
            <div class="progress-bar">
              <div class="progress-fill" :style="{ width: generationProgress + '%' }"></div>
            </div>
          </div>
        </div>
      </div>

      <!-- 生成历史 -->
      <div class="history-section">
        <div class="section-header">
          <h2>📊 生成历史</h2>
          <button @click="loadHistory" class="btn-secondary">刷新</button>
        </div>
        
        <div v-if="history.length === 0" class="empty-state">
          <p>暂无生成记录</p>
        </div>
        
        <div v-else class="history-grid">
          <div 
            v-for="item in history" 
            :key="item.id" 
            class="history-item"
          >
            <div class="history-image">
              <img 
                v-if="item.result?.image_url" 
                :src="item.result.image_url" 
                :alt="item.prompt"
                @click="viewImage(item.result.image_url)"
              />
              <div v-else class="image-placeholder">
                <span>{{ item.success === false ? '❌' : '⏳' }}</span>
              </div>
            </div>
            <div class="history-info">
              <p class="history-prompt">{{ item.prompt }}</p>
              <p class="history-time">{{ formatTime(item.timestamp) }}</p>
              <div v-if="item.result?.image_url" class="history-actions">
                <button @click="downloadImage(item.result.image_url, item.prompt)" class="btn-small">下载</button>
                <button @click="useAsPrompt(item.prompt)" class="btn-small">复用</button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 图片查看模态框 -->
    <div v-if="viewingImage" class="image-modal" @click="closeImageView">
      <div class="modal-content" @click.stop>
        <img :src="viewingImage" alt="Generated Image" />
        <button @click="closeImageView" class="modal-close">×</button>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'

const router = useRouter()

// 响应式数据
const midjourneyStatus = ref({
  configured: false,
  available: false
})

const prompt = ref('')
const options = ref({
  aspect_ratio: '',
  quality: '',
  stylize: 100,
  chaos: 0
})

const generating = ref(false)
const generationMessage = ref('')
const generationProgress = ref(0)

const history = ref([])
const viewingImage = ref(null)

// 方法
async function checkMidjourneyStatus() {
  try {
    const response = await fetch('/api/midjourney/status')
    const data = await response.json()
    
    if (data.success) {
      midjourneyStatus.value = data.status
    }
  } catch (error) {
    console.error('检查Midjourney状态失败:', error)
  }
}

async function generateImage() {
  if (!prompt.value.trim()) return
  
  generating.value = true
  generationMessage.value = '正在发送生成请求...'
  generationProgress.value = 10
  
  try {
    const response = await fetch('/api/midjourney/generate', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        prompt: prompt.value,
        options: options.value
      })
    })
    
    const data = await response.json()
    
    if (data.success) {
      generationMessage.value = '生成任务已开始，预计需要2-5分钟...'
      generationProgress.value = 30
      
      // 模拟进度更新
      simulateProgress()
      
      // 定期检查结果
      checkGenerationResult()
    } else {
      throw new Error(data.error)
    }
  } catch (error) {
    console.error('生成图片失败:', error)
    alert('生成图片失败: ' + error.message)
    generating.value = false
  }
}

function simulateProgress() {
  const interval = setInterval(() => {
    if (generationProgress.value < 90) {
      generationProgress.value += Math.random() * 10
      
      if (generationProgress.value > 50) {
        generationMessage.value = '正在生成图片，请耐心等待...'
      }
      if (generationProgress.value > 80) {
        generationMessage.value = '即将完成...'
      }
    } else {
      clearInterval(interval)
    }
  }, 3000)
}

async function checkGenerationResult() {
  // 这里应该实现轮询检查生成结果的逻辑
  // 由于是演示，我们模拟一个延迟后的完成
  setTimeout(async () => {
    generationProgress.value = 100
    generationMessage.value = '生成完成！'
    
    setTimeout(() => {
      generating.value = false
      loadHistory()
    }, 1000)
  }, 10000) // 10秒后模拟完成
}

function clearPrompt() {
  prompt.value = ''
}

async function loadHistory() {
  try {
    const response = await fetch('/api/midjourney/history')
    const data = await response.json()
    
    if (data.success) {
      history.value = data.history
    }
  } catch (error) {
    console.error('加载历史失败:', error)
  }
}

function viewImage(imageUrl) {
  viewingImage.value = imageUrl
}

function closeImageView() {
  viewingImage.value = null
}

async function downloadImage(imageUrl, promptText) {
  try {
    const response = await fetch('/api/midjourney/download', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        imageUrl,
        filename: `midjourney_${Date.now()}.png`
      })
    })
    
    const data = await response.json()
    
    if (data.success) {
      alert('图片下载成功！')
    } else {
      alert('图片下载失败: ' + data.error)
    }
  } catch (error) {
    console.error('下载图片失败:', error)
    alert('下载图片失败: ' + error.message)
  }
}

function useAsPrompt(promptText) {
  prompt.value = promptText
}

function formatTime(timestamp) {
  return new Date(timestamp).toLocaleString()
}

// 生命周期
onMounted(async () => {
  await checkMidjourneyStatus()
  await loadHistory()
})
</script>

<style scoped>
.midjourney-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
}

.midjourney-header {
  text-align: center;
  margin-bottom: 30px;
}

.midjourney-header h1 {
  font-size: 2.5rem;
  margin-bottom: 10px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.midjourney-header p {
  color: var(--color-text-secondary);
  font-size: 1.1rem;
}

.status-warning {
  margin-bottom: 30px;
}

.warning-card {
  display: flex;
  align-items: center;
  padding: 20px;
  background: #fff3cd;
  border: 1px solid #ffeaa7;
  border-radius: 8px;
  color: #856404;
}

.warning-icon {
  font-size: 2rem;
  margin-right: 15px;
}

.warning-content h3 {
  margin: 0 0 5px 0;
  font-size: 1.2rem;
}

.warning-content p {
  margin: 0 0 15px 0;
}

.midjourney-workspace {
  display: grid;
  gap: 30px;
}

.prompt-section,
.parameters-section,
.history-section {
  background: var(--color-background-soft);
  padding: 25px;
  border-radius: 12px;
  border: 1px solid var(--color-border);
}

.prompt-section h2,
.parameters-section h2,
.history-section h2 {
  margin: 0 0 20px 0;
  font-size: 1.3rem;
  color: var(--color-text);
}

.prompt-input-container {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.prompt-input {
  width: 100%;
  padding: 15px;
  border: 2px solid var(--color-border);
  border-radius: 8px;
  font-size: 16px;
  font-family: inherit;
  resize: vertical;
  min-height: 100px;
  background: var(--color-background);
  color: var(--color-text);
}

.prompt-input:focus {
  outline: none;
  border-color: var(--color-primary);
  box-shadow: 0 0 0 3px rgba(0, 119, 204, 0.1);
}

.prompt-actions {
  display: flex;
  gap: 10px;
}

.btn-generate {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
  padding: 12px 24px;
  border-radius: 8px;
  font-size: 16px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
}

.btn-generate:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);
}

.btn-generate:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}

.parameters-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 20px;
}

.param-item {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.param-item label {
  font-weight: 500;
  color: var(--color-text);
}

.param-item select,
.param-item input[type="range"] {
  padding: 8px;
  border: 1px solid var(--color-border);
  border-radius: 6px;
  background: var(--color-background);
  color: var(--color-text);
}

.param-value {
  font-size: 14px;
  color: var(--color-text-secondary);
  text-align: center;
}

.generation-status {
  margin: 20px 0;
}

.status-card {
  display: flex;
  align-items: center;
  padding: 20px;
  background: var(--color-background-soft);
  border: 1px solid var(--color-border);
  border-radius: 8px;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 4px solid var(--color-border);
  border-top: 4px solid var(--color-primary);
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-right: 20px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.status-content {
  flex: 1;
}

.status-content h3 {
  margin: 0 0 10px 0;
  color: var(--color-text);
}

.status-content p {
  margin: 0 0 15px 0;
  color: var(--color-text-secondary);
}

.progress-bar {
  width: 100%;
  height: 8px;
  background: var(--color-border);
  border-radius: 4px;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, var(--color-primary), #667eea);
  transition: width 0.3s ease;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.history-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 20px;
}

.history-item {
  background: var(--color-background);
  border: 1px solid var(--color-border);
  border-radius: 8px;
  overflow: hidden;
  transition: transform 0.2s ease;
}

.history-item:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.history-image {
  aspect-ratio: 1;
  overflow: hidden;
  cursor: pointer;
}

.history-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s ease;
}

.history-image img:hover {
  transform: scale(1.05);
}

.image-placeholder {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  background: var(--color-background-soft);
  font-size: 2rem;
}

.history-info {
  padding: 15px;
}

.history-prompt {
  margin: 0 0 8px 0;
  font-size: 14px;
  color: var(--color-text);
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.history-time {
  margin: 0 0 10px 0;
  font-size: 12px;
  color: var(--color-text-secondary);
}

.history-actions {
  display: flex;
  gap: 8px;
}

.btn-small {
  padding: 4px 8px;
  font-size: 12px;
  border: 1px solid var(--color-border);
  background: var(--color-background);
  color: var(--color-text);
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.btn-small:hover {
  background: var(--color-primary);
  color: white;
  border-color: var(--color-primary);
}

.empty-state {
  text-align: center;
  padding: 40px;
  color: var(--color-text-secondary);
}

.image-modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.modal-content {
  position: relative;
  max-width: 90%;
  max-height: 90%;
}

.modal-content img {
  max-width: 100%;
  max-height: 100%;
  border-radius: 8px;
}

.modal-close {
  position: absolute;
  top: -10px;
  right: -10px;
  width: 30px;
  height: 30px;
  background: white;
  border: none;
  border-radius: 50%;
  font-size: 18px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
}

.btn-primary,
.btn-secondary {
  padding: 10px 20px;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  text-decoration: none;
  display: inline-block;
  text-align: center;
}

.btn-primary {
  background: var(--color-primary);
  color: white;
  border: none;
}

.btn-primary:hover {
  background: #005a9e;
}

.btn-secondary {
  background: var(--color-background);
  color: var(--color-text);
  border: 1px solid var(--color-border);
}

.btn-secondary:hover {
  background: var(--color-background-soft);
}

@media (max-width: 768px) {
  .midjourney-container {
    padding: 15px;
  }

  .parameters-grid {
    grid-template-columns: 1fr;
  }

  .history-grid {
    grid-template-columns: 1fr;
  }

  .prompt-actions {
    flex-direction: column;
  }
}
</style>
