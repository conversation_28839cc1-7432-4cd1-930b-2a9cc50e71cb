/**
 * JWT认证中间件
 * 
 * 功能说明：
 * - 验证JWT令牌
 * - 提取用户信息
 * - 权限检查
 * - 令牌刷新
 */

const jwt = require('jsonwebtoken');
const db = require('../config/database');
require('dotenv').config();

/**
 * 验证JWT令牌中间件
 * @param {Object} req - Express请求对象
 * @param {Object} res - Express响应对象
 * @param {Function} next - Express下一个中间件函数
 */
const authenticateToken = async (req, res, next) => {
  try {
    // 从请求头获取Authorization字段
    const authHeader = req.headers['authorization'];
    const token = authHeader && authHeader.split(' ')[1]; // Bearer TOKEN格式
    
    // 检查令牌是否存在
    if (!token) {
      return res.status(401).json({
        status: 'error',
        message: '访问令牌缺失',
        code: 'TOKEN_MISSING'
      });
    }
    
    // 验证JWT令牌
    const decoded = jwt.verify(token, process.env.JWT_SECRET);
    // 从数据库获取用户信息 (确保用户仍然存在且有效)
    const users = await db.execute(
      'SELECT id, username, display_name, role, created_at FROM users WHERE id = ? AND role IS NOT NULL',
      [decoded.id]
    );
    
    if (users.length === 0) {
      return res.status(401).json({
        status: 'error',
        message: '用户不存在或已被禁用',
        code: 'USER_NOT_FOUND'
      });
    }
    
    // 将用户信息添加到请求对象中
    req.user = {
      id: users[0].id,
      username: users[0].username,
      displayName: users[0].display_name,
      role: users[0].role,
      createdAt: users[0].created_at
    };
    
    // 记录用户活动日志
    await logUserActivity(req.user.id, 'api_access', 'system', null, {
      endpoint: req.originalUrl,
      method: req.method,
      userAgent: req.get('User-Agent'),
      ip: req.ip
    });
    
    next();
  } catch (error) {
    console.error('❌ JWT验证错误:', error.message);
    
    // 处理不同类型的JWT错误
    if (error.name === 'TokenExpiredError') {
      return res.status(401).json({
        status: 'error',
        message: '访问令牌已过期',
        code: 'TOKEN_EXPIRED'
      });
    } else if (error.name === 'JsonWebTokenError') {
      return res.status(401).json({
        status: 'error',
        message: '无效的访问令牌',
        code: 'TOKEN_INVALID'
      });
    } else {
      return res.status(500).json({
        status: 'error',
        message: '令牌验证失败',
        code: 'TOKEN_VERIFICATION_FAILED'
      });
    }
  }
};

/**
 * 管理员权限检查中间件
 * @param {Object} req - Express请求对象
 * @param {Object} res - Express响应对象
 * @param {Function} next - Express下一个中间件函数
 */
const requireAdmin = (req, res, next) => {
  // 检查用户是否已通过身份验证
  if (!req.user) {
    return res.status(401).json({
      status: 'error',
      message: '需要身份验证',
      code: 'AUTHENTICATION_REQUIRED'
    });
  }
  
  // 检查用户是否为管理员
  if (req.user.role !== 'admin') {
    return res.status(403).json({
      status: 'error',
      message: '需要管理员权限',
      code: 'ADMIN_REQUIRED'
    });
  }
  
  next();
};

/**
 * 可选的身份验证中间件 (不强制要求登录)
 * @param {Object} req - Express请求对象
 * @param {Object} res - Express响应对象
 * @param {Function} next - Express下一个中间件函数
 */
const optionalAuth = async (req, res, next) => {
  try {
    const authHeader = req.headers['authorization'];
    const token = authHeader && authHeader.split(' ')[1];
    
    if (token) {
      const decoded = jwt.verify(token, process.env.JWT_SECRET);
      
      const users = await db.execute(
        'SELECT id, username, display_name, role FROM users WHERE id = ?',
        [decoded.userId]
      );
      
      if (users.length > 0) {
        req.user = {
          id: users[0].id,
          username: users[0].username,
          displayName: users[0].display_name,
          role: users[0].role
        };
      }
    }
    
    next();
  } catch (error) {
    // 可选认证失败时不返回错误，继续执行
    next();
  }
};

/**
 * 生成JWT令牌
 * @param {Object} payload - 令牌载荷
 * @param {string} expiresIn - 过期时间
 * @returns {string} JWT令牌
 */
const generateToken = (payload, expiresIn = process.env.JWT_EXPIRES_IN || '7d') => {
  return jwt.sign(payload, process.env.JWT_SECRET, { expiresIn });
};

/**
 * 生成刷新令牌
 * @param {Object} payload - 令牌载荷
 * @returns {string} 刷新令牌
 */
const generateRefreshToken = (payload) => {
  return jwt.sign(payload, process.env.JWT_SECRET, {
    expiresIn: process.env.JWT_REFRESH_EXPIRES_IN || '30d'
  });
};

/**
 * 验证刷新令牌
 * @param {string} refreshToken - 刷新令牌
 * @returns {Object} 解码后的令牌信息
 */
const verifyRefreshToken = (refreshToken) => {
  return jwt.verify(refreshToken, process.env.JWT_SECRET);
};

/**
 * 记录用户活动日志
 * @param {number} userId - 用户ID
 * @param {string} actionType - 操作类型
 * @param {string} resourceType - 资源类型
 * @param {number} resourceId - 资源ID
 * @param {Object} details - 详细信息
 */
async function logUserActivity(userId, actionType, resourceType, resourceId = null, details = {}) {
  try {
    await db.execute(
      `INSERT INTO user_activity_logs 
       (user_id, action_type, resource_type, resource_id, details, ip_address, user_agent) 
       VALUES (?, ?, ?, ?, ?, ?, ?)`,
      [
        userId,
        actionType,
        resourceType,
        resourceId,
        JSON.stringify(details),
        details.ip || null,
        details.userAgent || null
      ]
    );
  } catch (error) {
    console.error('❌ 记录用户活动日志失败:', error.message);
    // 不抛出错误，避免影响主要业务流程
  }
}

/**
 * 检查用户是否有权限访问特定资源
 * @param {number} userId - 用户ID
 * @param {string} resourceType - 资源类型
 * @param {number} resourceId - 资源ID
 * @param {string} action - 操作类型 (read, write, delete)
 * @returns {boolean} 是否有权限
 */
async function checkResourcePermission(userId, resourceType, resourceId, action = 'read') {
  try {
    // 管理员拥有所有权限
    const user = await db.execute(
      'SELECT role FROM users WHERE id = ?',
      [userId]
    );
    
    if (user.length > 0 && user[0].role === 'admin') {
      return true;
    }
    
    // 根据资源类型检查权限
    switch (resourceType) {
      case 'knowledge':
        const knowledge = await db.execute(
          'SELECT user_id FROM knowledge_items WHERE id = ?',
          [resourceId]
        );
        return knowledge.length > 0 && knowledge[0].user_id === userId;
        
      case 'image':
        const image = await db.execute(
          'SELECT user_id FROM image_items WHERE id = ?',
          [resourceId]
        );
        return image.length > 0 && image[0].user_id === userId;
        
      case 'chat':
        const chat = await db.execute(
          'SELECT user_id FROM chat_sessions WHERE id = ?',
          [resourceId]
        );
        return chat.length > 0 && chat[0].user_id === userId;
        
      default:
        return false;
    }
  } catch (error) {
    console.error('❌ 检查资源权限失败:', error.message);
    return false;
  }
}

/**
 * 资源权限检查中间件
 * @param {string} resourceType - 资源类型
 * @param {string} action - 操作类型
 */
const requireResourcePermission = (resourceType, action = 'read') => {
  return async (req, res, next) => {
    try {
      const resourceId = req.params.id || req.body.id;
      
      if (!resourceId) {
        return res.status(400).json({
          status: 'error',
          message: '资源ID缺失',
          code: 'RESOURCE_ID_MISSING'
        });
      }
      
      const hasPermission = await checkResourcePermission(
        req.user.id,
        resourceType,
        resourceId,
        action
      );
      
      if (!hasPermission) {
        return res.status(403).json({
          status: 'error',
          message: '没有权限访问此资源',
          code: 'RESOURCE_ACCESS_DENIED'
        });
      }
      
      next();
    } catch (error) {
      console.error('❌ 资源权限检查错误:', error.message);
      return res.status(500).json({
        status: 'error',
        message: '权限检查失败',
        code: 'PERMISSION_CHECK_FAILED'
      });
    }
  };
};

module.exports = {
  authenticateToken,
  requireAdmin,
  optionalAuth,
  generateToken,
  generateRefreshToken,
  verifyRefreshToken,
  logUserActivity,
  checkResourcePermission,
  requireResourcePermission
};