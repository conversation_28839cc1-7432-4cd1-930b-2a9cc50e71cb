#!/usr/bin/env python3
"""
测试Discord API连接
"""

import asyncio
import aiohttp
import sys

async def test_discord_connection():
    """测试Discord API连接"""
    
    if len(sys.argv) < 4:
        print("用法: python test_discord_api.py <token> <channel_id> <server_id>")
        return
    
    token = sys.argv[1]
    channel_id = sys.argv[2]
    server_id = sys.argv[3]
    
    headers = {
        "Authorization": f"Bot {token}",
        "Content-Type": "application/json",
        "User-Agent": "DiscordBot (https://github.com/discord/discord-api-docs, 1.0)"
    }
    
    print(f"🔧 测试Discord API连接")
    print(f"📍 频道ID: {channel_id}")
    print(f"🏠 服务器ID: {server_id}")
    
    async with aiohttp.ClientSession() as session:
        try:
            # 测试1: 获取频道信息
            print("\n📡 测试1: 获取频道信息")
            url = f"https://discord.com/api/v10/channels/{channel_id}"
            
            async with session.get(url, headers=headers) as response:
                print(f"状态码: {response.status}")
                response_text = await response.text()
                print(f"响应: {response_text[:200]}...")
                
                if response.status == 200:
                    print("✅ 频道访问成功")
                else:
                    print("❌ 频道访问失败")
                    return
            
            # 测试2: 获取最近消息
            print("\n📡 测试2: 获取最近消息")
            url = f"https://discord.com/api/v10/channels/{channel_id}/messages?limit=5"
            
            async with session.get(url, headers=headers) as response:
                print(f"状态码: {response.status}")
                response_text = await response.text()
                print(f"响应: {response_text[:200]}...")
                
                if response.status == 200:
                    print("✅ 消息读取成功")
                else:
                    print("❌ 消息读取失败")
                    return
            
            # 测试3: 发送测试消息
            print("\n📡 测试3: 发送测试消息")
            url = f"https://discord.com/api/v10/channels/{channel_id}/messages"
            data = {
                "content": "🤖 Discord API测试消息",
                "tts": False
            }
            
            async with session.post(url, headers=headers, json=data) as response:
                print(f"状态码: {response.status}")
                response_text = await response.text()
                print(f"响应: {response_text[:200]}...")
                
                if response.status == 200:
                    print("✅ 消息发送成功")
                    result = await response.json()
                    print(f"消息ID: {result.get('id')}")
                else:
                    print("❌ 消息发送失败")
                    if response.status == 403:
                        print("🚫 权限不足 - 请检查Bot权限")
                    elif response.status == 404:
                        print("🔍 频道不存在或无法访问")
                    return
            
            print("\n🎉 所有测试通过！Discord API连接正常")
            
        except Exception as e:
            print(f"❌ 连接异常: {e}")

if __name__ == "__main__":
    asyncio.run(test_discord_connection())
