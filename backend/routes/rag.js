const express = require('express');
const router = express.Router();
const ragService = require('../services/ragService');

// 文件上传中间件
const upload = ragService.getUploadMiddleware();

// 检查RAG服务状态
router.get('/status', async (req, res) => {
  try {
    const status = await ragService.checkRAGStatus();
    res.json({
      success: true,
      status
    });
  } catch (error) {
    console.error('❌ 检查RAG状态失败:', error);
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

// 上传并处理文档
router.post('/upload', upload.single('file'), async (req, res) => {
  try {
    if (!req.file) {
      return res.status(400).json({
        success: false,
        error: '没有上传文件'
      });
    }

    const { apiKey, baseUrl } = req.body;
    
    if (!apiKey) {
      return res.status(400).json({
        success: false,
        error: '需要提供API密钥'
      });
    }

    console.log('📁 文件上传成功:', req.file.filename);
    
    // 异步处理文档，不阻塞响应
    ragService.processDocument(req.file.path, apiKey, baseUrl)
      .then(result => {
        console.log('✅ 文档处理完成:', req.file.filename);
      })
      .catch(error => {
        console.error('❌ 文档处理失败:', error);
      });

    res.json({
      success: true,
      message: '文件上传成功，正在后台处理...',
      file: {
        filename: req.file.filename,
        originalName: req.file.originalname,
        size: req.file.size,
        mimetype: req.file.mimetype
      }
    });

  } catch (error) {
    console.error('❌ 文件上传失败:', error);
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

// 查询RAG知识库
router.post('/query', async (req, res) => {
  try {
    const { query, mode = 'hybrid', apiKey, baseUrl } = req.body;
    
    if (!query) {
      return res.status(400).json({
        success: false,
        error: '查询内容不能为空'
      });
    }
    
    if (!apiKey) {
      return res.status(400).json({
        success: false,
        error: '需要提供API密钥'
      });
    }

    console.log('🔍 RAG查询:', query);
    
    const result = await ragService.queryRAG(query, mode, apiKey, baseUrl);
    
    res.json(result);

  } catch (error) {
    console.error('❌ RAG查询失败:', error);
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

// 获取文档列表
router.get('/documents', async (req, res) => {
  try {
    const documents = await ragService.getDocumentList();
    res.json({
      success: true,
      documents
    });
  } catch (error) {
    console.error('❌ 获取文档列表失败:', error);
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

// 删除文档
router.delete('/documents/:filename', async (req, res) => {
  try {
    const { filename } = req.params;
    const success = await ragService.deleteDocument(filename);
    
    if (success) {
      res.json({
        success: true,
        message: '文档删除成功'
      });
    } else {
      res.status(404).json({
        success: false,
        error: '文档不存在或删除失败'
      });
    }
  } catch (error) {
    console.error('❌ 删除文档失败:', error);
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

// 批量上传文档
router.post('/upload-batch', upload.array('files', 10), async (req, res) => {
  try {
    if (!req.files || req.files.length === 0) {
      return res.status(400).json({
        success: false,
        error: '没有上传文件'
      });
    }

    const { apiKey, baseUrl } = req.body;
    
    if (!apiKey) {
      return res.status(400).json({
        success: false,
        error: '需要提供API密钥'
      });
    }

    console.log('📁 批量文件上传成功:', req.files.length, '个文件');
    
    // 异步处理所有文档
    const processPromises = req.files.map(file => 
      ragService.processDocument(file.path, apiKey, baseUrl)
        .then(result => ({ file: file.filename, success: true, result }))
        .catch(error => ({ file: file.filename, success: false, error: error.message }))
    );

    // 不等待处理完成，立即返回
    Promise.all(processPromises)
      .then(results => {
        console.log('✅ 批量文档处理完成:', results);
      })
      .catch(error => {
        console.error('❌ 批量文档处理失败:', error);
      });

    res.json({
      success: true,
      message: `${req.files.length}个文件上传成功，正在后台处理...`,
      files: req.files.map(file => ({
        filename: file.filename,
        originalName: file.originalname,
        size: file.size,
        mimetype: file.mimetype
      }))
    });

  } catch (error) {
    console.error('❌ 批量文件上传失败:', error);
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

// 获取RAG配置信息
router.get('/config', async (req, res) => {
  try {
    res.json({
      success: true,
      config: {
        maxFileSize: '50MB',
        supportedTypes: [
          'PDF文档 (.pdf)',
          'Word文档 (.doc, .docx)',
          '文本文件 (.txt)',
          '图片文件 (.jpg, .png, .gif)',
          'Excel文件 (.xls, .xlsx)'
        ],
        features: [
          '多模态文档解析',
          '图片内容理解',
          '表格数据提取',
          '混合检索模式',
          '本地知识库存储'
        ]
      }
    });
  } catch (error) {
    console.error('❌ 获取RAG配置失败:', error);
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

module.exports = router;
