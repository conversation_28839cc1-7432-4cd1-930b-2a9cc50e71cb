/* 全局样式变量 */
:root {
  --primary-color: #4a6baf;
  --secondary-color: #2c3e50;
  --accent-color: #42b983;
  --text-color: #333;
  --light-text: #fff;
  --border-color: #e9ecef;
  --bg-light: #f8f9fa;
  --bg-dark: #2c3e50;
  --shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  
  --spacing-xs: 0.25rem;
  --spacing-sm: 0.5rem;
  --spacing-md: 1rem;
  --spacing-lg: 1.5rem;
  --spacing-xl: 2rem;
  
  --font-size-sm: 0.875rem;
  --font-size-md: 1rem;
  --font-size-lg: 1.25rem;
  --font-size-xl: 1.5rem;
  --font-size-xxl: 2rem;
  
  --border-radius: 4px;
  --transition-speed: 0.3s;
}

/* 按钮样式 */
.btn {
  display: inline-block;
  padding: 0.5rem 1rem;
  border: none;
  border-radius: var(--border-radius);
  font-size: var(--font-size-md);
  cursor: pointer;
  transition: background-color var(--transition-speed), transform var(--transition-speed);
}

.btn:hover {
  transform: translateY(-2px);
}

.btn-primary {
  background-color: var(--primary-color);
  color: var(--light-text);
}

.btn-primary:hover {
  background-color: #3a5a9f;
}

.btn-secondary {
  background-color: var(--secondary-color);
  color: var(--light-text);
}

.btn-secondary:hover {
  background-color: #243342;
}

.btn-accent {
  background-color: var(--accent-color);
  color: var(--light-text);
}

.btn-accent:hover {
  background-color: #35a573;
}

/* 卡片样式 */
.card {
  background-color: white;
  border-radius: var(--border-radius);
  box-shadow: var(--shadow);
  padding: var(--spacing-lg);
  margin-bottom: var(--spacing-lg);
}

.card-title {
  font-size: var(--font-size-lg);
  margin-bottom: var(--spacing-md);
  color: var(--secondary-color);
}

/* 表单样式 */
.form-group {
  margin-bottom: var(--spacing-md);
}

.form-label {
  display: block;
  margin-bottom: var(--spacing-xs);
  font-weight: bold;
}

.form-input {
  width: 100%;
  padding: 0.5rem;
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius);
  font-size: var(--font-size-md);
}

.form-input:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 2px rgba(74, 107, 175, 0.2);
}

/* 工具类 */
.text-center {
  text-align: center;
}

.mt-1 { margin-top: var(--spacing-xs); }
.mt-2 { margin-top: var(--spacing-sm); }
.mt-3 { margin-top: var(--spacing-md); }
.mt-4 { margin-top: var(--spacing-lg); }
.mt-5 { margin-top: var(--spacing-xl); }

.mb-1 { margin-bottom: var(--spacing-xs); }
.mb-2 { margin-bottom: var(--spacing-sm); }
.mb-3 { margin-bottom: var(--spacing-md); }
.mb-4 { margin-bottom: var(--spacing-lg); }
.mb-5 { margin-bottom: var(--spacing-xl); }

.ml-1 { margin-left: var(--spacing-xs); }
.ml-2 { margin-left: var(--spacing-sm); }
.ml-3 { margin-left: var(--spacing-md); }
.ml-4 { margin-left: var(--spacing-lg); }
.ml-5 { margin-left: var(--spacing-xl); }

.mr-1 { margin-right: var(--spacing-xs); }
.mr-2 { margin-right: var(--spacing-sm); }
.mr-3 { margin-right: var(--spacing-md); }
.mr-4 { margin-right: var(--spacing-lg); }
.mr-5 { margin-right: var(--spacing-xl); }

.p-1 { padding: var(--spacing-xs); }
.p-2 { padding: var(--spacing-sm); }
.p-3 { padding: var(--spacing-md); }
.p-4 { padding: var(--spacing-lg); }
.p-5 { padding: var(--spacing-xl); }

/* 响应式布局 */
@media (max-width: 768px) {
  .sidebar-left {
    width: 60px;
  }
  
  .menu-text {
    display: none;
  }
  
  .menu-icon {
    margin-right: 0;
  }
  
  .sidebar-right {
    display: none;
  }
  
  .main-content {
    padding: var(--spacing-md);
  }
}