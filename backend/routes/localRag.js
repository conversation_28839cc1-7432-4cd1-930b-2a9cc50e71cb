const express = require('express');
const router = express.Router();
const localRagService = require('../services/localRagService');
const ragService = require('../services/ragService');

// 检查本地RAG服务状态
router.get('/status', async (req, res) => {
  try {
    const status = await localRagService.getLocalRAGStatus();
    res.json({
      success: true,
      status,
      mode: 'local'
    });
  } catch (error) {
    console.error('❌ 检查本地RAG状态失败:', error);
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

// 初始化本地RAG环境
router.post('/init', async (req, res) => {
  try {
    console.log('🔄 初始化本地RAG环境...');
    
    // 创建本地配置文件
    const configCreated = await localRagService.createLocalConfig();
    
    if (!configCreated) {
      return res.status(500).json({
        success: false,
        error: '创建本地配置文件失败'
      });
    }
    
    // 检查Ollama状态
    const ollamaStatus = await localRagService.checkOllamaStatus();
    
    let modelInstalled = false;
    if (ollamaStatus.available) {
      // 检查是否已有推荐模型
      const hasQwen = ollamaStatus.models.some(model => model.name.includes('qwen2'));
      
      if (!hasQwen) {
        console.log('🔄 安装推荐的Ollama模型...');
        try {
          await localRagService.installOllamaModel('qwen2:7b');
          modelInstalled = true;
        } catch (error) {
          console.warn('⚠️ 模型安装失败，将使用其他可用模型:', error.message);
        }
      } else {
        modelInstalled = true;
      }
    }
    
    res.json({
      success: true,
      message: '本地RAG环境初始化完成',
      details: {
        configCreated,
        ollamaAvailable: ollamaStatus.available,
        modelInstalled,
        availableModels: ollamaStatus.models || []
      }
    });
    
  } catch (error) {
    console.error('❌ 初始化本地RAG环境失败:', error);
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

// 使用本地模型上传并处理文档
router.post('/upload', ragService.getUploadMiddleware().single('file'), async (req, res) => {
  try {
    if (!req.file) {
      return res.status(400).json({
        success: false,
        error: '没有上传文件'
      });
    }

    console.log('📁 文件上传成功（本地模式）:', req.file.filename);
    
    // 使用本地模型异步处理文档
    localRagService.processDocumentLocal(req.file.path)
      .then(result => {
        console.log('✅ 本地文档处理完成:', req.file.filename);
      })
      .catch(error => {
        console.error('❌ 本地文档处理失败:', error);
      });

    res.json({
      success: true,
      message: '文件上传成功，正在使用本地模型处理...',
      mode: 'local',
      file: {
        filename: req.file.filename,
        originalName: req.file.originalname,
        size: req.file.size,
        mimetype: req.file.mimetype
      }
    });

  } catch (error) {
    console.error('❌ 本地文件上传失败:', error);
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

// 使用本地模型批量上传文档
router.post('/upload-batch', ragService.getUploadMiddleware().array('files', 10), async (req, res) => {
  try {
    if (!req.files || req.files.length === 0) {
      return res.status(400).json({
        success: false,
        error: '没有上传文件'
      });
    }

    console.log('📁 批量文件上传成功（本地模式）:', req.files.length, '个文件');
    
    // 使用本地模型异步处理所有文档
    const processPromises = req.files.map(file => 
      localRagService.processDocumentLocal(file.path)
        .then(result => ({ file: file.filename, success: true, result }))
        .catch(error => ({ file: file.filename, success: false, error: error.message }))
    );

    // 不等待处理完成，立即返回
    Promise.all(processPromises)
      .then(results => {
        console.log('✅ 批量本地文档处理完成:', results);
      })
      .catch(error => {
        console.error('❌ 批量本地文档处理失败:', error);
      });

    res.json({
      success: true,
      message: `${req.files.length}个文件上传成功，正在使用本地模型处理...`,
      mode: 'local',
      files: req.files.map(file => ({
        filename: file.filename,
        originalName: file.originalname,
        size: file.size,
        mimetype: file.mimetype
      }))
    });

  } catch (error) {
    console.error('❌ 批量本地文件上传失败:', error);
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

// 添加文本到本地RAG知识库
router.post('/add-text', async (req, res) => {
  try {
    const { content, metadata = {} } = req.body;

    if (!content) {
      return res.status(400).json({
        success: false,
        error: '文本内容不能为空'
      });
    }

    console.log('📄 添加文本到本地RAG知识库:', content.substring(0, 100) + '...');

    const result = await localRagService.addDocumentLocal(content, metadata);

    res.json({
      ...result,
      mode: 'local'
    });

  } catch (error) {
    console.error('❌ 添加文本到本地RAG失败:', error);
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

// 使用本地模型查询RAG知识库
router.post('/query', async (req, res) => {
  try {
    const { query, mode = 'hybrid' } = req.body;

    if (!query) {
      return res.status(400).json({
        success: false,
        error: '查询内容不能为空'
      });
    }

    console.log('🔍 本地RAG查询:', query);

    const result = await localRagService.queryRAGLocal(query);

    res.json({
      ...result,
      mode: 'local'
    });

  } catch (error) {
    console.error('❌ 本地RAG查询失败:', error);
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

// 获取本地模型配置
router.get('/config', async (req, res) => {
  try {
    const ollamaStatus = await localRagService.checkOllamaStatus();
    const dependenciesStatus = await localRagService.checkPythonDependencies();
    
    res.json({
      success: true,
      config: {
        mode: 'local',
        embeddingModel: 'BAAI/bge-small-zh-v1.5',
        llmModel: 'qwen2:7b',
        ollamaUrl: 'http://localhost:11434',
        maxFileSize: '50MB',
        supportedTypes: [
          'PDF文档 (.pdf)',
          'Word文档 (.doc, .docx)',
          '文本文件 (.txt)',
          '图片文件 (.jpg, .png, .gif)',
          'Excel文件 (.xls, .xlsx)'
        ],
        features: [
          '🏠 完全本地化处理',
          '🔒 数据隐私保护',
          '🚀 无需API密钥',
          '🧠 本地大语言模型',
          '📊 本地向量化模型',
          '🔍 混合检索模式'
        ],
        status: {
          ollama: ollamaStatus,
          dependencies: dependenciesStatus
        }
      }
    });
  } catch (error) {
    console.error('❌ 获取本地RAG配置失败:', error);
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

// 安装Ollama模型
router.post('/install-model', async (req, res) => {
  try {
    const { modelName = 'qwen2:7b' } = req.body;
    
    console.log('🔄 开始安装Ollama模型:', modelName);
    
    const result = await localRagService.installOllamaModel(modelName);
    
    res.json({
      success: true,
      message: `模型 ${modelName} 安装成功`,
      result
    });
    
  } catch (error) {
    console.error('❌ 安装Ollama模型失败:', error);
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

// 获取可用的Ollama模型列表
router.get('/models', async (req, res) => {
  try {
    const ollamaStatus = await localRagService.checkOllamaStatus();
    
    if (!ollamaStatus.available) {
      return res.status(503).json({
        success: false,
        error: 'Ollama服务不可用'
      });
    }
    
    res.json({
      success: true,
      models: ollamaStatus.models,
      recommended: [
        {
          name: 'qwen2:7b',
          description: '通义千问2，7B参数，中文友好',
          size: '4.4GB'
        },
        {
          name: 'llama3:8b',
          description: 'Meta Llama 3，8B参数，英文优秀',
          size: '4.7GB'
        },
        {
          name: 'mistral:7b',
          description: 'Mistral AI，7B参数，平衡性能',
          size: '4.1GB'
        }
      ]
    });
    
  } catch (error) {
    console.error('❌ 获取模型列表失败:', error);
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

// 启动RAG服务
router.post('/start', async (req, res) => {
  try {
    console.log('🚀 启动RAG服务');

    const result = await localRagService.startService();

    res.json({
      success: true,
      message: 'RAG服务启动成功',
      result
    });

  } catch (error) {
    console.error('❌ 启动RAG服务失败:', error);
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

// 停止RAG服务
router.post('/stop', async (req, res) => {
  try {
    console.log('🛑 停止RAG服务');

    const result = await localRagService.stopService();

    res.json({
      success: true,
      message: 'RAG服务停止成功',
      result
    });

  } catch (error) {
    console.error('❌ 停止RAG服务失败:', error);
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

module.exports = router;
