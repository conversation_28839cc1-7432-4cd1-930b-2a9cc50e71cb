<template>
  <div class="user-management-view">
    <h1 class="page-title">用户管理</h1>
    <p class="page-description">管理系统用户和权限</p>
    
    <div class="user-management-container">
      <div class="user-management-content">
        <div class="user-management-section">
          <div class="section-header">
            <h2 class="section-title">用户列表</h2>
            <button class="btn-primary" @click="showAddUserModal = true">
              <span>+</span> 添加用户
            </button>
          </div>
          
          <div class="user-list">
            <div class="user-list-header">
              <div class="user-column user-name">用户名</div>
              <div class="user-column user-role">角色</div>
              <div class="user-column user-last-login">最后登录</div>
              <div class="user-column user-actions">操作</div>
            </div>
            
            <div v-if="users.length === 0" class="no-users">
              暂无用户数据
            </div>
            
            <div v-else class="user-list-body">
              <div v-for="user in users" :key="user.id" class="user-item">
                <div class="user-column user-name">
                  <div class="user-avatar" v-if="user.avatar" :style="{ backgroundImage: `url(${user.avatar})` }"></div>
                  <div class="user-avatar" v-else>{{ user.username.charAt(0).toUpperCase() }}</div>
                  <div class="user-info">
                    <div class="user-username">{{ user.username }} - {{ user.display_name }}</div>
                  </div>
                </div>
                <div class="user-column user-role">
                  <span class="role-badge" :class="{ 'admin-role': user.isAdmin }">
                    {{ user.isAdmin ? '管理员' : '普通用户' }}
                  </span>
                </div>
                <div class="user-column user-last-login">
                  {{ formatDate(user.last_login_at) }}
                </div>
                <div class="user-column user-actions">
                  <button class="btn-icon" @click="toggleAdminRole(user)" :title="user.isAdmin ? '取消管理员权限' : '设为管理员'">
                    <span>{{ user.isAdmin ? '👑' : '👤' }}</span>
                  </button>
                  <button class="btn-icon" @click="editUser(user)" title="编辑用户">
                    <span>✏️</span>
                  </button>
                  <button class="btn-icon" @click="confirmDeleteUser(user)" title="删除用户">
                    <span>🗑️</span>
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    
    <!-- 添加/编辑用户模态框 -->
    <div v-if="showAddUserModal || showEditUserModal" class="modal">
      <div class="modal-content">
        <div class="modal-header">
          <h3>{{ showEditUserModal ? '编辑用户' : '添加用户' }}</h3>
          <button class="close-btn" @click="closeUserModal">&times;</button>
        </div>
        <div class="modal-body">
          <div class="avatar-section">
            <div class="avatar-container">
              <div class="avatar-preview" :style="avatarPreviewStyle">
                <span v-if="!userForm.avatar && !avatarPreview">{{ userForm.username ? userForm.username.charAt(0).toUpperCase() : 'U' }}</span>
              </div>
              <div class="avatar-actions">
                <label for="avatar-upload" class="avatar-upload-label">更换头像</label>
                <input 
                  type="file" 
                  id="avatar-upload" 
                  class="avatar-upload-input" 
                  accept="image/*"
                  @change="handleAvatarUpload"
                >
              </div>
            </div>
          </div>
          
          <div class="form-group">
            <label for="userUsername">用户名</label>
            <input 
              type="text" 
              id="userUsername" 
              v-model="userForm.username" 
              class="form-input"
              placeholder="请输入用户名"
              :disabled="showEditUserModal"
            >
            <div v-if="userFormErrors.username" class="error-message">{{ userFormErrors.username }}</div>
          </div>
          
          <div class="form-group">
            <label for="userDisplayName">显示名称</label>
            <input 
              type="text" 
              id="userDisplayName" 
              v-model="userForm.display_name" 
              class="form-input"
              placeholder="请输入显示名称"
            >
            <div v-if="userFormErrors.display_name" class="error-message">{{ userFormErrors.display_name }}</div>
          </div>
          
          <div class="form-group" v-if="showAddUserModal || showEditUserModal">
            <label for="userPassword">{{ showEditUserModal ? '新密码 (留空保持不变)' : '密码' }}</label>
            <input 
              type="password" 
              id="userPassword" 
              v-model="userForm.password" 
              class="form-input"
              :placeholder="showEditUserModal ? '输入新密码或留空' : '请输入密码'"
            >
            <div v-if="userFormErrors.password" class="error-message">{{ userFormErrors.password }}</div>
          </div>
          
          <div class="form-group">
            <label class="checkbox-label">
              <input type="checkbox" v-model="userForm.isAdmin">
              <span>管理员权限</span>
            </label>
            <div class="setting-description">管理员可以管理用户和系统设置</div>
          </div>
        </div>
        <div class="modal-footer">
          <button class="btn-secondary" @click="closeUserModal">取消</button>
          <button class="btn-primary" @click="saveUser">保存</button>
        </div>
      </div>
    </div>
    
    <!-- 删除用户确认模态框 -->
    <div v-if="showDeleteUserModal" class="modal">
      <div class="modal-content">
        <div class="modal-header">
          <h3>删除用户</h3>
          <button class="close-btn" @click="showDeleteUserModal = false">&times;</button>
        </div>
        <div class="modal-body">
          <div class="danger-warning">
            <div class="warning-icon">⚠️</div>
            <h4>确定要删除此用户吗？</h4>
            <p>您即将删除用户 "{{ userToDelete?.displayName }}" (@{{ userToDelete?.username }})。</p>
            <p>此操作不可撤销。</p>
          </div>
        </div>
        <div class="modal-footer">
          <button class="btn-secondary" @click="showDeleteUserModal = false">取消</button>
          <button class="btn-danger" @click="deleteUser">删除</button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, computed } from 'vue';
import { useRouter } from 'vue-router';
import { userAPI, getCurrentUserFromCache } from '../services/api.js';

// 用户列表数据
const users = ref([]);

// 当前登录用户
const currentUser = ref(null);

// 获取当前用户信息
function getCurrentUserInfo() {
  const user = getCurrentUserFromCache();
  if (user) {
    currentUser.value = user;
  }
}

// 模态框状态
const showAddUserModal = ref(false);
const showEditUserModal = ref(false);
const showDeleteUserModal = ref(false);
const userToDelete = ref(null);

// 用户表单数据
const userForm = reactive({
  id: null,
  username: '',
  display_name: '',
  password: '',
  isAdmin: false,
  last_login_at: null,
  avatar: ''
});

// 头像预览
const avatarPreview = ref('');

// 头像预览样式
const avatarPreviewStyle = computed(() => {
  if (avatarPreview.value) {
    return { backgroundImage: `url(${avatarPreview.value})` };
  } else if (userForm.avatar) {
    return { backgroundImage: `url(${userForm.avatar})` };
  }
  return {};
});

// 处理头像上传
function handleAvatarUpload(event) {
  const file = event.target.files[0];
  if (!file) return;
  
  // 验证文件类型
  if (!file.type.match('image.*')) {
    alert('请上传图片文件');
    return;
  }
  
  // 验证文件大小（最大 2MB）
  if (file.size > 2 * 1024 * 1024) {
    alert('图片大小不能超过 2MB');
    return;
  }
  
  // 创建文件阅读器
  const reader = new FileReader();
  reader.onload = (e) => {
    avatarPreview.value = e.target.result;
  };
  reader.readAsDataURL(file);
}

// 表单错误信息
const userFormErrors = reactive({
  username: '',
  display_name: '',
  password: ''
});

// 初始化加载用户数据
onMounted(() => {
  getCurrentUserInfo();
  loadUsers();
});

// 加载用户数据
async function loadUsers() {
  try {
    const response = await userAPI.getUserList();
    users.value = response.data.map(user => ({
      ...user,
      isAdmin: user.role === 'admin'
    }));
  } catch (error) {
    console.error('加载用户列表失败:', error);
    alert('加载用户列表失败: ' + (error.message || '请重试'));
    users.value = [];
  }
}

// 格式化日期
function formatDate(dateString) {
  if (!dateString) return '从未登录';
  
  const date = new Date(dateString);
  return date.toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit'
  });
}

// 切换用户管理员权限
async function toggleAdminRole(user) {
  // 防止取消自己的管理员权限
  if (user.id === currentUser.value?.id && user.isAdmin) {
    alert('不能取消自己的管理员权限');
    return;
  }
  
  try {
    const newRole = user.isAdmin ? 'user' : 'admin';
    await userAPI.updateUser(user.id, { role: newRole });
    await loadUsers();
  } catch (error) {
    console.error('更新用户权限失败:', error);
    alert('更新用户权限失败: ' + (error.message || '请重试'));
  }
}

// 打开编辑用户模态框
function editUser(user) {
  showEditUserModal.value = true;
  
  userForm.id = user.id;
  userForm.username = user.username;
  userForm.display_name = user.display_name;
  userForm.password = '';
  userForm.isAdmin = user.role === 'admin';
  userForm.last_login_at = user.last_login_at;
  userForm.avatar = user.avatar_url || '';
  avatarPreview.value = '';
  
  resetUserFormErrors();
}

// 确认删除用户
function confirmDeleteUser(user) {
  // 防止删除自己
  if (user.id === currentUser.value?.id) {
    alert('不能删除当前登录的用户');
    return;
  }
  
  userToDelete.value = user;
  showDeleteUserModal.value = true;
}

// 删除用户
async function deleteUser() {
  if (!userToDelete.value) return;
  
  try {
    await userAPI.deleteUser(userToDelete.value.id);
    await loadUsers();
    showDeleteUserModal.value = false;
    userToDelete.value = null;
    alert('用户删除成功');
  } catch (error) {
    console.error('删除用户失败:', error);
    alert('删除用户失败: ' + (error.message || '请重试'));
  }
}

// 关闭用户模态框
function closeUserModal() {
  showAddUserModal.value = false;
  showEditUserModal.value = false;
  resetUserForm();
}

// 重置用户表单
function resetUserForm() {
  userForm.id = null;
  userForm.username = '';
  userForm.display_name = '';
  userForm.password = '';
  userForm.isAdmin = false;
  userForm.last_login_at = null;
  userForm.avatar = '';
  avatarPreview.value = '';
  resetUserFormErrors();
}

// 重置表单错误信息
function resetUserFormErrors() {
  userFormErrors.username = '';
  userFormErrors.display_name = '';
  userFormErrors.password = '';
}

// 验证用户表单
function validateUserForm() {
  let isValid = true;
  resetUserFormErrors();
  
  if (!userForm.username.trim()) {
    userFormErrors.username = '请输入用户名';
    isValid = false;
  }
  
  if (!userForm.display_name.trim()) {
    userFormErrors.display_name = '请输入显示名称';
    isValid = false;
  }
  
  if (showAddUserModal.value && !userForm.password.trim()) {
    userFormErrors.password = '请输入密码';
    isValid = false;
  }
  
  return isValid;
}

// 保存用户
async function saveUser() {
  if (!validateUserForm()) return;
  
  try {
    if (showAddUserModal.value) {
      // 添加新用户
      const userData = {
        username: userForm.username.trim(),
        password: userForm.password,
        display_name: userForm.display_name.trim(),
        role: userForm.isAdmin ? 'admin' : 'user'
      };
      await userAPI.createUser(userData);
      alert('用户添加成功');
    } else if (showEditUserModal.value) {
      // 更新现有用户
      const updateData = {
        display_name: userForm.display_name.trim(),
        role: userForm.isAdmin ? 'admin' : 'user'
      };
      
      // 如果提供了新密码，则更新密码
      if (userForm.password.trim()) {
        updateData.password = userForm.password;
      }
      
      await userAPI.updateUser(userForm.id, updateData);
      alert('用户更新成功');
    }
    
    await loadUsers();
    closeUserModal();
  } catch (error) {
    console.error('保存用户失败:', error);
    alert('保存用户失败: ' + (error.message || '请重试'));
  }
}
</script>

<style scoped>
.user-management-view {
  padding: 12px 20px;
  height: 100%;
}

.page-title {
  font-size: 1.3rem;
  margin-bottom: 0.2rem;
  color: var(--color-heading);
}

.page-description {
  color: var(--color-text-light);
  margin-bottom: 1rem;
  font-size: 0.85rem;
}

.user-management-container {
  display: flex;
  background-color: var(--color-background-soft);
  border-radius: 8px;
  padding: 0;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  height: calc(100% - 60px);
}

.user-management-content {
  flex: 1;
  padding: 30px;
  width: 100%;
  overflow-y: auto;
}

.user-management-section {
  background-color: white;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.section-title {
  font-size: 1.2rem;
  color: var(--color-heading);
  margin: 0;
}

.user-list {
  border: 1px solid var(--color-border-light);
  border-radius: 6px;
  overflow: hidden;
}

.user-list-header {
  display: flex;
  background-color: var(--color-background-mute);
  padding: 12px 15px;
  font-weight: 500;
  color: var(--color-text);
  border-bottom: 1px solid var(--color-border-light);
}

.user-column {
  padding: 0 10px;
}

.user-name {
  flex: 3;
  display: flex;
  align-items: center;
}

.user-role {
  flex: 1;
  display: flex;
  align-items: center;
}

.user-last-login {
  flex: 2;
  display: flex;
  align-items: center;
}

.user-actions {
  flex: 1;
  display: flex;
  justify-content: flex-end;
  gap: 5px;
}

.user-list-body {
  max-height: 500px;
  overflow-y: auto;
}

.user-item {
  display: flex;
  padding: 15px;
  border-bottom: 1px solid var(--color-border-light);
  transition: background-color 0.2s;
}

.user-item:last-child {
  border-bottom: none;
}

.user-item:hover {
  background-color: var(--color-background-soft);
}

.user-avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background-color: var(--color-primary);
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
  margin-right: 12px;
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
}

.user-info {
  display: flex;
  flex-direction: column;
}

.user-display-name {
  font-weight: 500;
  color: var(--color-heading);
}

.user-username {
  font-size: 0.85rem;
  color: var(--color-text-light);
}

.role-badge {
  display: inline-block;
  padding: 4px 8px;
  border-radius: 12px;
  background-color: #e0e0e0;
  color: #666;
  font-size: 0.8rem;
}

.role-badge.admin-role {
  background-color: #4a6baf;
  color: white;
}

.btn-icon {
  background: none;
  border: none;
  font-size: 1.1rem;
  cursor: pointer;
  padding: 5px;
  border-radius: 4px;
  transition: background-color 0.2s;
}

.btn-icon:hover {
  background-color: rgba(0, 0, 0, 0.05);
}

.no-users {
  padding: 30px;
  text-align: center;
  color: var(--color-text-light);
  font-style: italic;
}

/* 头像部分样式 */
.avatar-section {
  margin-bottom: 30px;
}

.avatar-container {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.avatar-preview {
  width: 100px;
  height: 100px;
  border-radius: 50%;
  background-color: var(--color-primary);
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
  font-size: 2.5rem;
  margin-bottom: 15px;
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  border: 3px solid white;
}

.avatar-actions {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.avatar-upload-label {
  background-color: var(--color-primary);
  color: white;
  padding: 8px 16px;
  border-radius: 4px;
  cursor: pointer;
  transition: background-color 0.2s;
  font-size: 0.9rem;
  text-align: center;
}

.avatar-upload-label:hover {
  background-color: #3d5a8f;
}

.avatar-upload-input {
  display: none;
}

/* 表单样式 */
.form-group {
  margin-bottom: 20px;
}

.form-group label {
  display: block;
  margin-bottom: 8px;
  font-weight: 500;
  color: var(--color-heading);
}

.form-input {
  width: 100%;
  padding: 10px 12px;
  border: 1px solid var(--color-border);
  border-radius: 4px;
  font-size: 1rem;
  transition: border-color 0.2s;
}

.form-input:focus {
  border-color: var(--color-primary);
  outline: none;
}

.form-input:disabled {
  background-color: #f5f5f5;
  cursor: not-allowed;
}

.checkbox-label {
  display: flex;
  align-items: center;
  gap: 8px;
  cursor: pointer;
}

.setting-description {
  font-size: 0.85rem;
  color: var(--color-text-light);
  margin-top: 5px;
  margin-left: 24px;
}

.error-message {
  color: #dc3545;
  font-size: 0.85rem;
  margin-top: 5px;
}

/* 按钮样式 */
.btn-primary {
  background-color: #4a6baf;
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 4px;
  cursor: pointer;
  transition: background-color 0.2s;
  display: flex;
  align-items: center;
  gap: 5px;
}

.btn-primary:hover {
  background-color: #3d5a8f;
}

.btn-secondary {
  background-color: #f0f0f0;
  color: var(--color-text);
  border: 1px solid var(--color-border);
  padding: 8px 16px;
  border-radius: 4px;
  cursor: pointer;
  transition: background-color 0.2s;
}

.btn-secondary:hover {
  background-color: #e0e0e0;
}

.btn-danger {
  background-color: #dc3545;
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 4px;
  cursor: pointer;
  transition: background-color 0.2s;
}

.btn-danger:hover {
  background-color: #bd2130;
}

/* 模态框样式 */
.modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.modal-content {
  background-color: white;
  border-radius: 8px;
  width: 90%;
  max-width: 500px;
  max-height: 90vh;
  overflow-y: auto;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px 20px;
  border-bottom: 1px solid var(--color-border);
}

.modal-body {
  padding: 20px;
}

.modal-footer {
  padding: 15px 20px;
  border-top: 1px solid var(--color-border);
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}

.close-btn {
  background: none;
  border: none;
  font-size: 1.5rem;
  cursor: pointer;
  color: var(--color-text-light);
}

/* 警告样式 */
.danger-warning {
  text-align: center;
  margin-bottom: 20px;
}

.warning-icon {
  font-size: 3rem;
  margin-bottom: 10px;
  color: #dc3545;
}

.danger-warning h4 {
  color: #dc3545;
  margin-bottom: 10px;
}

/* 响应式调整 */
@media (max-width: 768px) {
  .user-list-header, .user-item {
    flex-direction: column;
    padding: 10px;
  }
  
  .user-column {
    padding: 5px 0;
  }
  
  .user-actions {
    justify-content: flex-start;
    margin-top: 10px;
  }
  
  .section-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 10px;
  }
}
</style>