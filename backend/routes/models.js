/**
 * AI模型管理路由
 * 
 * 功能说明：
 * - AI模型配置管理
 * - 模型状态管理
 * - 模型使用统计
 */

const express = require('express');
const router = express.Router();

const db = require('../config/database');
const { authenticateToken, requireAdmin } = require('../middleware/auth');
const { asyncHandler, ValidationError, NotFoundError, createSuccessResponse } = require('../middleware/errorHandler');

// ==================== 模型管理 ====================

/**
 * @route GET /api/models
 * @desc 获取可用AI模型列表
 * @access Private
 */
router.get('/', authenticateToken, asyncHandler(async (req, res) => {
  const { type } = req.query;

  let query = `
    SELECT id, name, type, provider, max_tokens, temperature, is_active, created_at, updated_at, api_Key, system_prompt
    FROM ai_models
    WHERE is_active = 1
  `;
  const params = [];

  if (type) {
    query += ' AND type = ?';
    params.push(type);
  }

  query += ' ORDER BY name ASC';

  const models = await db.execute(query, params);
  
  res.json(createSuccessResponse(models));
}));

/**
 * @route GET /api/models/all
 * @desc 获取所有AI模型列表（包括禁用的）
 * @access Private (Admin only)
 */
router.get('/all', authenticateToken, requireAdmin, asyncHandler(async (req, res) => {
  const models = await db.execute(
    `SELECT id, name, type, provider, api_endpoint, max_tokens, temperature, is_active, created_at, updated_at, api_Key as apiKey, system_prompt
     FROM ai_models
     ORDER BY name ASC`
  );
  
  res.json(createSuccessResponse(models));
}));

/**
 * @route GET /api/models/:id
 * @desc 获取单个AI模型详情
 * @access Private
 */
router.get('/:id', authenticateToken, asyncHandler(async (req, res) => {
  const { id } = req.params;
  
  const [model] = await db.execute(
    `SELECT id, name, type, provider, api_endpoint, max_tokens, temperature, is_active, created_at, updated_at, api_Key, system_prompt
     FROM ai_models
     WHERE id = ?`,
    [id]
  );
  
  if (!model) {
    throw new NotFoundError('AI模型不存在');
  }
  
  res.json(createSuccessResponse(model));
}));

/**
 * @route POST /api/models
 * @desc 创建AI模型配置
 * @access Private (Admin only)
 */
router.post('/', authenticateToken, requireAdmin, asyncHandler(async (req, res) => {
  const { id, name, type, provider, api_endpoint, max_tokens = 4096, temperature = 0.7, system_prompt } = req.body;
  
  if (!id || !name || !type || !provider) {
    throw new ValidationError('模型ID、名称、类型和提供商不能为空');
  }
  
  // 检查模型ID是否已存在
  const [existing] = await db.execute(
    'SELECT id FROM ai_models WHERE id = ?',
    [id]
  );
  
  if (existing) {
    throw new ValidationError('模型ID已存在');
  }
  
  await db.execute(
    `INSERT INTO ai_models (id, name, type, provider, api_endpoint, max_tokens, temperature, system_prompt)
     VALUES (?, ?, ?, ?, ?, ?, ?, ?)`,
    [id, name, type, provider, api_endpoint, max_tokens, temperature, system_prompt]
  );
  
  const [newModel] = await db.execute(
    `SELECT id, name, type, provider, api_endpoint, max_tokens, temperature, is_active, created_at, updated_at, system_prompt
     FROM ai_models
     WHERE id = ?`,
    [id]
  );
  
  res.status(201).json(createSuccessResponse(newModel, 'AI模型创建成功'));
}));

/**
 * @route PUT /api/models/:id
 * @desc 更新AI模型配置
 * @access Private (Admin only)
 */
router.put('/:id', authenticateToken, requireAdmin, asyncHandler(async (req, res) => {
  const { id } = req.params;
  const { name, type, provider, api_endpoint, max_tokens, temperature, is_active, apiKey, system_prompt } = req.body;
  
  // 检查模型是否存在
  const [existing] = await db.execute(
    'SELECT id FROM ai_models WHERE id = ?',
    [id]
  );
  
  if (!existing) {
    throw new NotFoundError('AI模型不存在');
  }
  
  if (!name || !type || !provider) {
    throw new ValidationError('名称、类型和提供商不能为空');
  }
  
  await db.execute(
    `UPDATE ai_models 
     SET name = ?, type = ?, provider = ?, api_endpoint = ?, max_tokens = ?, temperature = ?, is_active = ?, api_Key = ?, system_prompt = ?
     WHERE id = ?`,
    [name, type, provider, api_endpoint, max_tokens || 4096, temperature || 0.7, is_active !== undefined ? is_active : 1, apiKey, system_prompt, id]
  );
  
  const [updatedModel] = await db.execute(
    `SELECT id, name, type, provider, api_endpoint, max_tokens, temperature, is_active, created_at, updated_at, system_prompt
     FROM ai_models
     WHERE id = ?`,
    [id]
  );
  
  res.json(createSuccessResponse(updatedModel, 'AI模型更新成功'));
}));

/**
 * @route DELETE /api/models/:id
 * @desc 删除AI模型配置
 * @access Private (Admin only)
 */
router.delete('/:id', authenticateToken, requireAdmin, asyncHandler(async (req, res) => {
  const { id } = req.params;
  
  // 检查模型是否存在
  const [existing] = await db.execute(
    'SELECT id FROM ai_models WHERE id = ?',
    [id]
  );
  
  if (!existing) {
    throw new NotFoundError('AI模型不存在');
  }
  
  // 检查是否有相关的使用记录
  const [usageCount] = await db.execute(
    'SELECT COUNT(*) as count FROM model_usage WHERE model_id = ?',
    [id]
  );
  
  if (usageCount.count > 0) {
    throw new ValidationError('该模型存在使用记录，无法删除');
  }
  
  await db.execute('DELETE FROM ai_models WHERE id = ?', [id]);
  
  res.json(createSuccessResponse(null, 'AI模型删除成功'));
}));

/**
 * @route PUT /api/models/:id/toggle
 * @desc 切换AI模型状态
 * @access Private (Admin only)
 */
router.put('/:id/toggle', authenticateToken, requireAdmin, asyncHandler(async (req, res) => {
  const { id } = req.params;
  
  // 检查模型是否存在
  const [existing] = await db.execute(
    'SELECT id, is_active FROM ai_models WHERE id = ?',
    [id]
  );
  
  if (!existing) {
    throw new NotFoundError('AI模型不存在');
  }
  
  // 切换状态
  const newStatus = existing.is_active === 1 ? 0 : 1;
  
  await db.execute(
    'UPDATE ai_models SET is_active = ? WHERE id = ?',
    [newStatus, id]
  );
  
  const [updatedModel] = await db.execute(
    `SELECT id, name, type, provider, api_endpoint, max_tokens, temperature, is_active, created_at, updated_at
     FROM ai_models
     WHERE id = ?`,
    [id]
  );
  
  res.json(createSuccessResponse(updatedModel, `AI模型${newStatus === 1 ? '启用' : '禁用'}成功`));
}));

/**
 * @route GET /api/models/:id/usage
 * @desc 获取AI模型使用统计
 * @access Private (Admin only)
 */
router.get('/:id/usage', authenticateToken, requireAdmin, asyncHandler(async (req, res) => {
  const { id } = req.params;
  const { start_date, end_date } = req.query;
  
  // 检查模型是否存在
  const [existing] = await db.execute(
    'SELECT id FROM ai_models WHERE id = ?',
    [id]
  );
  
  if (!existing) {
    throw new NotFoundError('AI模型不存在');
  }
  
  let query = `
    SELECT 
      DATE(created_at) as date,
      COUNT(*) as request_count,
      SUM(input_tokens) as total_input_tokens,
      SUM(output_tokens) as total_output_tokens,
      SUM(total_tokens) as total_tokens
    FROM model_usage 
    WHERE model_id = ?
  `;
  
  const params = [id];
  
  if (start_date) {
    query += ' AND DATE(created_at) >= ?';
    params.push(start_date);
  }
  
  if (end_date) {
    query += ' AND DATE(created_at) <= ?';
    params.push(end_date);
  }
  
  query += ' GROUP BY DATE(created_at) ORDER BY date DESC';
  
  const usage = await db.execute(query, params);
  
  const summary = await db.execute(
    `SELECT 
      SUM(total_tokens) as total_tokens
    FROM model_usage 
    WHERE model_id = ?`,
    [id]
  );
  
  const dailyUsage = usage.map(row => ({
    date: row.date,
    request_count: row.request_count,
    total_input_tokens: row.total_input_tokens,
    total_output_tokens: row.total_output_tokens,
    total_tokens: row.total_tokens
  }));
  
  res.json(createSuccessResponse({
    summary: summary[0],
    daily_usage: dailyUsage
  }));
}));

// 导出路由
module.exports = router;