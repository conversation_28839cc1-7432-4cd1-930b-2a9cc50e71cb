const { spawn } = require('child_process');
const path = require('path');
const fs = require('fs').promises;

class LocalRAGService {
  constructor() {
    this.ragAnythingPath = '/opt/RAG-Anything';
    this.workingDir = path.join(__dirname, '../rag_storage');
    this.uploadsDir = path.join(__dirname, '../uploads');
    this.localConfigPath = path.join(this.ragAnythingPath, 'local_rag_config.py');
    this.isInitialized = false;
    
    // 本地模型配置
    this.config = {
      embeddingModel: 'BAAI/bge-small-zh-v1.5', // 中文嵌入模型
      llmModel: 'qwen2:7b', // Ollama模型
      ollamaUrl: 'http://localhost:11434'
    };
    
    this.ensureDirectories();
  }

  async ensureDirectories() {
    try {
      await fs.mkdir(this.workingDir, { recursive: true });
      await fs.mkdir(this.uploadsDir, { recursive: true });
      console.log('📁 本地RAG目录创建成功');
    } catch (error) {
      console.error('❌ 创建本地RAG目录失败:', error);
    }
  }

  // 检查Ollama服务状态
  async checkOllamaStatus() {
    return new Promise((resolve) => {
      const checkProcess = spawn('curl', ['-s', `${this.config.ollamaUrl}/api/tags`], {
        stdio: ['pipe', 'pipe', 'pipe']
      });

      let stdout = '';
      let stderr = '';

      checkProcess.stdout.on('data', (data) => {
        stdout += data.toString();
      });

      checkProcess.stderr.on('data', (data) => {
        stderr += data.toString();
      });

      checkProcess.on('close', (code) => {
        if (code === 0) {
          try {
            const models = JSON.parse(stdout);
            resolve({
              available: true,
              models: models.models || [],
              url: this.config.ollamaUrl
            });
          } catch (error) {
            resolve({
              available: false,
              error: 'Ollama响应格式错误'
            });
          }
        } else {
          resolve({
            available: false,
            error: 'Ollama服务不可用'
          });
        }
      });
    });
  }

  // 安装推荐的Ollama模型
  async installOllamaModel(modelName = 'qwen2:7b') {
    return new Promise((resolve, reject) => {
      console.log(`🔄 开始安装Ollama模型: ${modelName}`);
      
      const installProcess = spawn('ollama', ['pull', modelName], {
        stdio: ['pipe', 'pipe', 'pipe']
      });

      let stdout = '';
      let stderr = '';

      installProcess.stdout.on('data', (data) => {
        stdout += data.toString();
        console.log('📦 模型下载进度:', data.toString().trim());
      });

      installProcess.stderr.on('data', (data) => {
        stderr += data.toString();
      });

      installProcess.on('close', (code) => {
        if (code === 0) {
          console.log(`✅ 模型 ${modelName} 安装成功`);
          resolve({
            success: true,
            model: modelName,
            output: stdout
          });
        } else {
          console.error(`❌ 模型 ${modelName} 安装失败:`, stderr);
          reject(new Error(`模型安装失败: ${stderr}`));
        }
      });
    });
  }

  // 创建本地RAG配置文件
  async createLocalConfig() {
    const configContent = `#!/usr/bin/env python3
"""
本地RAG配置 - 使用本地模型，无需OpenAI API
"""

import asyncio
import os
import sys
from typing import List, Optional
import torch
from sentence_transformers import SentenceTransformer
import numpy as np

# 添加项目路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from lightrag.llm.openai import openai_complete_if_cache, openai_embed
from lightrag.utils import EmbeddingFunc
from raganything.raganything import RAGAnything

class LocalEmbeddingFunc:
    """本地嵌入模型函数"""
    
    def __init__(self, model_name: str = "${this.config.embeddingModel}"):
        self.model_name = model_name
        self.model = None
        self.embedding_dim = None
        
    def _load_model(self):
        if self.model is None:
            print(f"🔄 加载本地嵌入模型: {self.model_name}")
            try:
                self.model = SentenceTransformer(self.model_name)
                test_embedding = self.model.encode(["test"])
                self.embedding_dim = test_embedding.shape[1]
                print(f"✅ 模型加载成功，嵌入维度: {self.embedding_dim}")
            except Exception as e:
                print(f"❌ 模型加载失败: {e}")
                self.model = SentenceTransformer('all-MiniLM-L6-v2')
                test_embedding = self.model.encode(["test"])
                self.embedding_dim = test_embedding.shape[1]
                print(f"✅ 默认模型加载成功，嵌入维度: {self.embedding_dim}")
    
    def __call__(self, texts: List[str]) -> List[List[float]]:
        self._load_model()
        try:
            embeddings = self.model.encode(texts, convert_to_tensor=False)
            if isinstance(embeddings, np.ndarray):
                embeddings = embeddings.tolist()
            return embeddings
        except Exception as e:
            print(f"❌ 嵌入生成失败: {e}")
            return [[0.0] * self.embedding_dim for _ in texts]

class LocalLLMFunc:
    """本地LLM函数 - 支持Ollama"""
    
    def __init__(self, model_name: str = "${this.config.llmModel}", base_url: str = "${this.config.ollamaUrl}"):
        self.model_name = model_name
        self.base_url = base_url
        
    async def __call__(self, prompt: str, system_prompt: Optional[str] = None, 
                      history_messages: List = None, **kwargs) -> str:
        try:
            import requests
            
            messages = []
            if system_prompt:
                messages.append({"role": "system", "content": system_prompt})
            if history_messages:
                messages.extend(history_messages)
            messages.append({"role": "user", "content": prompt})
            
            response = requests.post(
                f"{self.base_url}/api/chat",
                json={
                    "model": self.model_name,
                    "messages": messages,
                    "stream": False
                },
                timeout=60
            )
            
            if response.status_code == 200:
                result = response.json()
                return result.get("message", {}).get("content", "")
            else:
                return "抱歉，本地模型暂时不可用。"
                
        except Exception as e:
            print(f"❌ 本地LLM调用失败: {e}")
            return "抱歉，本地模型调用出现错误。"

def create_local_rag(working_dir: str) -> RAGAnything:
    local_embedding = LocalEmbeddingFunc()
    local_llm = LocalLLMFunc()
    
    embedding_func = EmbeddingFunc(
        embedding_dim=384,
        max_token_size=512,
        func=local_embedding
    )
    
    rag = RAGAnything(
        working_dir=working_dir,
        llm_model_func=local_llm,
        embedding_func=embedding_func
    )
    
    if hasattr(local_embedding, 'embedding_dim') and local_embedding.embedding_dim:
        embedding_func.embedding_dim = local_embedding.embedding_dim
    
    return rag

if __name__ == "__main__":
    import sys
    if len(sys.argv) > 1:
        working_dir = sys.argv[1]
        query = sys.argv[2] if len(sys.argv) > 2 else "测试查询"
        
        async def main():
            rag = create_local_rag(working_dir)
            result = await rag.query_with_multimodal(query, mode="hybrid")
            print("LOCAL_RAG_RESULT:", result)
        
        asyncio.run(main())
`;

    try {
      await fs.writeFile(this.localConfigPath, configContent);
      console.log('✅ 本地RAG配置文件创建成功');
      return true;
    } catch (error) {
      console.error('❌ 创建本地RAG配置文件失败:', error);
      return false;
    }
  }

  // 使用本地模型处理文档
  async processDocumentLocal(filePath) {
    return new Promise((resolve, reject) => {
      console.log('🔄 使用本地模型处理文档:', filePath);
      
      const pythonProcess = spawn('bash', ['-c', `cd ${this.ragAnythingPath} && source venv/bin/activate && python ${this.localConfigPath} ${this.workingDir} process_document`], {
        stdio: ['pipe', 'pipe', 'pipe']
      });

      let stdout = '';
      let stderr = '';

      pythonProcess.stdout.on('data', (data) => {
        stdout += data.toString();
        console.log('📄 本地RAG处理输出:', data.toString());
      });

      pythonProcess.stderr.on('data', (data) => {
        stderr += data.toString();
        console.error('⚠️ 本地RAG处理警告:', data.toString());
      });

      pythonProcess.on('close', (code) => {
        if (code === 0) {
          console.log('✅ 本地文档处理完成');
          resolve({
            success: true,
            message: '文档处理完成（本地模型）',
            output: stdout
          });
        } else {
          console.error('❌ 本地文档处理失败，退出码:', code);
          reject(new Error(`本地文档处理失败: ${stderr}`));
        }
      });
    });
  }

  // 使用本地模型查询RAG
  async queryRAGLocal(query) {
    return new Promise((resolve, reject) => {
      console.log('🔍 使用本地模型查询RAG知识库:', query);

      const pythonProcess = spawn('python3', ['/opt/scaia/backend/scripts/offline_local_rag.py', this.workingDir, 'query', query], {
        stdio: ['pipe', 'pipe', 'pipe']
      });

      let stdout = '';
      let stderr = '';

      pythonProcess.stdout.on('data', (data) => {
        stdout += data.toString();
      });

      pythonProcess.stderr.on('data', (data) => {
        stderr += data.toString();
      });

      pythonProcess.on('close', (code) => {
        if (code === 0) {
          const lines = stdout.split('\n');
          const resultLine = lines.find(line => line.startsWith('LOCAL_RAG_RESULT:'));

          if (resultLine) {
            const result = resultLine.replace('LOCAL_RAG_RESULT:', '').trim();
            resolve({
              success: true,
              result: result,
              query: query,
              mode: 'local-offline'
            });
          } else {
            resolve({
              success: true,
              result: '未找到相关信息（本地模型）',
              query: query,
              mode: 'local-offline'
            });
          }
        } else {
          const errorLine = stdout.split('\n').find(line => line.startsWith('LOCAL_RAG_ERROR:'));
          const errorMsg = errorLine ? errorLine.replace('LOCAL_RAG_ERROR:', '').trim() : stderr;
          reject(new Error(`本地查询失败: ${errorMsg}`));
        }
      });
    });
  }

  // 使用简化版本地RAG添加文档
  async addDocumentLocal(content, metadata = {}) {
    return new Promise((resolve, reject) => {
      console.log('📄 使用本地模型添加文档');

      const pythonProcess = spawn('python3', ['/opt/scaia/backend/scripts/offline_local_rag.py', this.workingDir, 'add', content], {
        stdio: ['pipe', 'pipe', 'pipe']
      });

      let stdout = '';
      let stderr = '';

      pythonProcess.stdout.on('data', (data) => {
        stdout += data.toString();
      });

      pythonProcess.stderr.on('data', (data) => {
        stderr += data.toString();
      });

      pythonProcess.on('close', (code) => {
        if (code === 0) {
          const lines = stdout.split('\n');
          const resultLine = lines.find(line => line.startsWith('LOCAL_RAG_RESULT:'));

          if (resultLine) {
            const result = resultLine.replace('LOCAL_RAG_RESULT:', '').trim();
            resolve({
              success: true,
              result: result,
              mode: 'local-offline'
            });
          } else {
            resolve({
              success: true,
              result: '文档添加成功',
              mode: 'local-offline'
            });
          }
        } else {
          const errorLine = stdout.split('\n').find(line => line.startsWith('LOCAL_RAG_ERROR:'));
          const errorMsg = errorLine ? errorLine.replace('LOCAL_RAG_ERROR:', '').trim() : stderr;
          reject(new Error(`文档添加失败: ${errorMsg}`));
        }
      });
    });
  }

  // 获取本地RAG状态
  async getLocalRAGStatus() {
    try {
      // 检查Ollama状态
      const ollamaStatus = await this.checkOllamaStatus();

      // 检查配置文件
      const configExists = await fs.access(this.localConfigPath).then(() => true).catch(() => false);

      // 检查Python依赖
      const dependenciesCheck = await this.checkPythonDependencies();

      // 测试本地嵌入模型
      const embeddingTest = await this.testLocalEmbedding();

      // 本地RAG可用条件：Python依赖 + 嵌入模型可用（Ollama可选）
      const available = dependenciesCheck.available && embeddingTest.available;

      return {
        available,
        ollama: ollamaStatus,
        config: {
          exists: configExists,
          path: this.localConfigPath
        },
        dependencies: dependenciesCheck,
        embedding: embeddingTest,
        models: {
          embedding: this.config.embeddingModel,
          llm: this.config.llmModel
        },
        mode: ollamaStatus.available ? 'full' : 'embedding-only'
      };
    } catch (error) {
      return {
        available: false,
        error: error.message
      };
    }
  }

  // 检查Python依赖
  async checkPythonDependencies() {
    return new Promise((resolve) => {
      const checkProcess = spawn('bash', ['-c', `cd ${this.ragAnythingPath} && source venv/bin/activate && python -c "import sentence_transformers, torch; print('OK')"`], {
        stdio: ['pipe', 'pipe', 'pipe']
      });

      checkProcess.on('close', (code) => {
        resolve({
          available: code === 0,
          message: code === 0 ? 'Python依赖已安装' : 'Python依赖缺失'
        });
      });
    });
  }

  // 测试本地嵌入模型
  async testLocalEmbedding() {
    return new Promise((resolve) => {
      // 测试离线RAG系统
      const testProcess = spawn('python3', ['/opt/scaia/backend/scripts/offline_local_rag.py', '/tmp/test_local_rag', 'stats'], {
        stdio: ['pipe', 'pipe', 'pipe']
      });

      let stdout = '';
      let stderr = '';

      testProcess.stdout.on('data', (data) => {
        stdout += data.toString();
      });

      testProcess.stderr.on('data', (data) => {
        stderr += data.toString();
      });

      testProcess.on('close', (code) => {
        const success = code === 0 && stdout.includes('LOCAL_RAG_RESULT:');
        resolve({
          available: success,
          message: success ? '离线本地RAG系统可用' : '离线本地RAG系统不可用',
          output: stdout,
          error: stderr
        });
      });
    });
  }
  // 启动RAG服务
  async startService() {
    try {
      console.log('🚀 启动RAG服务...');

      // 检查Ollama状态
      const ollamaStatus = await this.checkOllamaStatus();

      if (!ollamaStatus.available) {
        console.log('⚠️ Ollama不可用，使用离线RAG模式');

        // 测试离线RAG
        const offlineTest = await this.testLocalEmbedding();

        if (offlineTest.available) {
          return {
            success: true,
            message: 'RAG服务启动成功（离线模式）',
            mode: 'offline',
            status: {
              ollama: ollamaStatus,
              offline: offlineTest
            }
          };
        } else {
          throw new Error('Ollama和离线RAG都不可用');
        }
      }

      // Ollama可用，检查是否有模型
      if (!ollamaStatus.models || ollamaStatus.models.length === 0) {
        console.log('🔄 安装默认模型...');
        try {
          await this.installOllamaModel('qwen2:7b');
        } catch (error) {
          console.log('⚠️ 模型安装失败，使用离线RAG模式');
          const offlineTest = await this.testLocalEmbedding();

          if (offlineTest.available) {
            return {
              success: true,
              message: 'RAG服务启动成功（离线模式）',
              mode: 'offline',
              status: {
                ollama: ollamaStatus,
                offline: offlineTest
              }
            };
          }
        }
      }

      return {
        success: true,
        message: 'RAG服务启动成功（Ollama模式）',
        mode: 'ollama',
        status: ollamaStatus
      };

    } catch (error) {
      console.error('❌ 启动RAG服务失败:', error);
      throw error;
    }
  }

  // 停止RAG服务
  async stopService() {
    try {
      console.log('🛑 停止RAG服务...');

      // 停止Ollama服务
      const stopResult = await this.stopOllama();

      return {
        success: true,
        message: 'RAG服务停止成功',
        result: stopResult
      };

    } catch (error) {
      console.error('❌ 停止RAG服务失败:', error);
      throw error;
    }
  }

  // 启动Ollama
  async startOllama() {
    return new Promise((resolve) => {
      console.log('🔄 启动Ollama服务...');

      const ollamaProcess = spawn('ollama', ['serve'], {
        detached: true,
        stdio: 'ignore'
      });

      ollamaProcess.unref();

      // 等待几秒让服务启动
      setTimeout(async () => {
        const status = await this.checkOllamaStatus();
        resolve({
          success: status.available,
          message: status.available ? 'Ollama启动成功' : 'Ollama启动失败'
        });
      }, 3000);
    });
  }

  // 停止Ollama
  async stopOllama() {
    return new Promise((resolve) => {
      console.log('🛑 停止Ollama服务...');

      // 在Linux/Mac上使用pkill
      const killProcess = spawn('pkill', ['-f', 'ollama'], {
        stdio: 'ignore'
      });

      killProcess.on('close', (code) => {
        resolve({
          success: true,
          message: 'Ollama服务已停止'
        });
      });

      killProcess.on('error', (error) => {
        resolve({
          success: false,
          message: '停止Ollama失败: ' + error.message
        });
      });
    });
  }
}

module.exports = new LocalRAGService();
