<template>
  <div class="knowledge-base-view">
    <h1 class="page-title">知识库</h1>
    <p class="page-description">上传和管理您的设计知识资源，供AI聊天时引用</p>
    
    <div class="knowledge-container">
      <div class="action-bar">
        <button class="btn btn-primary" @click="showUploadModal = true">
          <span class="btn-icon">📤</span> 上传文件
        </button>
        <div class="search-box">
          <input 
            type="text" 
            v-model="searchQuery" 
            class="form-input" 
            placeholder="搜索知识库..."
          >
          <button class="search-btn" @click="searchKnowledge">
            🔍
          </button>
        </div>
      </div>
      
      <div class="knowledge-list">
        <div v-if="filteredKnowledgeItems.length === 0" class="empty-state">
          <div class="empty-icon">📚</div>
          <h3>知识库为空</h3>
          <p>点击"上传文件"按钮添加您的第一个知识文件</p>
        </div>
        
        <div v-else class="knowledge-grid">
          <div 
            v-for="item in filteredKnowledgeItems" 
            :key="item.id"
            class="knowledge-item"
          >
            <div class="item-icon">
              {{ getFileIcon(item.fileType) }}
            </div>
            <div class="item-content">
              <h3 class="item-title">{{ item.title }}</h3>
              <p class="item-meta">
                <span class="item-type">{{ item.fileType }}</span>
                <span class="item-size">{{ formatFileSize(item.size) }}</span>
                <span class="item-date">{{ formatDate(item.uploadDate) }}</span>
              </p>
              <p class="item-description">{{ item.description }}</p>
              <div class="item-tags">
                <span 
                  v-for="(tag, tagIndex) in (Array.isArray(item.tags) ? item.tags : [])" 
                  :key="tagIndex"
                  class="tag"
                >
                  {{ typeof tag === 'object' ? tag.name : tag }}
                </span>
              </div>
            </div>
            <div class="item-actions">
              <button class="action-btn" @click="viewKnowledgeDetails(item)" title="查看详情">
                👁️
              </button>
              <button class="action-btn" @click="editKnowledge(item)" title="编辑">
                ✏️
              </button>
              <button class="action-btn" @click="deleteKnowledge(item)" title="删除">
                🗑️
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
    
    <!-- 上传文件模态框 -->
    <div v-if="showUploadModal" class="modal">
      <div class="modal-content">
        <div class="modal-header">
          <h3>上传知识文件</h3>
          <button class="close-btn" @click="showUploadModal = false">×</button>
        </div>
        <div class="modal-body">
          <div class="form-group">
            <label class="form-label">选择文件</label>
            <input 
              type="file" 
              class="file-input" 
              accept=".pdf,.txt,.doc,.docx"
              @change="handleFileSelect"
              ref="fileInput"
            >
          </div>
          
          <div class="form-group">
            <label class="form-label">标题</label>
            <input type="text" v-model="uploadData.title" class="form-input" placeholder="为文件添加标题">
          </div>
          
          <div class="form-group">
            <label class="form-label">描述</label>
            <textarea v-model="uploadData.description" class="form-input" rows="3" placeholder="添加文件描述"></textarea>
          </div>
          
          <div class="form-group">
            <label class="form-label">标签</label>
            <input type="text" v-model="uploadData.tags" class="form-input" placeholder="用逗号分隔多个标签">
          </div>
        </div>
        <div class="modal-footer">
          <button class="btn btn-secondary" @click="showUploadModal = false">取消</button>
          <button class="btn btn-primary" @click="uploadFile">上传</button>
        </div>
      </div>
    </div>
    
    <!-- 知识详情模态框 -->
    <div v-if="showDetailsModal" class="modal">
      <div class="modal-content">
        <div class="modal-header">
          <h3>知识详情</h3>
          <button class="close-btn" @click="showDetailsModal = false">×</button>
        </div>
        <div class="modal-body">
          <div class="detail-header">
            <div class="detail-icon">
              {{ getFileIcon(selectedItem.fileType) }}
            </div>
            <div class="detail-title-section">
              <h2 class="detail-title">{{ selectedItem.title }}</h2>
              <p class="detail-meta">
                <span class="detail-type">{{ selectedItem.fileType }}</span>
                <span class="detail-size">{{ formatFileSize(selectedItem.size) }}</span>
                <span class="detail-date">上传于 {{ formatDate(selectedItem.uploadDate) }}</span>
              </p>
            </div>
          </div>
          
          <div class="detail-section">
            <h4>描述</h4>
            <p>{{ selectedItem.description || '无描述' }}</p>
          </div>
          
          <div class="detail-section">
            <h4>标签</h4>
            <div class="detail-tags">
              <span 
                v-for="(tag, tagIndex) in (Array.isArray(selectedItem.tags) ? selectedItem.tags : [])" 
                :key="tagIndex"
                class="tag"
              >
                {{ typeof tag === 'object' ? tag.name : tag }}
              </span>
              <span v-if="!selectedItem.tags || selectedItem.tags.length === 0" class="no-tags">无标签</span>
            </div>
          </div>
          
          <div class="detail-section">
            <h4>内容预览</h4>
            <div class="content-preview">
              <p v-if="selectedItem.preview">{{ selectedItem.preview }}</p>
              <p v-else class="no-preview">无法预览此文件类型的内容</p>
            </div>
          </div>
          
          <div class="detail-section">
            <h4>使用方法</h4>
            <p>在聊天界面中，输入 <code>@{{ selectedItem.title }}</code> 即可引用此知识</p>
          </div>
        </div>
        <div class="modal-footer">
          <button class="btn btn-secondary" @click="showDetailsModal = false">关闭</button>
          <button class="btn btn-primary" @click="editKnowledge(selectedItem)">编辑</button>
        </div>
      </div>
    </div>
    
    <!-- 编辑知识模态框 -->
    <div v-if="showEditModal" class="modal">
      <div class="modal-content">
        <div class="modal-header">
          <h3>编辑知识</h3>
          <button class="close-btn" @click="showEditModal = false">×</button>
        </div>
        <div class="modal-body">
          <div class="form-group">
            <label class="form-label">标题</label>
            <input type="text" v-model="editData.title" class="form-input">
          </div>
          
          <div class="form-group">
            <label class="form-label">描述</label>
            <textarea v-model="editData.description" class="form-input" rows="3"></textarea>
          </div>
          
          <div class="form-group">
            <label class="form-label">标签</label>
            <input type="text" v-model="editData.tagsString" class="form-input" placeholder="用逗号分隔多个标签">
          </div>
        </div>
        <div class="modal-footer">
          <button class="btn btn-secondary" @click="showEditModal = false">取消</button>
          <button class="btn btn-primary" @click="saveKnowledgeEdit">保存</button>
        </div>
      </div>
    </div>

    <!-- 向量检索对话框 -->
    <div v-if="showVectorSearch" class="modal-overlay" @click="closeVectorSearch">
      <div class="modal-content" @click.stop>
        <div class="modal-header">
          <h3>向量检索</h3>
          <button class="btn-close" @click="closeVectorSearch">×</button>
        </div>
        <div class="modal-body">
          <div class="search-form">
            <input 
              v-model="vectorQuery" 
              type="text" 
              placeholder="输入搜索内容..."
              class="form-input"
              @keyup.enter="performVectorSearch"
            />
            <button class="btn btn-primary" @click="performVectorSearch">搜索</button>
          </div>
          
          <div v-if="vectorSearchResults.length > 0" class="search-results">
            <h4>检索结果 ({{ vectorSearchResults.length }})</h4>
            <div v-for="result in vectorSearchResults" :key="result.id" class="search-result-item">
              <div class="result-header">
                <span class="result-title">{{ result.title || '未知文档' }}</span>
                <span class="result-similarity">相似度: {{ (result.similarity * 100).toFixed(1) }}%</span>
              </div>
              <div class="result-content">{{ result.content }}</div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue';
import { 
  getKnowledgeItems, 
  createKnowledgeItem, 
  updateKnowledgeItem, 
  deleteKnowledgeItem,
  uploadKnowledgeFile,
  vectorizeKnowledgeItem,
  getVectorizationProgress,
  vectorSearch
} from '@/services/api';

// 响应式数据
const knowledgeItems = ref([]);
const loading = ref(false);
const error = ref('');
const searchQuery = ref('');
const showUploadModal = ref(false);
const showDetailsModal = ref(false);
const showEditModal = ref(false);
const selectedItem = ref(null);
const fileInput = ref(null);

// 向量化相关数据
const vectorizationProgress = ref({});
const vectorizationStatus = ref({}); // 存储每个项目的向量化状态
const vectorSearchResults = ref([]);
const showVectorSearch = ref(false);
const vectorQuery = ref('');

// 表单数据
const showForm = ref(false);
const formData = ref({
  title: '',
  description: '',
  file: null
});

// 加载知识库数据
async function loadKnowledgeItems() {
  try {
    loading.value = true;
    error.value = '';
    const response = await getKnowledgeItems();
    
    // 正确处理API响应
    if (response.success) {
      knowledgeItems.value = response.data || [];
    } else {
      knowledgeItems.value = [];
      error.value = response.message || '加载知识库失败';
    }
  } catch (err) {
    console.error('加载知识库失败:', err);
    error.value = '加载知识库失败，请稍后重试';
    knowledgeItems.value = [];
  } finally {
    loading.value = false;
  }
}

// 组件挂载时加载数据
onMounted(() => {
  loadKnowledgeItems();
});

// 搜索和过滤
const filteredKnowledgeItems = computed(() => {
  if (!searchQuery.value) return knowledgeItems.value;
  
  const query = searchQuery.value.toLowerCase();
  return knowledgeItems.value.filter(item => {
    return (
      item.title.toLowerCase().includes(query) ||
      item.description.toLowerCase().includes(query) ||
      (item.tags && item.tags.some(tag => tag.toLowerCase().includes(query)))
    );
  });
});

// 上传数据
const uploadData = ref({
  title: '',
  description: '',
  tags: ''
});

// 编辑数据
const editData = ref({
  title: '',
  description: '',
  tagsString: ''
});

// 方法
function searchKnowledge() {
  // 搜索已通过计算属性实现
}

function getFileIcon(fileType) {
  const icons = {
    pdf: '📄',
    doc: '📝',
    docx: '📝',
    txt: '📃',
    default: '📑'
  };
  
  return icons[fileType] || icons.default;
}

function formatDate(date) {
  // 添加日期验证
  if (!date) {
    return '未知日期';
  }
  
  try {
    const dateObj = new Date(date);
    
    // 检查日期是否有效
    if (isNaN(dateObj.getTime())) {
      return '无效日期';
    }
    
    return new Intl.DateTimeFormat('zh-CN', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    }).format(dateObj);
  } catch (error) {
    console.error('日期格式化错误:', error);
    return '日期错误';
  }
}

async function uploadFile() {
  try {
    const fileInputElement = fileInput.value;
    const file = fileInputElement?.files[0];
    
    if (!file) {
      alert('请选择要上传的文件');
      return;
    }
    
    // 检查登录状态
    const token = localStorage.getItem('accessToken');
    if (!token) {
      alert('请先登录后再上传文件');
      return;
    }
    
    loading.value = true;
    const formData = new FormData();
    formData.append('file', file);
    formData.append('title', uploadData.value.title || file.name);
    formData.append('content', uploadData.value.description);
    formData.append('categoryId', '2'); // 默认分类 - 建筑设计
    
    // 将标签字符串转换为数组
    const tags = uploadData.value.tags.split(',').map(tag => tag.trim()).filter(tag => tag);
    tags.forEach(tag => {
        formData.append('tags[]', tag);
    });

    const response = await createKnowledgeItem(formData);
    
    if (response.success) {
      // 重新加载知识库列表
      await loadKnowledgeItems();
      
      showUploadModal.value = false;
      
      // 重置表单
      uploadData.value = {
        title: '',
        description: '',
        tags: ''
      };
      
      // 清空文件输入
      if (fileInputElement) {
        fileInputElement.value = '';
      }
      
      alert('文件上传成功！');
    } else {
      alert('上传失败: ' + response.message);
    }
  } catch (err) {
    console.error('上传文件失败:', err);
    if (err.message && err.message.includes('访问令牌缺失')) {
      alert('登录已过期，请重新登录');
    } else {
      alert('上传文件失败: ' + (err.message || '未知错误'));
    }
  } finally {
    loading.value = false;
  }
}

function viewKnowledgeDetails(item) {
  selectedItem.value = item;
  showDetailsModal.value = true;
}

function editKnowledge(item) {
  selectedItem.value = item;
  // 确保tags是数组格式
  let tagsArray = [];
  if (item.tags) {
    if (Array.isArray(item.tags)) {
      tagsArray = item.tags.map(tag => tag.name || tag).filter(tag => tag);
    } else if (typeof item.tags === 'string') {
      tagsArray = item.tags.split(',').map(tag => tag.trim()).filter(tag => tag);
    } else if (typeof item.tags === 'object') {
      // 如果是对象数组，提取name字段
      tagsArray = item.tags.map(tag => tag.name || tag).filter(tag => tag);
    }
  }
  
  editData.value = {
    title: item.title,
    description: item.description || item.content || '',
    tagsString: tagsArray.join(', ')
  };
  showDetailsModal.value = false;
  showEditModal.value = true;
}

async function saveKnowledgeEdit() {
  try {
    loading.value = true;
    const updateData = {
      title: editData.value.title,
      content: editData.value.description,
      tags: editData.value.tagsString.split(',').map(tag => tag.trim()).filter(tag => tag)
    };
    
    await updateKnowledgeItem(selectedItem.value.id, updateData);
    
    // 重新加载知识库列表
    await loadKnowledgeItems();
    
    showEditModal.value = false;
    alert('知识更新成功！');
  } catch (err) {
    console.error('更新知识失败:', err);
    alert('更新知识失败: ' + err.message);
  } finally {
    loading.value = false;
  }
}

async function deleteKnowledge(item) {
  if (confirm(`确定要删除 "${item.title}" 吗？此操作不可撤销。`)) {
    try {
      loading.value = true;
      await deleteKnowledgeItem(item.id);
      
      // 重新加载知识库列表
      await loadKnowledgeItems();
      
      alert('知识已删除！');
    } catch (err) {
      console.error('删除知识失败:', err);
      alert('删除知识失败: ' + err.message);
    } finally {
      loading.value = false;
    }
  }
}

// 向量化相关方法
const startVectorization = async (itemId) => {
  try {
    await vectorizeKnowledgeItem(itemId);
    showSuccess('向量化处理已开始，请稍后查看进度');
    
    // 开始轮询进度
    pollVectorizationProgress(itemId);
  } catch (err) {
    showError('启动向量化失败: ' + err.message);
  }
};

const pollVectorizationProgress = async (itemId) => {
  const pollInterval = setInterval(async () => {
    try {
      const progress = await getVectorizationProgress(itemId);
      vectorizationProgress.value[itemId] = progress;
      
      // 如果完成或失败，停止轮询
      if (progress.completed > 0 && progress.processing === 0 && progress.pending === 0) {
        clearInterval(pollInterval);
        vectorizationStatus.value[itemId] = 'completed';
        showSuccess(`项目 ${itemId} 向量化完成`);
      } else if (progress.failed > 0) {
        clearInterval(pollInterval);
        vectorizationStatus.value[itemId] = 'failed';
        showError(`项目 ${itemId} 向量化失败`);
      }
    } catch (err) {
      console.error('获取向量化进度失败:', err);
      clearInterval(pollInterval);
    }
  }, 2000); // 每2秒查询一次进度
};

const getVectorizationStatus = (itemId) => {
  return vectorizationStatus.value[itemId] || 'not_started';
};

const getProgressPercentage = (itemId) => {
  const progress = vectorizationProgress.value[itemId];
  if (!progress || progress.total === 0) return 0;
  return Math.round((progress.completed / progress.total) * 100);
};

// 向量检索
const performVectorSearch = async () => {
  if (!vectorQuery.value.trim()) {
    showError('请输入搜索内容');
    return;
  }
  
  try {
    const results = await vectorSearch(vectorQuery.value.trim());
    vectorSearchResults.value = results;
    showVectorSearch.value = true;
  } catch (err) {
    showError('向量检索失败: ' + err.message);
  }
};

const closeVectorSearch = () => {
  showVectorSearch.value = false;
  vectorSearchResults.value = [];
  vectorQuery.value = '';
};

// 辅助方法
const getVectorizationStatusText = (status) => {
  const statusMap = {
    'not_started': '未开始',
    'processing': '处理中',
    'completed': '已完成',
    'failed': '失败'
  };
  return statusMap[status] || '未知';
};

const formatFileSize = (bytes) => {
  if (!bytes) return '0 B';
  const k = 1024;
  const sizes = ['B', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
};

// 新增方法
const handleFileSelect = (event) => {
  const fileInput = event.target;
  const file = fileInput.files[0];
  
  if (file) {
    uploadData.value.title = file.name;
    uploadData.value.description = '';
    uploadData.value.tags = '';
  }
};
</script>

<style scoped>
.knowledge-base-view {
  padding: 20px;
  height: 100%;
  overflow-y: auto;
}

.page-title {
  font-size: 2rem;
  margin-bottom: 0.5rem;
  color: var(--color-heading);
}

.page-description {
  color: var(--color-text-light);
  margin-bottom: 2rem;
}

.knowledge-container {
  background-color: var(--color-background-soft);
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
}

.action-bar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.search-box {
  display: flex;
  align-items: center;
  width: 300px;
}

.search-btn {
  background-color: var(--color-primary);
  color: white;
  border: none;
  border-radius: 0 4px 4px 0;
  padding: 8px 12px;
  cursor: pointer;
}

.knowledge-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 20px;
}

.knowledge-item {
  background-color: white;
  border-radius: 8px;
  padding: 15px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  display: flex;
  flex-direction: column;
  position: relative;
  transition: transform 0.2s, box-shadow 0.2s;
}

.knowledge-item:hover {
  transform: translateY(-3px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.item-icon {
  font-size: 2rem;
  margin-bottom: 10px;
}

.item-content {
  flex: 1;
}

.item-title {
  font-size: 1.2rem;
  margin-bottom: 8px;
  color: var(--color-heading);
}

.item-meta {
  display: flex;
  gap: 10px;
  font-size: 0.8rem;
  color: var(--color-text-light);
  margin-bottom: 10px;
}

.item-description {
  margin-bottom: 10px;
  font-size: 0.9rem;
  color: var(--color-text);
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.item-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 5px;
  margin-top: 10px;
}

.tag {
  background-color: var(--color-background-mute);
  color: var(--color-text);
  padding: 3px 8px;
  border-radius: 4px;
  font-size: 0.8rem;
}

.item-actions {
  display: flex;
  justify-content: flex-end;
  gap: 5px;
  margin-top: 10px;
}

.action-btn {
  background: none;
  border: none;
  font-size: 1.1rem;
  cursor: pointer;
  padding: 5px;
  border-radius: 4px;
  transition: background-color 0.2s;
}

.action-btn:hover {
  background-color: var(--color-background-mute);
}

/* 空状态 */
.empty-state {
  text-align: center;
  padding: 40px 20px;
  color: var(--color-text-light);
}

.empty-icon {
  font-size: 4rem;
  margin-bottom: 20px;
}

/* 模态框样式 */
.modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.modal-content {
  background-color: white;
  border-radius: 8px;
  width: 90%;
  max-width: 600px;
  max-height: 90vh;
  overflow-y: auto;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px 20px;
  border-bottom: 1px solid var(--color-border);
}

.modal-body {
  padding: 20px;
}

.modal-footer {
  padding: 15px 20px;
  border-top: 1px solid var(--color-border);
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}

.close-btn {
  background: none;
  border: none;
  font-size: 1.5rem;
  cursor: pointer;
  color: var(--color-text-light);
}

/* 详情模态框样式 */
.detail-header {
  display: flex;
  gap: 15px;
  margin-bottom: 20px;
}

.detail-icon {
  font-size: 3rem;
}

.detail-title-section {
  flex: 1;
}

.detail-title {
  margin-bottom: 5px;
  color: var(--color-heading);
}

.detail-meta {
  display: flex;
  gap: 15px;
  color: var(--color-text-light);
  font-size: 0.9rem;
}

.detail-section {
  margin-bottom: 20px;
}

.detail-section h4 {
  margin-bottom: 10px;
  color: var(--color-heading);
  font-size: 1.1rem;
}

.detail-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.content-preview {
  background-color: var(--color-background-mute);
  padding: 15px;
  border-radius: 4px;
  font-family: monospace;
  white-space: pre-line;
  max-height: 200px;
  overflow-y: auto;
}

.no-preview, .no-tags {
  color: var(--color-text-light);
  font-style: italic;
}

/* 表单样式 */
.form-group {
  margin-bottom: 15px;
}

.form-label {
  display: block;
  margin-bottom: 5px;
  font-weight: 500;
}

.file-input {
  width: 100%;
  padding: 8px;
  border: 1px solid var(--color-border);
  border-radius: 4px;
}

/* 响应式调整 */
@media (max-width: 768px) {
  .action-bar {
    flex-direction: column;
    gap: 10px;
    align-items: stretch;
  }
  
  .search-box {
    width: 100%;
  }
  
  .knowledge-grid {
    grid-template-columns: 1fr;
  }
}

/* 向量化相关样式 */
.vectorization-section {
  margin-top: 15px;
  padding-top: 15px;
  border-top: 1px solid #eee;
}

.vectorization-status {
  display: flex;
  align-items: center;
  gap: 10px;
  margin-bottom: 10px;
}

.status-label {
  font-size: 0.9em;
  color: #666;
}

.status-badge {
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 0.8em;
  font-weight: 500;
}

.status-badge.not_started {
  background-color: #f0f0f0;
  color: #666;
}

.status-badge.processing {
  background-color: #fff3cd;
  color: #856404;
}

.status-badge.completed {
  background-color: #d4edda;
  color: #155724;
}

.status-badge.failed {
  background-color: #f8d7da;
  color: #721c24;
}

.vectorization-progress {
  margin-bottom: 10px;
}

.progress-bar {
  width: 100%;
  height: 8px;
  background-color: #f0f0f0;
  border-radius: 4px;
  overflow: hidden;
  margin-bottom: 5px;
}

.progress-fill {
  height: 100%;
  background-color: #007bff;
  transition: width 0.3s ease;
}

.progress-text {
  font-size: 0.8em;
  color: #666;
}

/* 向量检索样式 */
.search-form {
  display: flex;
  gap: 10px;
  margin-bottom: 20px;
}

.search-form input {
  flex: 1;
}

.search-results {
  max-height: 400px;
  overflow-y: auto;
}

.search-result-item {
  border: 1px solid #eee;
  border-radius: 4px;
  padding: 15px;
  margin-bottom: 10px;
  background: white;
}

.result-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
}

.result-title {
  font-weight: 500;
  color: #333;
}

.result-similarity {
  font-size: 0.9em;
  color: #007bff;
  font-weight: 500;
}

.result-content {
  color: #666;
  line-height: 1.5;
  max-height: 100px;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 4;
  -webkit-box-orient: vertical;
}

/* 模态框样式 */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.modal-content {
  background: white;
  border-radius: 8px;
  width: 90%;
  max-width: 600px;
  max-height: 80vh;
  overflow: hidden;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px;
  border-bottom: 1px solid #eee;
}

.modal-header h3 {
  margin: 0;
}

.btn-close {
  background: none;
  border: none;
  font-size: 24px;
  cursor: pointer;
  color: #666;
}

.modal-body {
  padding: 20px;
  max-height: 60vh;
  overflow-y: auto;
}
</style>