<template>
  <div class="profile-view">
    <h1 class="page-title">个人资料</h1>
    <p class="page-description">管理您的账户信息</p>
    
    <div class="profile-container">
      <div class="profile-content">
        <div class="profile-section">
          <div class="section-header">
            <h2 class="section-title">基本信息</h2>
          </div>
          
          <div class="profile-form">
            <div class="avatar-section">
              <div class="avatar-container">
                <div class="avatar-preview" :style="avatarPreviewStyle">
                  <span v-if="!userForm.avatar && !avatarPreview">{{ userForm.username.charAt(0).toUpperCase() }}</span>
                </div>
                <div class="avatar-actions">
                  <label for="avatar-upload" class="avatar-upload-label">更换头像</label>
                  <input 
                    type="file" 
                    id="avatar-upload" 
                    class="avatar-upload-input" 
                    accept="image/*"
                    @change="handleAvatarUpload"
                  >
                </div>
              </div>
            </div>
            
            <div class="form-group">
              <label for="username">用户名</label>
              <input 
                type="text" 
                id="username" 
                v-model="userForm.username" 
                class="form-input"
                disabled
              >
              <div class="setting-description">用户名不可更改</div>
            </div>
            
            <div class="form-group">
              <label for="displayName">显示名称</label>
              <input 
                type="text" 
                id="displayName" 
                v-model="userForm.displayName" 
                class="form-input"
                placeholder="请输入显示名称"
              >
              <div v-if="formErrors.displayName" class="error-message">{{ formErrors.displayName }}</div>
            </div>
            
            <div class="form-actions">
              <button class="btn-primary" @click="saveProfile" :disabled="isSaving">
                {{ isSaving ? '保存中...' : '保存基本信息' }}
              </button>
            </div>
          </div>
        </div>
        
        <div class="profile-section">
          <div class="section-header">
            <h2 class="section-title">修改密码</h2>
          </div>
          
          <div class="profile-form">
            <div class="form-group">
              <label for="currentPassword">当前密码</label>
              <input 
                type="password" 
                id="currentPassword" 
                v-model="passwordForm.currentPassword" 
                class="form-input"
                placeholder="请输入当前密码"
              >
              <div v-if="formErrors.currentPassword" class="error-message">{{ formErrors.currentPassword }}</div>
            </div>
            
            <div class="form-group">
              <label for="newPassword">新密码</label>
              <input 
                type="password" 
                id="newPassword" 
                v-model="passwordForm.newPassword" 
                class="form-input"
                placeholder="请输入新密码"
              >
              <div v-if="formErrors.newPassword" class="error-message">{{ formErrors.newPassword }}</div>
            </div>
            
            <div class="form-group">
              <label for="confirmPassword">确认新密码</label>
              <input 
                type="password" 
                id="confirmPassword" 
                v-model="passwordForm.confirmPassword" 
                class="form-input"
                placeholder="请再次输入新密码"
              >
              <div v-if="formErrors.confirmPassword" class="error-message">{{ formErrors.confirmPassword }}</div>
            </div>
            
            <div class="form-actions">
              <button class="btn-primary" @click="changePassword" :disabled="isChangingPassword">
                {{ isChangingPassword ? '修改中...' : '修改密码' }}
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
    
    <!-- 成功提示 -->
    <div v-if="showSuccessMessage" class="success-message">
      {{ successMessage }}
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, computed } from 'vue';
import { useRouter } from 'vue-router';
import { userAPI, getCurrentUserFromCache, setCurrentUserToCache } from '../services/api.js';

const router = useRouter();

// 当前用户信息
const currentUser = ref(null);

// 表单数据
const userForm = reactive({
  username: '',
  displayName: '',
  avatar: ''
});

const passwordForm = reactive({
  currentPassword: '',
  newPassword: '',
  confirmPassword: ''
});

// 表单错误信息
const formErrors = reactive({
  displayName: '',
  currentPassword: '',
  newPassword: '',
  confirmPassword: ''
});

// 头像预览
const avatarPreview = ref('');

// 头像预览样式
const avatarPreviewStyle = computed(() => {
  if (avatarPreview.value) {
    return { backgroundImage: `url(${avatarPreview.value})` };
  } else if (userForm.avatar) {
    return { backgroundImage: `url(${userForm.avatar})` };
  }
  return {};
});

// 处理头像上传
function handleAvatarUpload(event) {
  const file = event.target.files[0];
  if (!file) return;
  
  // 验证文件类型
  if (!file.type.match('image.*')) {
    alert('请上传图片文件');
    return;
  }
  
  // 验证文件大小（最大 2MB）
  if (file.size > 2 * 1024 * 1024) {
    alert('图片大小不能超过 2MB');
    return;
  }
  
  // 创建文件阅读器
  const reader = new FileReader();
  reader.onload = (e) => {
    avatarPreview.value = e.target.result;
  };
  reader.readAsDataURL(file);
}

// 状态标志
const isSaving = ref(false);
const isChangingPassword = ref(false);
const showSuccessMessage = ref(false);
const successMessage = ref('');

// 初始化加载用户数据
onMounted(() => {
  loadUserData();
});

// 加载用户数据
async function loadUserData() {
  try {
    // 从API获取最新的用户信息
    const user = await userAPI.getProfile();
    currentUser.value = user;
    userForm.username = user.username;
    userForm.displayName = user.displayName;
    userForm.avatar = user.avatarUrl || '';
    
    // 更新缓存
    const userData = {
      id: user.id,
      username: user.username,
      displayName: user.displayName,
      isAdmin: user.role === 'admin',
      role: user.role,
      avatarUrl: user.avatarUrl
    };
    setCurrentUserToCache(userData);
  } catch (error) {
    console.error('获取用户信息失败:', error);
    // 如果API调用失败，尝试从缓存获取
    const cachedUser = getCurrentUserFromCache();
    if (cachedUser) {
      currentUser.value = cachedUser;
      userForm.username = cachedUser.username;
      userForm.displayName = cachedUser.displayName;
      userForm.avatar = cachedUser.avatarUrl || '';
    } else {
      // 如果没有登录，重定向到登录页面
      router.push('/login');
    }
  }
}

// 保存个人资料
async function saveProfile() {
  // 重置错误信息
  formErrors.displayName = '';
  
  // 验证表单
  if (!userForm.displayName.trim()) {
    formErrors.displayName = '请输入显示名称';
    return;
  }
  
  isSaving.value = true;
  
  try {
    const updateData = {
      displayName: userForm.displayName
    };
    
    // 如果有新头像，先上传头像
    if (avatarPreview.value) {
      // 这里需要将base64转换为文件对象
      const response = await fetch(avatarPreview.value);
      const blob = await response.blob();
      const file = new File([blob], 'avatar.jpg', { type: 'image/jpeg' });
      
      const avatarResponse = await userAPI.uploadAvatar(file);
      updateData.avatarUrl = avatarResponse.avatarUrl;
    }
    
    // 更新个人资料
    const updatedUser = await userAPI.updateProfile(updateData);
    
    // 更新本地状态
    currentUser.value = { ...currentUser.value, ...updatedUser };
    userForm.avatar = updatedUser.avatarUrl || '';
    
    // 更新缓存
    const userData = {
      id: updatedUser.id,
      username: updatedUser.username,
      displayName: updatedUser.displayName,
      isAdmin: updatedUser.role === 'admin',
      role: updatedUser.role,
      avatarUrl: updatedUser.avatarUrl
    };
    setCurrentUserToCache(userData);
    
    // 清除头像预览
    avatarPreview.value = '';
    
    // 显示成功消息
    showSuccessMessage.value = true;
    successMessage.value = '个人资料已更新';
    setTimeout(() => {
      showSuccessMessage.value = false;
    }, 3000);
  } catch (error) {
    console.error('保存个人资料失败:', error);
    showSuccessMessage.value = true;
    successMessage.value = '保存失败: ' + (error.message || '请重试');
    setTimeout(() => {
      showSuccessMessage.value = false;
    }, 3000);
  } finally {
    isSaving.value = false;
  }
}

// 修改密码
async function changePassword() {
  // 重置错误信息
  formErrors.currentPassword = '';
  formErrors.newPassword = '';
  formErrors.confirmPassword = '';
  
  // 验证表单
  let isValid = true;
  
  if (!passwordForm.currentPassword.trim()) {
    formErrors.currentPassword = '请输入当前密码';
    isValid = false;
  }
  
  if (!passwordForm.newPassword.trim()) {
    formErrors.newPassword = '请输入新密码';
    isValid = false;
  } else if (passwordForm.newPassword.length < 6) {
    formErrors.newPassword = '新密码至少6个字符';
    isValid = false;
  }
  
  if (!passwordForm.confirmPassword.trim()) {
    formErrors.confirmPassword = '请确认新密码';
    isValid = false;
  } else if (passwordForm.newPassword !== passwordForm.confirmPassword) {
    formErrors.confirmPassword = '两次输入的密码不一致';
    isValid = false;
  }
  
  if (!isValid) return;
  
  isChangingPassword.value = true;
  
  try {
    // 调用修改密码API
    await userAPI.updateProfile({
      currentPassword: passwordForm.currentPassword,
      newPassword: passwordForm.newPassword
    });
    
    // 清空表单
    passwordForm.currentPassword = '';
    passwordForm.newPassword = '';
    passwordForm.confirmPassword = '';
    
    // 显示成功消息
    showSuccessMessage.value = true;
    successMessage.value = '密码已修改';
    setTimeout(() => {
      showSuccessMessage.value = false;
    }, 3000);
  } catch (error) {
    console.error('修改密码失败:', error);
    if (error.message.includes('当前密码')) {
      formErrors.currentPassword = error.message;
    } else {
      showSuccessMessage.value = true;
      successMessage.value = '修改失败: ' + (error.message || '请重试');
      setTimeout(() => {
        showSuccessMessage.value = false;
      }, 3000);
    }
  } finally {
    isChangingPassword.value = false;
  }
}
</script>

<style scoped>
.profile-view {
  padding: 12px 20px;
  height: 100%;
}

.page-title {
  font-size: 1.3rem;
  margin-bottom: 0.2rem;
  color: var(--color-heading);
}

.page-description {
  color: var(--color-text-light);
  margin-bottom: 1rem;
  font-size: 0.85rem;
}

.profile-container {
  display: flex;
  background-color: var(--color-background-soft);
  border-radius: 8px;
  padding: 0;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  height: calc(100% - 60px);
}

.profile-content {
  flex: 1;
  padding: 30px;
  width: 100%;
  overflow-y: auto;
}

.profile-section {
  background-color: white;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
  margin-bottom: 20px;
}

.section-header {
  margin-bottom: 20px;
}

.section-title {
  font-size: 1.2rem;
  color: var(--color-heading);
  margin: 0;
}

.profile-form {
  max-width: 500px;
}

/* 头像部分样式 */
.avatar-section {
  margin-bottom: 30px;
}

.avatar-container {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.avatar-preview {
  width: 100px;
  height: 100px;
  border-radius: 50%;
  background-color: #4a6baf;
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
  font-size: 2.5rem;
  margin-bottom: 15px;
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  border: 3px solid white;
}

.avatar-actions {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.avatar-upload-label {
  background-color: #4a6baf;
  color: white;
  padding: 8px 16px;
  border-radius: 4px;
  cursor: pointer;
  transition: background-color 0.2s;
  font-size: 0.9rem;
  text-align: center;
}

.avatar-upload-label:hover {
  background-color: #3d5a8f;
}

.avatar-upload-input {
  display: none;
}

/* 表单样式 */
.form-group {
  margin-bottom: 20px;
}

.form-group label {
  display: block;
  margin-bottom: 8px;
  font-weight: 500;
  color: var(--color-heading);
}

.form-input {
  width: 100%;
  padding: 10px 12px;
  border: 1px solid var(--color-border);
  border-radius: 4px;
  font-size: 1rem;
  transition: border-color 0.2s;
}

.form-input:focus {
  border-color: var(--color-primary);
  outline: none;
}

.form-input:disabled {
  background-color: #f5f5f5;
  cursor: not-allowed;
}

.setting-description {
  font-size: 0.85rem;
  color: var(--color-text-light);
  margin-top: 5px;
}

.error-message {
  color: #dc3545;
  font-size: 0.85rem;
  margin-top: 5px;
}

.form-actions {
  margin-top: 30px;
}

/* 按钮样式 */
.btn-primary {
  background-color: #4a6baf;
  color: white;
  border: none;
  padding: 10px 16px;
  border-radius: 4px;
  cursor: pointer;
  transition: background-color 0.2s;
  font-size: 0.95rem;
}

.btn-primary:hover {
  background-color: #3d5a8f;
}

.btn-primary:disabled {
  background-color: #7a92c9;
  cursor: not-allowed;
}

/* 成功消息 */
.success-message {
  position: fixed;
  bottom: 20px;
  right: 20px;
  background-color: #28a745;
  color: white;
  padding: 12px 20px;
  border-radius: 4px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  z-index: 1000;
  animation: fadeIn 0.3s, fadeOut 0.3s 2.7s;
}

@keyframes fadeIn {
  from { opacity: 0; transform: translateY(10px); }
  to { opacity: 1; transform: translateY(0); }
}

@keyframes fadeOut {
  from { opacity: 1; transform: translateY(0); }
  to { opacity: 0; transform: translateY(10px); }
}

/* 响应式调整 */
@media (max-width: 768px) {
  .profile-content {
    padding: 20px;
  }
  
  .profile-section {
    padding: 15px;
  }
}
</style>