import { ref } from 'vue';

// 分类数据
export const categories = [
  { id: 'all', name: '全部' },
  { id: 'customer', name: '客户画像' },
  { id: 'opinion', name: '设计观点' },
  { id: 'strategy', name: '设计策略' }
];

// 脚本库数据
export const scripts = ref([
  {
    id: 1,
    name: '高端住宅客户画像',
    type: '客户画像',
    categoryId: 'customer',
    description: '针对高端住宅项目的目标客户群体特征描述',
    createdAt: new Date(2023, 5, 15),
    tags: ['客户画像', '高端住宅', '目标群体'],
    content: `请帮我分析高端住宅项目的目标客户群体特征：

1. 人口统计特征（年龄、收入、职业、家庭结构）
2. 生活方式和价值观
3. 购房动机和决策因素
4. 设计偏好和审美倾向
5. 对空间功能的特殊需求

请结合当前市场趋势和消费者行为变化，提供详细分析。`,
    parameters: []
  },
  {
    id: 2,
    name: '可持续设计核心理念',
    type: '设计观点',
    categoryId: 'opinion',
    description: '关于可持续设计的核心理念和价值观表述',
    createdAt: new Date(2023, 4, 22),
    tags: ['可持续设计', '设计理念', '环保'],
    content: `请帮我阐述关于可持续设计的核心理念：

1. 可持续设计的定义和重要性
2. 可持续设计的基本原则（材料选择、能源效率、废物管理等）
3. 可持续设计如何平衡美学与环保需求
4. 成功的可持续设计案例分析
5. 可持续设计面临的挑战和未来发展方向

请提供深入的分析和见解，以帮助我形成自己的设计观点。`,
    parameters: []
  },
  {
    id: 3,
    name: '商业空间设计策略',
    type: '设计策略',
    categoryId: 'strategy',
    description: '针对不同类型商业空间的设计策略和方法论',
    createdAt: new Date(2023, 6, 8),
    tags: ['商业空间', '设计策略', '用户体验'],
    content: `请为我提供商业空间设计的策略框架：

1. 不同类型商业空间（零售、餐饮、办公等）的设计重点
2. 如何通过空间设计提升品牌形象和用户体验
3. 空间流线和功能分区的优化方法
4. 照明、材料和色彩策略
5. 如何平衡美学、功能性和商业目标
6. 评估设计成功的关键指标

请结合实际案例和最新的设计趋势进行分析。`,
    parameters: []
  },
  {
    id: 4,
    name: '建筑风格提示词生成器',
    type: '生成提示词',
    categoryId: 'prompt',
    description: '根据输入的建筑类型和风格偏好，生成详细的建筑设计提示词',
    createdAt: new Date(2023, 5, 15),
    tags: ['建筑', '提示词', '风格'],
    content: `// 建筑风格提示词生成器
function generatePrompt(params) {
  const { buildingType, style, materials, lighting } = params;
  
  let prompt = \`\${buildingType} in \${style} architectural style\`;
  
  if (materials) {
    prompt += \`, featuring \${materials} materials\`;
  }
  
  if (lighting) {
    prompt += \`, with \${lighting} lighting\`;
  }
  
  prompt += \`, professional architectural visualization, detailed, realistic, high quality rendering\`;
  
  return prompt;
}`,
    parameters: [
      { name: '建筑类型', type: 'text', default: '住宅' },
      { name: '风格', type: 'select', default: '现代,北欧,工业,极简主义,传统' },
      { name: '材料', type: 'text', default: '混凝土和木材' },
      { name: '光照', type: 'text', default: '自然光' }
    ]
  },
  {
    id: 5,
    name: '室内设计色彩方案生成器',
    type: '生成提示词',
    categoryId: 'prompt',
    description: '根据输入的房间类型和风格，生成协调的室内设计色彩方案',
    createdAt: new Date(2023, 4, 22),
    tags: ['室内设计', '色彩', '方案'],
    content: `// 室内设计色彩方案生成器
function generateColorScheme(params) {
  const { roomType, style, mood, season } = params;
  
  // 色彩库
  const colorPalettes = {
    modern: ['#F5F5F5', '#333333', '#0077CC', '#FFFFFF'],
    scandinavian: ['#FFFFFF', '#E8E8E8', '#F9F9F9', '#CCCCCC'],
    industrial: ['#333333', '#666666', '#999999', '#CCCCCC'],
    bohemian: ['#FF6B6B', '#4ECDC4', '#FFE66D', '#1A535C'],
    minimalist: ['#FFFFFF', '#F5F5F5', '#E0E0E0', '#BDBDBD']
  };
  
  // 根据风格选择基础色板
  let basePalette = colorPalettes[style.toLowerCase()] || colorPalettes.modern;
  
  // 根据季节调整色板
  if (season === '夏季') {
    basePalette = basePalette.map(color => lightenColor(color, 10));
  } else if (season === '冬季') {
    basePalette = basePalette.map(color => darkenColor(color, 10));
  }
  
  // 根据情绪调整色板
  if (mood === '活力') {
    basePalette.push('#FF4500');
  } else if (mood === '平静') {
    basePalette.push('#4682B4');
  } else if (mood === '温馨') {
    basePalette.push('#FFD700');
  }
  
  return {
    roomType,
    style,
    primaryColor: basePalette[0],
    secondaryColor: basePalette[1],
    accentColor: basePalette[2],
    backgroundColor: basePalette[3],
    recommendation: \`为您的\${roomType}推荐\${style}风格的色彩方案，主色调\${basePalette[0]}，辅助色\${basePalette[1]}，点缀色\${basePalette[2]}，背景色\${basePalette[3]}。\`
  };
}`,
    parameters: [
      { name: '房间类型', type: 'select', default: '客厅,卧室,厨房,浴室,书房' },
      { name: '风格', type: 'select', default: 'modern,scandinavian,industrial,bohemian,minimalist' },
      { name: '情绪', type: 'select', default: '活力,平静,温馨,专注' },
      { name: '季节', type: 'select', default: '春季,夏季,秋季,冬季' }
    ]
  },
  {
    id: 6,
    name: '批量图像重命名工具',
    type: '批量操作',
    categoryId: 'batch',
    description: '根据指定的命名规则，批量重命名图像文件',
    createdAt: new Date(2023, 6, 8),
    tags: ['批量', '重命名', '图像'],
    content: `// 批量图像重命名工具
function batchRename(params) {
  const { prefix, startNumber, dateFormat, includeOriginalName } = params;
  
  // 在实际应用中，这里会处理选定的文件
  // 这里仅作为示例返回重命名预览
  
  const today = new Date();
  const dateStr = formatDate(today, dateFormat);
  
  const results = [];
  for (let i = 0; i < 5; i++) { // 假设有5个文件
    const originalName = 'image_' + (i + 1) + '.jpg';
    let newName = prefix;
    
    if (dateFormat) {
      newName += '_' + dateStr;
    }
    
    newName += '_' + (startNumber + i);
    
    if (includeOriginalName) {
      const origNameWithoutExt = originalName.split('.')[0];
      newName += '_' + origNameWithoutExt;
    }
    
    newName += '.jpg';
    
    results.push({
      original: originalName,
      renamed: newName
    });
  }
  
  return results;
}

function formatDate(date, format) {
  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, '0');
  const day = String(date.getDate()).padStart(2, '0');
  
  if (format === 'YYYYMMDD') {
    return \`\${year}\${month}\${day}\`;
  } else if (format === 'YYYY-MM-DD') {
    return \`\${year}-\${month}-\${day}\`;
  } else {
    return \`\${year}\${month}\${day}\`;
  }
}`,
    parameters: [
      { name: '前缀', type: 'text', default: 'Project' },
      { name: '起始编号', type: 'number', default: '1' },
      { name: '日期格式', type: 'select', default: 'YYYYMMDD,YYYY-MM-DD,无' },
      { name: '包含原文件名', type: 'boolean', default: 'true' }
    ]
  },
  {
    id: 7,
    name: '设计项目时间估算器',
    type: '实用工具',
    categoryId: 'utility',
    description: '根据项目类型和复杂度，估算设计项目所需的时间',
    createdAt: new Date(2023, 3, 17),
    tags: ['时间', '估算', '项目管理'],
    content: `// 设计项目时间估算器
function estimateProjectTime(params) {
  const { projectType, complexity, revisions, teamSize } = params;
  
  // 基础时间（小时）
  const baseTimeMap = {
    'logo': 8,
    'website': 40,
    'app': 60,
    'branding': 30,
    'illustration': 15,
    'interior': 50,
    'architecture': 80
  };
  
  // 复杂度系数
  const complexityFactor = {
    'low': 0.8,
    'medium': 1,
    'high': 1.5,
    'very high': 2
  };
  
  // 修改轮次系数
  const revisionFactor = 1 + (revisions * 0.1);
  
  // 团队规模效率
  let teamEfficiency = 1;
  if (teamSize > 1) {
    // 团队协作有一定效率提升，但不是线性的
    teamEfficiency = 1 / (1 + (0.8 * (teamSize - 1) / teamSize));
  }
  
  const baseTime = baseTimeMap[projectType] || 40;
  const adjustedTime = baseTime * complexityFactor[complexity] * revisionFactor * teamEfficiency;
  
  // 计算工作日（假设每天8小时）
  const workDays = Math.ceil(adjustedTime / 8);
  
  return {
    projectType,
    complexity,
    estimatedHours: Math.round(adjustedTime),
    estimatedDays: workDays,
    estimatedWeeks: Math.ceil(workDays / 5),
    teamSize,
    summary: \`估计您的\${projectType}项目需要约\${Math.round(adjustedTime)}小时（\${workDays}个工作日）完成。\`
  };
}`,
    parameters: [
      { name: '项目类型', type: 'select', default: 'logo,website,app,branding,illustration,interior,architecture' },
      { name: '复杂度', type: 'select', default: 'low,medium,high,very high' },
      { name: '修改轮次', type: 'number', default: '2' },
      { name: '团队规模', type: 'number', default: '1' }
    ]
  }
]);

// 获取分类名称的函数
export function getCategoryName(categoryId) {
  const category = categories.find(c => c.id === categoryId);
  return category ? category.name : categoryId;
}