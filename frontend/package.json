{"name": "aide-frontend", "version": "0.1.0", "private": true, "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "lint": "eslint . --ext .vue,.js,.jsx,.cjs,.mjs --fix --ignore-path .gitignore"}, "dependencies": {"element-plus": "^2.10.2", "eventsource-polyfill": "^0.9.6", "highlight.js": "^11.11.1", "marked": "^15.0.12", "vue": "^3.3.4", "vue-router": "^4.2.4"}, "devDependencies": {"@vitejs/plugin-vue": "^4.3.1", "eslint": "^8.46.0", "eslint-plugin-vue": "^9.16.1", "vite": "^4.4.9"}}