[{"id": "1751262706320", "timestamp": "2025-06-30T05:51:46.320Z", "prompt": "a beautiful sunset over mountains", "options": {"aspect_ratio": "16:9", "quality": "1"}, "error": "Midjourney配置不完整，请先配置Discord Token和频道ID", "type": "generate", "success": false}, {"id": "1751352728514", "timestamp": "2025-07-01T06:52:08.514Z", "prompt": "a beautiful sunset over mountains", "options": {"aspect_ratio": "16:9", "quality": "1"}, "error": "未找到生成结果", "type": "generate", "success": false}, {"id": "1751356697132", "timestamp": "2025-07-01T07:58:17.132Z", "prompt": "a beautiful sunset over mountains", "options": {"aspect_ratio": "16:9", "quality": "1"}, "result": {"success": true, "message_id": "demo_1751356697_1577", "image_url": "https://via.placeholder.com/1024x1024/4a90e2/ffffff?text=a+beautiful+sunset+o", "content": "**a beautiful sunset over mountains** - Image #1", "attachments": [{"id": "attachment_291064", "filename": "midjourney_demo_1751356697.png", "url": "https://via.placeholder.com/1024x1024/4a90e2/ffffff?text=a+beautiful+sunset+o", "width": 1024, "height": 1024, "size": 591319}], "prompt": "a beautiful sunset over mountains", "command": "/imagine prompt: a beautiful sunset over mountains", "options": {"aspect_ratio": "16:9", "quality": "1"}, "generation_time": 5, "demo_mode": true, "timestamp": "2025-07-01T07:58:17.131Z", "mode": "demo"}, "type": "generate"}, {"id": "1751358724359", "timestamp": "2025-07-01T08:32:04.359Z", "prompt": "a beautiful landscape with mountains and sunset", "options": {"aspect_ratio": "16:9", "quality": "1"}, "result": {"success": true, "message_id": "demo_1751358724_9312", "image_url": "https://via.placeholder.com/1024x1024/4a90e2/ffffff?text=a+beautiful+landscap", "content": "**a beautiful landscape with mountains and sunset** - Image #4", "attachments": [{"id": "attachment_517410", "filename": "midjourney_demo_1751358724.png", "url": "https://via.placeholder.com/1024x1024/4a90e2/ffffff?text=a+beautiful+landscap", "width": 1024, "height": 1024, "size": 1541259}], "prompt": "a beautiful landscape with mountains and sunset", "command": "/imagine prompt: a beautiful landscape with mountains and sunset", "options": {"aspect_ratio": "16:9", "quality": "1"}, "generation_time": 5, "demo_mode": true, "timestamp": "2025-07-01T08:32:04.358Z", "mode": "demo"}, "type": "generate"}, {"id": "1751358777146", "timestamp": "2025-07-01T08:32:57.146Z", "prompt": "a girl", "options": {}, "result": {"success": true, "message_id": "demo_1751358777_7331", "image_url": "https://picsum.photos/1024/1024?random=5353", "content": "**a girl** - Image #4", "attachments": [{"id": "attachment_476827", "filename": "midjourney_demo_1751358777.png", "url": "https://picsum.photos/1024/1024?random=5353", "width": 1024, "height": 1024, "size": 833859}], "prompt": "a girl", "command": "/imagine prompt: a girl", "options": {}, "generation_time": 5, "demo_mode": true, "timestamp": "2025-07-01T08:32:57.146Z", "mode": "demo"}, "type": "generate"}, {"id": "1751437908493", "timestamp": "2025-07-02T06:31:48.493Z", "prompt": "a boy", "options": {}, "error": "未找到生成结果", "type": "generate", "success": false}, {"id": "1751439565165", "timestamp": "2025-07-02T06:59:25.165Z", "prompt": "beautiful sunset", "options": {"aspect_ratio": "16:9"}, "result": {"success": true, "message_id": "fallback_1751439565_7518", "image_url": "https://picsum.photos/1024/576?random=5243", "content": "**beautiful sunset** - Fallback Image", "attachments": [{"id": "attachment_836678", "filename": "midjourney_fallback_1751439565.png", "url": "https://picsum.photos/1024/576?random=5243", "width": 1024, "height": 576, "size": 1052045}], "prompt": "beautiful sunset", "command": "/imagine prompt: beautiful sunset", "options": {"aspect_ratio": "16:9"}, "generation_time": 3, "fallback_mode": true, "note": "此图片由备用服务生成，非真实Midjourney结果", "timestamp": "2025-07-02T06:59:25.164Z"}, "type": "generate"}, {"id": "1751440267913", "timestamp": "2025-07-02T07:11:07.913Z", "prompt": "dogg", "options": {}, "result": {"success": true, "message_id": "fallback_1751440267_7941", "image_url": "https://source.unsplash.com/1024x1024/?dogg", "content": "**dogg** - Fallback Image", "attachments": [{"id": "attachment_209708", "filename": "midjourney_fallback_1751440267.png", "url": "https://source.unsplash.com/1024x1024/?dogg", "width": 1024, "height": 1024, "size": 1563447}], "prompt": "dogg", "command": "/imagine prompt: dogg", "options": {}, "generation_time": 5, "fallback_mode": true, "note": "此图片由备用服务生成，非真实Midjourney结果", "timestamp": "2025-07-02T07:11:07.912Z"}, "type": "generate"}, {"id": "1751440356496", "timestamp": "2025-07-02T07:12:36.496Z", "prompt": "boy", "options": {}, "result": {"success": true, "message_id": "fallback_1751440356_1915", "image_url": "https://picsum.photos/1024/1024?random=2097", "content": "**boy** - Fallback Image", "attachments": [{"id": "attachment_724899", "filename": "midjourney_fallback_1751440356.png", "url": "https://picsum.photos/1024/1024?random=2097", "width": 1024, "height": 1024, "size": 894901}], "prompt": "boy", "command": "/imagine prompt: boy", "options": {}, "generation_time": 2, "fallback_mode": true, "note": "此图片由备用服务生成，非真实Midjourney结果", "timestamp": "2025-07-02T07:12:36.496Z"}, "type": "generate"}, {"id": "1751440728701", "timestamp": "2025-07-02T07:18:48.701Z", "prompt": "test image", "options": {}, "error": "Midjourney生成失败: python3: can't open file '/opt/scaia/backend/scripts/midjourney_fallback.py': [Errno 2] No such file or directory\n", "type": "generate", "success": false}]