<template>
  <div class="prompt-library">
    <div class="page-header">
      <h1>提示词库</h1>
      <p>为生图功能提供结构化的提示词内容</p>
    </div>

    <div class="prompt-categories">
      <div class="category-tabs">
        <button 
          v-for="category in categories" 
          :key="category.id"
          :class="['tab-button', { active: activeCategory === category.id }]"
          @click="activeCategory = category.id"
        >
          <span class="tab-icon">{{ category.icon }}</span>
          <span class="tab-text">{{ category.name }}</span>
        </button>
      </div>

      <div class="category-content">
        <div class="content-header">
          <h2>{{ getCurrentCategory() ? getCurrentCategory().name : '' }}</h2>
          <button class="add-prompt-btn" @click="showAddDialog = true">
            <span>+</span> 添加提示词
          </button>
        </div>

        <div class="prompt-list">
          <div 
            v-for="prompt in getCurrentPrompts()" 
            :key="prompt.id"
            class="prompt-item"
          >
            <div class="prompt-header">
              <div class="prompt-text">{{ prompt.title }}</div>
              <div class="prompt-actions">
                <button class="edit-btn" @click="editPrompt(prompt)">
                  <span>✏️</span>
                </button>
                <button class="delete-btn" @click="deletePrompt(prompt.id)">
                  <span>🗑️</span>
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 添加/编辑提示词对话框 -->
    <div v-if="showAddDialog || editingPrompt" class="dialog-overlay" @click="closeDialog">
      <div class="dialog" @click.stop>
        <div class="dialog-header">
          <h3>{{ editingPrompt ? '编辑提示词' : '添加提示词' }}</h3>
          <button class="close-btn" @click="closeDialog">×</button>
        </div>
        <div class="dialog-content">
          <div class="form-group">
            <label>提示词（英文）</label>
            <input v-model="promptForm.promptEn" type="text" placeholder="例如：Modern Architecture">
          </div>
          <div class="form-group">
            <label>提示词（中文）</label>
            <input v-model="promptForm.promptCn" type="text" placeholder="例如：现代主义建筑">
          </div>
          <div class="form-group">
            <label>分类</label>
            <select v-model="promptForm.category">
              <option v-for="category in categories" :key="category.id" :value="category.id">
                {{ category.name }}
              </option>
            </select>
          </div>
        </div>
        <div class="dialog-actions">
          <button class="cancel-btn" @click="closeDialog">取消</button>
          <button class="save-btn" @click="savePrompt">保存</button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, watch } from 'vue'
import { fetchPrompts, fetchPromptCategories, promptAPI } from '@/services/api'

const activeCategory = ref('')
const showAddDialog = ref(false)
const editingPrompt = ref(null)

// 分类定义（改为动态获取）
const categories = ref([])

// 提示词表单
const promptForm = reactive({
  promptEn: '',
  promptCn: '',
  category: ''
})

// 提示词数据
const prompts = ref({})

async function loadCategories() {
  const res = await fetchPromptCategories()
  categories.value = res.data
  // 默认选中第一个分类
  if (categories.value.length > 0) {
    activeCategory.value = categories.value[0].id
  }
}

async function loadPrompts(categoryId) {
  const res = await fetchPrompts(categoryId)
  prompts.value[categoryId] = res.data // 根据你的后端返回结构调整
}

onMounted(() => {
  loadCategories()
})

// 监听分类切换，自动加载数据
watch(activeCategory, (newVal) => {
  if (newVal) loadPrompts(newVal)
})

// 获取当前分类
const getCurrentCategory = () => {
  if (!categories.value || !Array.isArray(categories.value) || !activeCategory.value) return null;
  return categories.value.find(cat => cat.id === activeCategory.value) || null;
}

// 获取当前分类的提示词
const getCurrentPrompts = () => {
  return prompts.value[activeCategory.value] || []
}

// 编辑提示词
const editPrompt = (prompt) => {
  editingPrompt.value = prompt
  
  // 拆分提示词为英文和中文部分
  const parts = (prompt.title || '').split(' - ')
  promptForm.promptEn = parts[0] || ''
  promptForm.promptCn = parts[1] || ''
  
  promptForm.category = activeCategory.value
}

// 删除提示词
const deletePrompt = async (id) => {
  if (confirm('确定要删除这个提示词吗？')) {
    try {
      await promptAPI.deletePrompt(id);
      // 重新加载当前分类的提示词
      await loadPrompts(activeCategory.value);
    } catch (error) {
      console.error('删除提示词失败:', error);
      alert('删除失败: ' + error.message);
    }
  }
}

// 保存提示词
const savePrompt = async () => {
  if (!promptForm.promptEn && !promptForm.promptCn) {
    alert('请填写英文和中文提示词内容');
    return;
  }

  const promptData = {
    title: `${promptForm.promptEn} - ${promptForm.promptCn}`,
    categoryId: promptForm.category,
    // 你可以根据需要添加其他字段，例如 description, content, isPublic
    description: '', 
    content: `${promptForm.promptEn} - ${promptForm.promptCn}`,
    isPublic: true
  };

  try {
    if (editingPrompt.value) {
      // 编辑模式
      await promptAPI.updatePrompt(editingPrompt.value.id, promptData);
    } else {
      // 添加模式
      await promptAPI.createPrompt(promptData);
    }
    // 成功后重新加载数据并关闭对话框
    await loadPrompts(activeCategory.value);
    closeDialog();
  } catch (error) {
    console.error('保存提示词失败:', error);
    alert('保存失败: ' + error.message);
  }
}

// 关闭对话框
const closeDialog = () => {
  showAddDialog.value = false
  editingPrompt.value = null
  promptForm.promptEn = ''
  promptForm.promptCn = ''
  promptForm.category = ''
}
</script>

<style scoped>
.prompt-library {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background-color: #f8f9fa;
}

.page-header {
  background: white;
  padding: 2rem;
  border-bottom: 1px solid #e9ecef;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.page-header h1 {
  margin: 0 0 0.5rem 0;
  color: #2c3e50;
  font-size: 2rem;
}

.page-header p {
  margin: 0;
  color: #6c757d;
  font-size: 1.1rem;
}

.prompt-categories {
  flex: 1;
  display: flex;
  overflow: hidden;
}

.category-tabs {
  width: 200px;
  background: white;
  border-right: 1px solid #e9ecef;
  display: flex;
  flex-direction: column;
}

.tab-button {
  display: flex;
  align-items: center;
  padding: 1rem 1.5rem;
  border: none;
  background: none;
  text-align: left;
  cursor: pointer;
  transition: all 0.2s;
  border-left: 3px solid transparent;
}

.tab-button:hover {
  background-color: #f8f9fa;
}

.tab-button.active {
  background-color: #e3f2fd;
  border-left-color: #2196f3;
  color: #1976d2;
}

.tab-icon {
  margin-right: 0.8rem;
  font-size: 1.2rem;
}

.tab-text {
  font-size: 1rem;
  font-weight: 500;
}

.category-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  background: white;
}

.content-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1.5rem 2rem;
  border-bottom: 1px solid #e9ecef;
}

.content-header h2 {
  margin: 0;
  color: #2c3e50;
  font-size: 1.5rem;
}

.add-prompt-btn {
  background: #28a745;
  color: white;
  border: none;
  padding: 0.5rem 1rem;
  border-radius: 4px;
  cursor: pointer;
  font-size: 0.9rem;
  transition: background-color 0.2s;
}

.add-prompt-btn:hover {
  background: #218838;
}

.prompt-list {
  flex: 1;
  overflow-y: auto;
  padding: 1rem 2rem;
}

.prompt-item {
  background: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 8px;
  padding: 1rem 1.5rem;
  margin-bottom: 0.75rem;
  transition: box-shadow 0.2s;
}

.prompt-item:hover {
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.prompt-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.prompt-text {
  flex: 1;
  color: #2c3e50;
  font-size: 1rem;
  font-weight: 500;
}

.prompt-actions {
  display: flex;
  gap: 0.5rem;
}

.edit-btn, .delete-btn {
  background: none;
  border: none;
  cursor: pointer;
  padding: 0.25rem;
  border-radius: 4px;
  transition: background-color 0.2s;
}

.edit-btn:hover {
  background: #e3f2fd;
}

.delete-btn:hover {
  background: #ffebee;
}

/* 对话框样式 */
.dialog-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0,0,0,0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.dialog {
  background: white;
  border-radius: 8px;
  width: 90%;
  max-width: 500px;
  max-height: 80vh;
  overflow: hidden;
  box-shadow: 0 4px 20px rgba(0,0,0,0.3);
}

.dialog-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1.5rem;
  border-bottom: 1px solid #e9ecef;
}

.dialog-header h3 {
  margin: 0;
  color: #2c3e50;
}

.close-btn {
  background: none;
  border: none;
  font-size: 1.5rem;
  cursor: pointer;
  color: #6c757d;
}

.dialog-content {
  padding: 1.5rem;
  max-height: 400px;
  overflow-y: auto;
}

.form-group {
  margin-bottom: 1rem;
}

.form-group label {
  display: block;
  margin-bottom: 0.5rem;
  font-weight: 500;
  color: #2c3e50;
}

.form-group input,
.form-group textarea,
.form-group select {
  width: 100%;
  padding: 0.75rem;
  border: 1px solid #ced4da;
  border-radius: 4px;
  font-size: 0.9rem;
}

.form-group textarea {
  resize: vertical;
  min-height: 100px;
}

.dialog-actions {
  display: flex;
  justify-content: flex-end;
  gap: 1rem;
  padding: 1.5rem;
  border-top: 1px solid #e9ecef;
}

.cancel-btn {
  background: #6c757d;
  color: white;
  border: none;
  padding: 0.5rem 1rem;
  border-radius: 4px;
  cursor: pointer;
}

.save-btn {
  background: #007bff;
  color: white;
  border: none;
  padding: 0.5rem 1rem;
  border-radius: 4px;
  cursor: pointer;
}

.cancel-btn:hover {
  background: #5a6268;
}

.save-btn:hover {
  background: #0056b3;
}
</style>