[{"id": "1751262706320", "timestamp": "2025-06-30T05:51:46.320Z", "prompt": "a beautiful sunset over mountains", "options": {"aspect_ratio": "16:9", "quality": "1"}, "error": "Midjourney配置不完整，请先配置Discord Token和频道ID", "type": "generate", "success": false}, {"id": "1751352728514", "timestamp": "2025-07-01T06:52:08.514Z", "prompt": "a beautiful sunset over mountains", "options": {"aspect_ratio": "16:9", "quality": "1"}, "error": "未找到生成结果", "type": "generate", "success": false}, {"id": "1751437908493", "timestamp": "2025-07-02T06:31:48.493Z", "prompt": "a boy", "options": {}, "error": "未找到生成结果", "type": "generate", "success": false}, {"id": "1751440728701", "timestamp": "2025-07-02T07:18:48.701Z", "prompt": "test image", "options": {}, "error": "Midjourney生成失败: python3: can't open file '/opt/scaia/backend/scripts/midjourney_fallback.py': [Errno 2] No such file or directory\n", "type": "generate", "success": false}, {"id": "1751442291124", "timestamp": "2025-07-02T07:44:51.124Z", "prompt": "a boy", "options": {}, "error": "Discord API连接失败，请检查网络连接", "type": "generate", "success": false}, {"id": "1751445134984", "timestamp": "2025-07-02T08:32:14.984Z", "prompt": "a boy", "options": {}, "error": "Discord API连接失败，请检查网络连接", "type": "generate", "success": false}, {"id": "1751445460487", "timestamp": "2025-07-02T08:37:40.487Z", "prompt": "test connection", "options": {}, "error": "未找到生成结果", "type": "generate", "success": false}]