/**
 * AIDE Backend API Server
 * 主应用程序入口文件
 * 
 * 功能说明：
 * - 配置Express服务器
 * - 设置中间件
 * - 配置路由
 * - 错误处理
 * - 数据库连接
 */

const express = require('express');
const cors = require('cors');
const helmet = require('helmet');
const morgan = require('morgan');
const compression = require('compression');
const rateLimit = require('express-rate-limit');
const path = require('path');
require('dotenv').config();

// 导入路由模块
const authRoutes = require('./routes/auth');
const userRoutes = require('./routes/users');
const chatRoutes = require('./routes/chat');
const knowledgeRoutes = require('./routes/knowledge');
const masterRoutes = require('./routes/masters');
const imageRoutes = require('./routes/images');
const promptRoutes = require('./routes/prompts');
const scriptRoutes = require('./routes/scripts');
const settingsRoutes = require('./routes/settings');
const featuresRoutes = require('./routes/features');
const modelsRoutes = require('./routes/models');
const ragRoutes = require('./routes/rag');
const localRagRoutes = require('./routes/localRag');
const midjourneyRoutes = require('./routes/midjourney');

// 导入错误处理中间件
const { notFoundHandler, errorHandler } = require('./middleware/errorHandler');

// 导入数据库连接
const db = require('./config/database');

// 创建Express应用实例
const app = express();

// 设置端口号，默认为3000
const PORT = process.env.PORT || 3000;

// ==================== 中间件配置 ====================

// 安全中间件 - 设置各种HTTP头部以增强安全性
app.use(helmet({
  crossOriginEmbedderPolicy: false, // 允许跨域嵌入
  contentSecurityPolicy: {
    directives: {
      defaultSrc: ["'self'"],
      styleSrc: ["'self'", "'unsafe-inline'"],
      scriptSrc: ["'self'"],
      imgSrc: ["'self'", "data:", "https:"],
    },
  },
}));

// CORS配置 - 允许前端跨域访问
const allowedOrigins = [
  'http://localhost:3001',
  'http://127.0.0.1:3001',
  'http://**************:3001',
  'http://localhost:5173',
  'http://127.0.0.1:5173',
];

// 从环境变量添加更多IP地址
if (process.env.FRONTEND_URLS) {
  const envUrls = process.env.FRONTEND_URLS.split(',').map(url => url.trim());
  allowedOrigins.push(...envUrls);
}

// 如果设置了单个FRONTEND_URL，也添加进去
if (process.env.FRONTEND_URL) {
  allowedOrigins.push(process.env.FRONTEND_URL);
}

app.use(cors({
  origin: function (origin, callback) {
    // 允许没有origin的请求（比如同源请求）
    if (!origin) return callback(null, true);
    
    if (allowedOrigins.indexOf(origin) !== -1) {
      callback(null, true);
    } else {
      console.log('CORS blocked origin:', origin);
      callback(new Error('Not allowed by CORS'));
    }
  },
  credentials: true, // 允许携带凭证
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'Authorization', 'Cache-Control']
}));

// 请求日志中间件
app.use(morgan('combined'));

// 响应压缩中间件
app.use(compression());

// 请求体解析中间件
app.use(express.json({ limit: '10mb' })); // 解析JSON格式的请求体
app.use(express.urlencoded({ extended: true, limit: '10mb' })); // 解析URL编码的请求体

// 速率限制中间件 - 防止API滥用
const limiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15分钟
  max: 1000, // 每个IP在窗口期内最多1000次请求
  message: {
    error: '请求过于频繁，请稍后再试',
    code: 'RATE_LIMIT_EXCEEDED'
  },
  standardHeaders: true, // 返回速率限制信息在 `RateLimit-*` 头部
  legacyHeaders: false, // 禁用 `X-RateLimit-*` 头部
});
app.use('/api/', limiter);

// 配置静态文件服务 - 指向前端public目录
app.use('/uploads', express.static(path.join(__dirname, '../frontend/public/uploads')));

// ==================== 路由配置 ====================

// API根路径健康检查
app.get('/api/health', (req, res) => {
  res.json({
    status: 'success',
    message: 'AIDE Backend API is running',
    timestamp: new Date().toISOString(),
    version: '1.0.0'
  });
});

// 注册各模块路由
app.use('/api/auth', authRoutes);           // 用户认证路由
app.use('/api/users', userRoutes);          // 用户管理路由
app.use('/api/chat', chatRoutes);           // 聊天功能路由
app.use('/api/knowledge', knowledgeRoutes); // 知识库路由
app.use('/api/masters', masterRoutes);      // 大师库路由
app.use('/api/images', imageRoutes);        // 图片库路由
app.use('/api/prompts', promptRoutes);      // 提示词库路由
app.use('/api/scripts', scriptRoutes);      // 话术库路由
app.use('/api/settings', settingsRoutes);   // 设置路由
app.use('/api/features', featuresRoutes);   // 功能特性路由
app.use('/api/models', modelsRoutes);       // AI模型路由
app.use('/api/rag', ragRoutes);             // RAG知识库路由
app.use('/api/local-rag', localRagRoutes);   // 本地RAG路由
app.use('/api/midjourney', midjourneyRoutes); // Midjourney路由

// ==================== 错误处理中间件 ====================

// 404错误处理 - 处理未找到的路由
app.use(notFoundHandler);

// 全局错误处理中间件
app.use(errorHandler);

// ==================== 服务器启动 ====================

// 导出app实例供测试使用
module.exports = app;