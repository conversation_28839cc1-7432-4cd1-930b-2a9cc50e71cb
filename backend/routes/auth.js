/**
 * 用户认证路由
 * 
 * 功能说明：
 * - 用户注册和登录
 * - JWT令牌管理
 * - 密码重置
 * - 用户状态管理
 */

const express = require('express');
const bcrypt = require('bcryptjs');
const jwt = require('jsonwebtoken');
const router = express.Router();

const db = require('../config/database');
const { validate, userRegisterSchema, userLoginSchema } = require('../middleware/validation');
const { authenticateToken, generateToken, generateRefreshToken, verifyRefreshToken, logUserActivity } = require('../middleware/auth');
const { asyncHandler, ValidationError, AuthenticationError, ConflictError, createSuccessResponse } = require('../middleware/errorHandler');

/**
 * @route POST /api/auth/register
 * @desc 用户注册
 * @access Public
 */
router.post('/register', validate(userRegisterSchema), asyncHandler(async (req, res) => {
  const { username, password, displayName, role = 'user' } = req.body;
  let connection;

  try {
    connection = await db.getConnection();
    await db.beginTransaction(connection);

    // 检查用户名是否已存在
    const existingUsers = await db.execute(
      'SELECT id FROM users WHERE username = ?',
      [username],
      connection
    );
    if (existingUsers.length > 0) {
      throw new ConflictError('用户名已存在');
    }

    // 加密密码
    const saltRounds = parseInt(process.env.BCRYPT_ROUNDS) || 12;
    const hashedPassword = await bcrypt.hash(password, saltRounds);

    // 创建用户
    const result = await db.execute(
      `INSERT INTO users (username, password_hash, display_name, role, status, created_at, updated_at) 
       VALUES (?, ?, ?, ?, 'active', NOW(), NOW())`,
      [username, hashedPassword, displayName, role],
      connection
    );

    const userId = result.insertId;

    // 创建默认用户设置
    await db.execute(
      `INSERT INTO user_settings (user_id, theme, primary_color, font_size, animations, language, 
       date_format, desktop_notifications, sound_notifications, created_at, updated_at) 
       VALUES (?, 'light', 'blue', 14, true, 'zh-CN', 'YYYY-MM-DD', true, true, NOW(), NOW())`,
      [userId],
      connection
    );

    await db.commitTransaction(connection);

    // 生成JWT令牌
    const accessToken = generateToken({ id: userId, username, role });
    const refreshToken = generateRefreshToken({ id: userId });

    // 记录用户活动
    await logUserActivity(userId, 'register', req.ip, req.get('User-Agent'));

    // 获取完整用户信息
    const users = await db.execute(
      `SELECT u.id, u.username, u.display_name, u.role, u.status, u.avatar_url, u.created_at,
               s.theme, s.primary_color, s.font_size, s.animations, s.language, s.date_format,
               s.desktop_notifications, s.sound_notifications
        FROM users u
        LEFT JOIN user_settings s ON u.id = s.user_id
        WHERE u.id = ?`,
      [userId]
    );

    const user = users[0];

    res.status(201).json(createSuccessResponse({
      user: {
        id: user.id,
        username: user.username,
        displayName: user.display_name,
        role: user.role,
        status: user.status,
        avatarUrl: user.avatar_url,
        createdAt: user.created_at,
        settings: {
          theme: user.theme,
          primaryColor: user.primary_color,
          fontSize: user.font_size,
          animations: user.animations,
          language: user.language,
          dateFormat: user.date_format,
          desktopNotifications: user.desktop_notifications,
          soundNotifications: user.sound_notifications
        }
      },
      tokens: {
        accessToken,
        refreshToken,
        expiresIn: process.env.JWT_EXPIRES_IN || '24h'
      }
    }, '注册成功'));
  } catch (error) {
    if (connection) {
      await db.rollbackTransaction(connection);
    }
    throw error;
  } finally {
    if (connection) {
      connection.release();
    }
  }
}));

/**
 * @route POST /api/auth/login
 * @desc 用户登录
 * @access Public
 */
router.post('/login', validate(userLoginSchema), asyncHandler(async (req, res) => {
  const { username, password } = req.body;
  
  // 查找用户
  const users = await db.execute(
    `SELECT u.id, u.username, u.password_hash, u.display_name, u.role, u.status, u.avatar_url, u.created_at,
            s.theme, s.primary_color, s.font_size, s.animations, s.language, s.date_format,
            s.desktop_notifications, s.sound_notifications
     FROM users u
     LEFT JOIN user_settings s ON u.id = s.user_id
     WHERE u.username = ?`,
    [username]
  );
  
  if (users.length === 0) {
    throw new AuthenticationError('用户名或密码错误');
  }
  
  const user = users[0];
  
  // 检查用户状态
  if (user.status !== 'active') {
    throw new AuthenticationError('账户已被禁用');
  }
  
  // 验证密码
  const isPasswordValid = await bcrypt.compare(password, user.password_hash);
  if (!isPasswordValid) {
    throw new AuthenticationError('用户名或密码错误');
  }
  
  // 生成JWT令牌
  const accessToken = generateToken({ id: user.id, username: user.username, role: user.role });
  const refreshToken = generateRefreshToken({ id: user.id });
  
  // 更新最后登录时间
  await db.execute(
    'UPDATE users SET last_login_at = NOW() WHERE id = ?',
    [user.id]
  );
  
  // 记录用户活动
  await logUserActivity(user.id, 'login', req.ip, req.get('User-Agent'));
  
  res.json(createSuccessResponse({
    user: {
      id: user.id,
      username: user.username,
      displayName: user.display_name,
      role: user.role,
      status: user.status,
      avatarUrl: user.avatar_url,
      createdAt: user.created_at,
      settings: {
        theme: user.theme,
        primaryColor: user.primary_color,
        fontSize: user.font_size,
        animations: user.animations,
        language: user.language,
        dateFormat: user.date_format,
        desktopNotifications: user.desktop_notifications,
        soundNotifications: user.sound_notifications
      }
    },
    tokens: {
      accessToken,
      refreshToken,
      expiresIn: process.env.JWT_EXPIRES_IN || '24h'
    }
  }, '登录成功'));
}));

/**
 * @route POST /api/auth/refresh
 * @desc 刷新访问令牌
 * @access Public
 */
router.post('/refresh', asyncHandler(async (req, res) => {
  const { refreshToken } = req.body;
  
  if (!refreshToken) {
    throw new AuthenticationError('刷新令牌不能为空');
  }
  
  // 验证刷新令牌
  const decoded = verifyRefreshToken(refreshToken);
  
  // 查找用户
  const users = await db.execute(
    'SELECT id, username, role, status FROM users WHERE id = ?',
    [decoded.id]
  );
  
  if (users.length === 0) {
    throw new AuthenticationError('用户不存在');
  }
  
  const user = users[0];
  
  if (user.status !== 'active') {
    throw new AuthenticationError('账户已被禁用');
  }
  
  // 生成新的访问令牌
  const accessToken = generateToken({ id: user.id, username: user.username, role: user.role });
  
  res.json(createSuccessResponse({
    accessToken,
    expiresIn: process.env.JWT_EXPIRES_IN || '24h'
  }, '令牌刷新成功'));
}));

/**
 * @route POST /api/auth/logout
 * @desc 用户登出
 * @access Private
 */
router.post('/logout', authenticateToken, asyncHandler(async (req, res) => {
  // 记录用户活动
  await logUserActivity(req.user.id, 'logout', req.ip, req.get('User-Agent'));
  
  res.json(createSuccessResponse(null, '登出成功'));
}));

/**
 * @route GET /api/auth/me
 * @desc 获取当前用户信息
 * @access Private
 */
router.get('/me', authenticateToken, asyncHandler(async (req, res) => {
  // 获取用户详细信息
  const users = await db.execute(
    `SELECT u.id, u.username, u.display_name, u.role, u.status, u.avatar_url, u.created_at, u.last_login_at,
            s.theme, s.primary_color, s.font_size, s.animations, s.language, s.date_format,
            s.desktop_notifications, s.sound_notifications, s.default_chat_model, s.default_image_model
     FROM users u
     LEFT JOIN user_settings s ON u.id = s.user_id
     WHERE u.id = ?`,
    [req.user.id]
  );
  
  if (users.length === 0) {
    throw new AuthenticationError('用户不存在');
  }
  
  const user = users[0];
  
  res.json(createSuccessResponse({
    id: user.id,
    username: user.username,
    displayName: user.display_name,
    role: user.role,
    status: user.status,
    avatarUrl: user.avatar_url,
    createdAt: user.created_at,
    lastLoginAt: user.last_login_at,
    settings: {
      theme: user.theme,
      primaryColor: user.primary_color,
      fontSize: user.font_size,
      animations: user.animations,
      language: user.language,
      dateFormat: user.date_format,
      desktopNotifications: user.desktop_notifications,
      soundNotifications: user.sound_notifications,
      defaultChatModel: user.default_chat_model,
      defaultImageModel: user.default_image_model
    }
  }));
}));

/**
 * @route GET /api/auth/verify
 * @desc 验证访问令牌
 * @access Private
 */
router.get('/verify', authenticateToken, asyncHandler(async (req, res) => {
  res.json(createSuccessResponse({
    valid: true,
    user: {
      id: req.user.id,
      username: req.user.username,
      role: req.user.role
    }
  }, '令牌有效'));
}));

/**
 * @route POST /api/auth/change-password
 * @desc 修改密码
 * @access Private
 */
router.post('/change-password', authenticateToken, asyncHandler(async (req, res) => {
  const { currentPassword, newPassword } = req.body;
  
  if (!currentPassword || !newPassword) {
    throw new ValidationError('当前密码和新密码不能为空');
  }
  
  if (newPassword.length < 6) {
    throw new ValidationError('新密码至少需要6个字符');
  }
  
  // 获取用户当前密码
  const users = await db.execute(
    'SELECT password_hash FROM users WHERE id = ?',
    [req.user.id]
  );
  
  if (users.length === 0) {
    throw new AuthenticationError('用户不存在');
  }
  
  // 验证当前密码
  const isCurrentPasswordValid = await bcrypt.compare(currentPassword, users[0].password_hash);
  if (!isCurrentPasswordValid) {
    throw new AuthenticationError('当前密码错误');
  }
  
  // 加密新密码
  const saltRounds = parseInt(process.env.BCRYPT_ROUNDS) || 12;
  const hashedNewPassword = await bcrypt.hash(newPassword, saltRounds);
  
  // 更新密码
  await db.execute(
    'UPDATE users SET password_hash = ?, updated_at = NOW() WHERE id = ?',
    [hashedNewPassword, req.user.id]
  );
  
  // 记录用户活动
  await logUserActivity(req.user.id, 'change_password', req.ip, req.get('User-Agent'));
  
  res.json(createSuccessResponse(null, '密码修改成功'));
}));

/**
 * @route GET /api/auth/activity
 * @desc 获取用户活动日志
 * @access Private
 */
router.get('/activity', authenticateToken, asyncHandler(async (req, res) => {
  const { page = 1, limit = 20 } = req.query;
  const offset = (page - 1) * limit;
  
  // 获取活动日志
  const logs = await db.execute(
    `SELECT action, ip_address, user_agent, created_at
     FROM user_activity_logs
     WHERE user_id = ?
     ORDER BY created_at DESC
     LIMIT ? OFFSET ?`,
    [req.user.id, parseInt(limit), offset]
  );
  
  // 获取总数
  const countResult = await db.execute(
    'SELECT COUNT(*) as total FROM user_activity_logs WHERE user_id = ?',
    [req.user.id]
  );
  
  const total = countResult[0].total;
  const totalPages = Math.ceil(total / limit);
  
  res.json(createSuccessResponse(logs, '获取活动日志成功', {
    pagination: {
      page: parseInt(page),
      limit: parseInt(limit),
      total,
      totalPages,
      hasNext: page < totalPages,
      hasPrev: page > 1
    }
  }));
}));

module.exports = router;