#!/usr/bin/env python3
"""
Midjourney Discord客户端
基于Discord API与Midjourney Bot交互生成图片
"""

import asyncio
import aiohttp
import json
import sys
import time
import re
from typing import Optional, Dict, Any, List

class MidjourneyClient:
    """Midjourney Discord客户端"""
    
    def __init__(self, token: str, channel_id: str, server_id: str):
        self.token = token
        self.channel_id = channel_id
        self.server_id = server_id
        self.bot_id = "936929561302675456"  # Midjourney Bot ID
        self.base_url = "https://discord.com/api/v10"
        self.headers = {
            "Authorization": f"Bot {token}",
            "Content-Type": "application/json",
            "User-Agent": "DiscordBot (https://github.com/discord/discord-api-docs, 1.0)"
        }

        print(f"🔧 初始化Midjourney客户端")
        print(f"📍 频道ID: {channel_id}")
        print(f"🏠 服务器ID: {server_id}")
        print(f"🤖 Bot ID: {self.bot_id}")
        
    async def send_command(self, session: aiohttp.ClientSession, prompt: str, options: Dict = None) -> Optional[Dict]:
        """发送消息到Discord频道，触发Midjourney"""
        try:
            # 构建Midjourney命令
            command = f"/imagine prompt: {prompt}"
            if options:
                if options.get('aspect_ratio'):
                    command += f" --ar {options['aspect_ratio']}"
                if options.get('quality'):
                    command += f" --q {options['quality']}"
                if options.get('stylize'):
                    command += f" --s {options['stylize']}"
                if options.get('chaos'):
                    command += f" --c {options['chaos']}"
                if options.get('model'):
                    command += f" --v {options['model']}"

            print(f"📝 发送命令: {command}")

            url = f"{self.base_url}/channels/{self.channel_id}/messages"
            data = {
                "content": command,
                "tts": False
            }

            async with session.post(url, headers=self.headers, json=data) as response:
                response_text = await response.text()
                print(f"📊 响应状态: {response.status}")

                if response.status == 200:
                    result = await response.json()
                    print(f"✅ 命令发送成功: {command}")
                    return {"success": True, "message_id": result.get('id'), "command": command}
                else:
                    print(f"❌ 命令发送失败: {response.status}")
                    print(f"📄 错误详情: {response_text}")

                    # 检查是否是权限问题
                    if response.status == 403:
                        print("🚫 权限不足，请检查Bot权限设置")
                    elif response.status == 404:
                        print("🔍 频道不存在或Bot无法访问")

                    return None

        except Exception as e:
            print(f"❌ 发送命令异常: {e}")
            return None
    
    async def get_messages(self, session: aiohttp.ClientSession, limit: int = 50) -> List[Dict]:
        """获取频道消息"""
        try:
            url = f"{self.base_url}/channels/{self.channel_id}/messages?limit={limit}"
            
            async with session.get(url, headers=self.headers) as response:
                if response.status == 200:
                    messages = await response.json()
                    return messages
                else:
                    print(f"❌ 获取消息失败: {response.status}")
                    return []
                    
        except Exception as e:
            print(f"❌ 获取消息异常: {e}")
            return []
    
    async def wait_for_result(self, session: aiohttp.ClientSession, prompt: str, timeout: int = 300) -> Optional[Dict]:
        """等待Midjourney生成结果"""
        start_time = time.time()
        last_check_time = start_time
        
        print(f"⏳ 等待Midjourney生成结果...")
        
        while time.time() - start_time < timeout:
            try:
                messages = await self.get_messages(session, 20)
                
                for message in messages:
                    # 检查是否是Midjourney Bot的消息
                    if message.get('author', {}).get('id') != self.bot_id:
                        continue
                    
                    # 检查消息时间是否在命令发送之后
                    message_time = self.parse_discord_timestamp(message.get('timestamp', ''))
                    if message_time < last_check_time:
                        continue
                    
                    # 检查是否包含我们的提示词
                    content = message.get('content', '')
                    if prompt.lower() in content.lower():
                        # 检查是否有附件（生成的图片）
                        attachments = message.get('attachments', [])
                        if attachments:
                            return {
                                'message_id': message['id'],
                                'content': content,
                                'attachments': attachments,
                                'timestamp': message['timestamp'],
                                'url': attachments[0]['url'] if attachments else None
                            }
                        
                        # 检查是否是进度消息
                        if any(keyword in content.lower() for keyword in ['(waiting to start)', '(0%)', '(25%)', '(50%)', '(75%)', '(100%)']):
                            print(f"📊 生成进度: {content}")
                
                await asyncio.sleep(5)  # 每5秒检查一次
                
            except Exception as e:
                print(f"❌ 检查结果异常: {e}")
                await asyncio.sleep(5)
        
        print(f"⏰ 等待超时 ({timeout}秒)")
        return None
    
    def parse_discord_timestamp(self, timestamp_str: str) -> float:
        """解析Discord时间戳"""
        try:
            from datetime import datetime
            dt = datetime.fromisoformat(timestamp_str.replace('Z', '+00:00'))
            return dt.timestamp()
        except:
            return 0
    
    async def generate_image(self, prompt: str, options: Dict = None) -> Dict:
        """生成图片"""
        if options is None:
            options = {}

        async with aiohttp.ClientSession() as session:
            # 发送命令
            command_result = await self.send_command(session, prompt, options)
            if not command_result:
                return {
                    'success': False,
                    'error': '命令发送失败'
                }

            # 等待结果
            result = await self.wait_for_result(session, prompt)
            if result:
                return {
                    'success': True,
                    'message_id': result['message_id'],
                    'image_url': result['url'],
                    'content': result['content'],
                    'attachments': result['attachments'],
                    'prompt': prompt,
                    'command': command_result['prompt']
                }
            else:
                return {
                    'success': False,
                    'error': '生成超时或失败'
                }
    
    async def upscale_image(self, message_id: str, index: int) -> Dict:
        """放大图片"""
        # Midjourney的upscale通过点击按钮实现，这里需要模拟按钮点击
        # 由于Discord API限制，这个功能需要更复杂的实现
        return {
            'success': False,
            'error': 'Upscale功能需要更复杂的Discord交互实现'
        }
    
    async def create_variation(self, message_id: str, index: int) -> Dict:
        """创建变体"""
        # 类似upscale，需要模拟按钮点击
        return {
            'success': False,
            'error': 'Variation功能需要更复杂的Discord交互实现'
        }

async def main():
    """主函数"""
    if len(sys.argv) < 6:
        print("用法: python midjourney_client.py <action> <prompt/message_id> <token> <channel_id> <server_id> [options]")
        print("动作:")
        print("  generate <prompt> <token> <channel_id> <server_id> [options_json]")
        print("  transform <message_id> <action> <index> <token> <channel_id> [options_json]")
        sys.exit(1)
    
    action = sys.argv[1]
    
    try:
        if action == "generate":
            prompt = sys.argv[2]
            token = sys.argv[3]
            channel_id = sys.argv[4]
            server_id = sys.argv[5]
            options = json.loads(sys.argv[6]) if len(sys.argv) > 6 else {}
            
            client = MidjourneyClient(token, channel_id, server_id)
            result = await client.generate_image(prompt, options)
            
            if result['success']:
                print("MIDJOURNEY_RESULT:", json.dumps(result))
            else:
                print("MIDJOURNEY_ERROR:", result['error'])
        
        elif action == "transform":
            message_id = sys.argv[2]
            transform_action = sys.argv[3]  # upscale 或 variation
            index = int(sys.argv[4])
            token = sys.argv[5]
            channel_id = sys.argv[6]
            options = json.loads(sys.argv[7]) if len(sys.argv) > 7 else {}
            
            client = MidjourneyClient(token, channel_id, "")
            
            if transform_action == "upscale":
                result = await client.upscale_image(message_id, index)
            elif transform_action == "variation":
                result = await client.create_variation(message_id, index)
            else:
                result = {'success': False, 'error': f'未知的变换动作: {transform_action}'}
            
            if result['success']:
                print("MIDJOURNEY_RESULT:", json.dumps(result))
            else:
                print("MIDJOURNEY_ERROR:", result['error'])
        
        else:
            print("MIDJOURNEY_ERROR:", f"未知的动作: {action}")
            sys.exit(1)
    
    except Exception as e:
        print("MIDJOURNEY_ERROR:", str(e))
        sys.exit(1)

if __name__ == "__main__":
    asyncio.run(main())
