/**
 * 知识库管理路由
 * 
 * 功能说明：
 * - 知识库分类管理
 * - 知识库项目管理
 * - 标签管理
 * - 文件上传和管理
 */

const express = require('express');
const multer = require('multer');
const path = require('path');
const fs = require('fs');
const router = express.Router();

const db = require('../config/database');
const VectorService = require('../services/vectorService');
const { validate, knowledgeItemSchema, paginationSchema } = require('../middleware/validation');
const { authenticateToken, requireAdmin } = require('../middleware/auth');
const { asyncHandler, ValidationError, NotFoundError, AuthorizationError, createSuccessResponse } = require('../middleware/errorHandler');

// 配置文件上传
const storage = multer.diskStorage({
  destination: (req, file, cb) => {
    const uploadDir = path.join(__dirname, '../uploads/knowledge');
    if (!fs.existsSync(uploadDir)) {
      fs.mkdirSync(uploadDir, { recursive: true });
    }
    cb(null, uploadDir);
  },
  filename: (req, file, cb) => {
    const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
    cb(null, `knowledge-${uniqueSuffix}${path.extname(file.originalname)}`);
  }
});

const upload = multer({
  storage,
  limits: {
    fileSize: parseInt(process.env.MAX_FILE_SIZE) || 10 * 1024 * 1024 // 10MB
  },
  fileFilter: (req, file, cb) => {
    const allowedTypes = [
      'text/plain', 'text/markdown', 'application/pdf',
      'application/msword', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
      'application/json', 'text/csv'
    ];
    if (allowedTypes.includes(file.mimetype)) {
      cb(null, true);
    } else {
      cb(new ValidationError('不支持的文件类型'));
    }
  }
});

// ==================== 分类管理 ====================

/**
 * @route GET /api/knowledge/categories
 * @desc 获取知识库分类列表
 * @access Private
 */
router.get('/categories', authenticateToken, asyncHandler(async (req, res) => {
  const [categories] = await db.execute(
    `SELECT kc.id, kc.name, kc.description, kc.created_at, kc.updated_at,
            COUNT(ki.id) as item_count
     FROM knowledge_categories kc
     LEFT JOIN knowledge_items ki ON kc.id = ki.category_id
     GROUP BY kc.id
     ORDER BY kc.name`
  );
  
  // 确保返回的是数组格式
  const categoriesArray = Array.isArray(categories) ? categories : [categories];
  
  res.json(createSuccessResponse(categoriesArray));
}));

/**
 * @route POST /api/knowledge/categories
 * @desc 创建知识库分类
 * @access Private (Admin only)
 */
router.post('/categories', authenticateToken, requireAdmin, asyncHandler(async (req, res) => {
  const { name, description } = req.body;
  
  if (!name || name.trim().length === 0) {
    throw new ValidationError('分类名称不能为空');
  }
  
  // 检查分类名称是否已存在
  const [existing] = await db.execute(
    'SELECT id FROM knowledge_categories WHERE name = ?',
    [name.trim()]
  );
  
  if (existing.length > 0) {
    throw new ValidationError('分类名称已存在');
  }
  
  // 创建分类
  const [result] = await db.execute(
    'INSERT INTO knowledge_categories (name, description, created_at, updated_at) VALUES (?, ?, NOW(), NOW())',
    [name.trim(), description || '']
  );
  
  const categoryId = result.insertId;
  
  // 获取创建的分类信息
  const [categories] = await db.execute(
    'SELECT id, name, description, created_at, updated_at FROM knowledge_categories WHERE id = ?',
    [categoryId]
  );
  
  res.status(201).json(createSuccessResponse(categories[0], '分类创建成功'));
}));

/**
 * @route PUT /api/knowledge/categories/:id
 * @desc 更新知识库分类
 * @access Private (Admin only)
 */
router.put('/categories/:id', authenticateToken, requireAdmin, asyncHandler(async (req, res) => {
  const categoryId = req.params.id;
  const { name, description } = req.body;
  
  // 检查分类是否存在
  const [categories] = await db.execute(
    'SELECT id FROM knowledge_categories WHERE id = ?',
    [categoryId]
  );
  
  if (categories.length === 0) {
    throw new NotFoundError('分类不存在');
  }
  
  // 检查名称是否与其他分类冲突
  if (name) {
    const [existing] = await db.execute(
      'SELECT id FROM knowledge_categories WHERE name = ? AND id != ?',
      [name.trim(), categoryId]
    );
    
    if (existing.length > 0) {
      throw new ValidationError('分类名称已存在');
    }
  }
  
  // 构建更新语句
  const updateFields = [];
  const updateValues = [];
  
  if (name !== undefined) {
    updateFields.push('name = ?');
    updateValues.push(name.trim());
  }
  
  if (description !== undefined) {
    updateFields.push('description = ?');
    updateValues.push(description);
  }
  
  if (updateFields.length > 0) {
    updateFields.push('updated_at = NOW()');
    updateValues.push(categoryId);
    
    await db.execute(
      `UPDATE knowledge_categories SET ${updateFields.join(', ')} WHERE id = ?`,
      updateValues
    );
  }
  
  res.json(createSuccessResponse(null, '分类更新成功'));
}));

/**
 * @route DELETE /api/knowledge/categories/:id
 * @desc 删除知识库分类
 * @access Private (Admin only)
 */
router.delete('/categories/:id', authenticateToken, requireAdmin, asyncHandler(async (req, res) => {
  const categoryId = req.params.id;
  
  // 检查分类是否存在
  const [categories] = await db.execute(
    'SELECT id FROM knowledge_categories WHERE id = ?',
    [categoryId]
  );
  
  if (categories.length === 0) {
    throw new NotFoundError('分类不存在');
  }
  
  // 检查是否有关联的知识项目
  const items = await db.execute(
    'SELECT COUNT(*) as count FROM knowledge_items WHERE category_id = ?',
    [categoryId]
  );
  
  if (items[0].count > 0) {
    throw new ValidationError('无法删除包含知识项目的分类');
  }
  
  // 删除分类
  await db.execute(
    'DELETE FROM knowledge_categories WHERE id = ?',
    [categoryId]
  );
  
  res.json(createSuccessResponse(null, '分类删除成功'));
}));

// ==================== 知识项目管理 ====================

/**
 * @route GET /api/knowledge/items
 * @desc 获取知识库项目列表
 * @access Private
 */
router.get('/items', authenticateToken, validate(paginationSchema, 'query'), asyncHandler(async (req, res) => {
  const { page, limit, sortBy, sortOrder, search } = req.query;
  const { categoryId, tags } = req.query;
  const offset = (page - 1) * limit;
  
  // 构建查询条件
  let whereClause = 'WHERE 1=1';
  let queryParams = [];
  
  if (search) {
    whereClause += ' AND (ki.title LIKE ? OR ki.content LIKE ?)';
    queryParams.push(`%${search}%`, `%${search}%`);
  }
  
  if (categoryId) {
    whereClause += ' AND ki.category_id = ?';
    queryParams.push(categoryId);
  }
  
  // 如果有标签筛选
  let tagJoin = '';
  if (tags) {
    const tagList = tags.split(',').map(tag => tag.trim()).filter(tag => tag);
    if (tagList.length > 0) {
      tagJoin = `
        INNER JOIN knowledge_item_tags kit ON ki.id = kit.item_id
        INNER JOIN knowledge_tags kt ON kit.tag_id = kt.id
      `;
      whereClause += ` AND kt.name IN (${tagList.map(() => '?').join(',')})`;
      queryParams.push(...tagList);
    }
  }
  
  // 获取知识项目列表
  const items = await db.execute(
    `SELECT DISTINCT ki.id, ki.title, ki.content as description, ki.file_path, ki.file_type, ki.file_size,
            ki.created_at, ki.updated_at,
            kc.name as category_name,
            u.display_name as creator_name
     FROM knowledge_items ki
     LEFT JOIN knowledge_categories kc ON ki.category_id = kc.id
     LEFT JOIN users u ON ki.created_by = u.id
     ${tagJoin}
     ${whereClause}
     ORDER BY ki.${sortBy} ${sortOrder}
     LIMIT ? OFFSET ?`,
    [...queryParams, String(limit), String(offset)]
  );
  
  // 获取每个项目的标签
  for (let item of items) {
    const itemTags = await db.execute(
      `SELECT kt.id, kt.name
       FROM knowledge_tags kt
       INNER JOIN knowledge_item_tags kit ON kt.id = kit.tag_id
       WHERE kit.item_id = ?`,
      [item.id]
    );
    item.tags = itemTags;
  }
  
  // 获取总数
  const countResult = await db.execute(
    `SELECT COUNT(DISTINCT ki.id) as total
     FROM knowledge_items ki
     ${tagJoin}
     ${whereClause}`,
    queryParams
  );
  
  const total = countResult[0].total;
  const totalPages = Math.ceil(total / limit);
  
  res.json(createSuccessResponse(items, '获取知识项目列表成功', {
    pagination: {
      page,
      limit,
      total,
      totalPages,
      hasNext: page < totalPages,
      hasPrev: page > 1
    }
  }));
}));

/**
 * @route GET /api/knowledge/items/:id
 * @desc 获取知识库项目详情
 * @access Private
 */
router.get('/items/:id', authenticateToken, asyncHandler(async (req, res) => {
  const itemId = String(req.params.id);
  
  // 获取项目详情
  const [items] = await db.execute(
    `SELECT ki.id, ki.title, ki.content, ki.file_path, ki.file_type, ki.file_size,
            ki.category_id, ki.created_by, ki.created_at, ki.updated_at,
            kc.name as category_name,
            u.display_name as creator_name
     FROM knowledge_items ki
     LEFT JOIN knowledge_categories kc ON ki.category_id = kc.id
     LEFT JOIN users u ON ki.created_by = u.id
     WHERE ki.id = ?`,
    [itemId]
  );
  
  if (items.length === 0) {
    throw new NotFoundError('知识项目不存在');
  }
  
  const item = items[0];
  
  // 获取项目标签
  const tags = await db.execute(
    `SELECT kt.id, kt.name
     FROM knowledge_tags kt
     INNER JOIN knowledge_item_tags kit ON kt.id = kit.tag_id
     WHERE kit.item_id = ?`,
    [itemId]
  );
  item.tags = tags;
  
  // 如果有文件，读取文件内容（仅文本文件）
  if (item.file_path && ['text/plain', 'text/markdown', 'application/json'].includes(item.file_type)) {
    try {
      const filePath = path.join(__dirname, '../', item.file_path);
      if (fs.existsSync(filePath)) {
        item.content = fs.readFileSync(filePath, 'utf8');
      }
    } catch (error) {
      // 文件读取失败，不影响其他信息的返回
      console.error('Failed to read file:', error);
    }
  }
  
  res.json(createSuccessResponse(item));
}));

/**
 * @route POST /api/knowledge/items
 * @desc 创建知识库项目
 * @access Private
 */
router.post('/items', authenticateToken, upload.single('file'), validate(knowledgeItemSchema), asyncHandler(async (req, res) => {
  const { title, content, categoryId } = req.body;
  const file = req.file;
  
  // 处理标签数组 - 从FormData中获取tags[]
  let tags = [];
  if (req.body.tags && Array.isArray(req.body.tags)) {
    tags = req.body.tags;
  } else if (req.body.tags) {
    // 如果是字符串，按逗号分割
    tags = req.body.tags.split(',').map(tag => tag.trim()).filter(tag => tag);
  }
  
  // 验证分类是否存在
  if (categoryId) {
    const [categories] = await db.execute(
      'SELECT id FROM knowledge_categories WHERE id = ?',
      [categoryId]
    );
    
    if (categories.length === 0) {
      throw new ValidationError('指定的分类不存在');
    }
  }
  
  // 开始事务
  const connection = await db.getConnection();
  await db.beginTransaction(connection);
  
  try {
    // 创建知识项目
    const [result] = await db.execute(
      `INSERT INTO knowledge_items (title, content, category_id, file_path, file_type, file_size, created_by, created_at, updated_at)
       VALUES (?, ?, ?, ?, ?, ?, ?, NOW(), NOW())`,
      [
        title,
        content || '',
        categoryId || null,
        file ? `/uploads/knowledge/${file.filename}` : null,
        file ? file.mimetype : null,
        file ? file.size : null,
        req.user.id
      ],
      connection
    );
    
    const itemId = result.insertId;
    
    // 处理标签
    if (tags && tags.length > 0) {
      for (const tagName of tags) {
        if (tagName.trim()) {
          // 查找或创建标签
          let [existingTags] = await db.execute(
            'SELECT id FROM knowledge_tags WHERE name = ?',
            [tagName.trim()],
            connection
          );
          
          let tagId;
          if (existingTags.length === 0) {
            const [tagResult] = await db.execute(
              'INSERT INTO knowledge_tags (name, created_at) VALUES (?, NOW())',
              [tagName.trim()],
              connection
            );
            tagId = tagResult.insertId;
          } else {
            tagId = existingTags[0].id;
          }
          
          // 关联标签和项目
          await db.execute(
            'INSERT INTO knowledge_item_tags (item_id, tag_id) VALUES (?, ?)',
            [itemId, tagId],
            connection
          );
        }
      }
    }
    
    await db.commitTransaction(connection);
    
    // 获取创建的项目信息
    const [items] = await db.execute(
      `SELECT ki.id, ki.title, ki.content, ki.file_path, ki.file_type, ki.file_size,
              ki.created_at, ki.updated_at,
              kc.name as category_name,
              u.display_name as creator_name
       FROM knowledge_items ki
       LEFT JOIN knowledge_categories kc ON ki.category_id = kc.id
       LEFT JOIN users u ON ki.created_by = u.id
       WHERE ki.id = ?`,
      [itemId]
    );
    
    const item = items[0];
    
    // 获取项目标签
    const [itemTags] = await db.execute(
      `SELECT kt.id, kt.name
       FROM knowledge_tags kt
       INNER JOIN knowledge_item_tags kit ON kt.id = kit.tag_id
       WHERE kit.item_id = ?`,
      [itemId]
    );
    item.tags = itemTags;
    
    res.status(201).json(createSuccessResponse(item, '知识项目创建成功'));
  } catch (error) {
    await db.rollbackTransaction(connection);
    
    // 删除已上传的文件
    if (file && fs.existsSync(file.path)) {
      fs.unlinkSync(file.path);
    }
    
    throw error;
  }
}));

/**
 * @route PUT /api/knowledge/items/:id
 * @desc 更新知识库项目
 * @access Private
 */
router.put('/items/:id', authenticateToken, asyncHandler(async (req, res) => {
  const itemId = req.params.id;
  const { title, content, categoryId, tags } = req.body;
  
  // 检查项目是否存在和权限
  const items = await db.execute(
    'SELECT created_by FROM knowledge_items WHERE id = ?',
    [itemId]
  );
  
  if (items.length === 0) {
    throw new NotFoundError('知识项目不存在');
  }
  
  if (items[0].created_by !== req.user.id && req.user.role !== 'admin') {
    throw new AuthorizationError('无权修改此知识项目');
  }
  
  // 验证分类是否存在
  if (categoryId) {
    const [categories] = await db.execute(
      'SELECT id FROM knowledge_categories WHERE id = ?',
      [categoryId]
    );
    
    if (categories.length === 0) {
      throw new ValidationError('指定的分类不存在');
    }
  }
  
  // 开始事务
  const connection = await db.getConnection();
  await db.beginTransaction(connection);
  
  try {
    // 更新基本信息
    const updateFields = [];
    const updateValues = [];
    
    if (title !== undefined) {
      updateFields.push('title = ?');
      updateValues.push(title);
    }
    
    if (content !== undefined) {
      updateFields.push('content = ?');
      updateValues.push(content);
    }
    
    if (categoryId !== undefined) {
      updateFields.push('category_id = ?');
      updateValues.push(categoryId);
    }
    
    if (updateFields.length > 0) {
      updateFields.push('updated_at = NOW()');
      updateValues.push(itemId);
      
      await db.execute(
        `UPDATE knowledge_items SET ${updateFields.join(', ')} WHERE id = ?`,
        updateValues,
        connection
      );
    }
    
    // 更新标签
    if (tags !== undefined) {
      // 删除现有标签关联
      await db.execute(
        'DELETE FROM knowledge_item_tags WHERE item_id = ?',
        [itemId],
        connection
      );
      
      // 添加新标签
      if (tags && tags.length > 0) {
        for (const tagName of tags) {
          if (tagName.trim()) {
            // 查找或创建标签
            let [existingTags] = await db.execute(
              'SELECT id FROM knowledge_tags WHERE name = ?',
              [tagName.trim()],
              connection
            );
            
            let tagId;
            if (existingTags.length === 0) {
              const [tagResult] = await db.execute(
                'INSERT INTO knowledge_tags (name, created_at) VALUES (?, NOW())',
                [tagName.trim()],
                connection
              );
              tagId = tagResult.insertId;
            } else {
              tagId = existingTags.id;
            }
            
            // 关联标签和项目
            await db.execute(
              'INSERT INTO knowledge_item_tags (item_id, tag_id) VALUES (?, ?)',
              [itemId, tagId],
              connection
            );
          }
        }
      }
    }
    
    await db.commitTransaction(connection);
    
    res.json(createSuccessResponse(null, '知识项目更新成功'));
  } catch (error) {
    await db.rollbackTransaction(connection);
    throw error;
  }
}));

/**
 * @route DELETE /api/knowledge/items/:id
 * @desc 删除知识库项目
 * @access Private
 */
router.delete('/items/:id', authenticateToken, asyncHandler(async (req, res) => {
  const itemId = req.params.id;
  
  // 检查项目是否存在和权限
  const items = await db.execute(
    'SELECT created_by, file_path FROM knowledge_items WHERE id = ?',
    [itemId]
  );
  
  if (items.length === 0) {
    throw new NotFoundError('知识项目不存在');
  }
  
  if (items[0].created_by !== req.user.id && req.user.role !== 'admin') {
    throw new AuthorizationError('无权删除此知识项目');
  }
  
  // 开始事务
  const connection = await db.getConnection();
  await db.beginTransaction(connection);
  
  try {
    // 删除标签关联
    await db.execute(
      'DELETE FROM knowledge_item_tags WHERE item_id = ?',
      [itemId],
      connection
    );
    
    // 删除消息引用
    await db.execute(
      'DELETE FROM message_references WHERE type = "knowledge" AND reference_id = ?',
      [itemId],
      connection
    );
    
    // 删除项目
    await db.execute(
      'DELETE FROM knowledge_items WHERE id = ?',
      [itemId],
      connection
    );
    
    await db.commitTransaction(connection);
    
    // 删除文件
    if (items[0].file_path) {
      const filePath = path.join(__dirname, '../', items[0].file_path);
      if (fs.existsSync(filePath)) {
        fs.unlinkSync(filePath);
      }
    }
    
    res.json(createSuccessResponse(null, '知识项目删除成功'));
  } catch (error) {
    await db.rollbackTransaction(connection);
    throw error;
  }
}));

// ==================== 标签管理 ====================

/**
 * @route GET /api/knowledge/tags
 * @desc 获取知识库标签列表
 * @access Private
 */
router.get('/tags', authenticateToken, asyncHandler(async (req, res) => {
  const [tags] = await db.execute(
    `SELECT kt.id, kt.name, kt.created_at,
            COUNT(kit.item_id) as usage_count
     FROM knowledge_tags kt
     LEFT JOIN knowledge_item_tags kit ON kt.id = kit.tag_id
     GROUP BY kt.id
     ORDER BY usage_count DESC, kt.name`
  );
  
  res.json(createSuccessResponse(tags));
}));

/**
 * @route GET /api/knowledge/stats
 * @desc 获取知识库统计信息
 * @access Private
 */
router.get('/stats', authenticateToken, asyncHandler(async (req, res) => {
  const userId = req.user.id;
  
  // 获取统计信息
  const [stats] = await db.execute(
    `SELECT 
       (SELECT COUNT(*) FROM knowledge_items) as total_items,
       (SELECT COUNT(*) FROM knowledge_items WHERE created_by = ?) as my_items,
       (SELECT COUNT(*) FROM knowledge_categories) as total_categories,
       (SELECT COUNT(*) FROM knowledge_tags) as total_tags,
       (SELECT SUM(file_size) FROM knowledge_items WHERE file_size IS NOT NULL) as total_file_size`,
    [userId]
  );
  
  // 获取分类统计
  const [categoryStats] = await db.execute(
    `SELECT kc.name, COUNT(ki.id) as item_count
     FROM knowledge_categories kc
     LEFT JOIN knowledge_items ki ON kc.id = ki.category_id
     GROUP BY kc.id
     ORDER BY item_count DESC
     LIMIT 10`
  );
  
  // 获取最近创建的项目
  const [recentItems] = await db.execute(
    `SELECT ki.id, ki.title, ki.created_at,
            kc.name as category_name,
            u.display_name as creator_name
     FROM knowledge_items ki
     LEFT JOIN knowledge_categories kc ON ki.category_id = kc.id
     LEFT JOIN users u ON ki.created_by = u.id
     ORDER BY ki.created_at DESC
     LIMIT 5`
  );
  
  res.json(createSuccessResponse({
    overview: stats[0],
    categoryStats,
    recentItems
  }));
}));

// ==================== 向量化相关API ====================

/**
 * @route POST /api/knowledge/items/:id/vectorize
 * @desc 处理知识库项目的向量化
 * @access Private
 */
router.post('/items/:id/vectorize', authenticateToken, asyncHandler(async (req, res) => {
  const itemId = req.params.id;
  const userId = req.user.id;

  // 检查项目是否存在和权限
  const [items] = await db.execute(
    'SELECT file_path, created_by FROM knowledge_items WHERE id = ?',
    [itemId]
  );

  if (items.length === 0) {
    throw new NotFoundError('知识库项目不存在');
  }

  if (items[0].created_by !== userId && req.user.role !== 'admin') {
    throw new AuthorizationError('无权处理此知识库项目');
  }

  const filePath = items[0].file_path;
  if (!filePath) {
    throw new ValidationError('该项目没有关联的文件');
  }

  const fullPath = path.join(__dirname, '..', filePath);
  if (!fs.existsSync(fullPath)) {
    throw new ValidationError('文件不存在');
  }

  // 异步处理向量化
  VectorService.processDocumentVectorization(itemId, fullPath)
    .then(result => {
      console.log(`项目 ${itemId} 向量化完成:`, result);
    })
    .catch(error => {
      console.error(`项目 ${itemId} 向量化失败:`, error);
    });

  res.json(createSuccessResponse(null, '向量化处理已开始，请稍后查看进度'));
}));

/**
 * @route GET /api/knowledge/items/:id/vectorization-progress
 * @desc 获取知识库项目的向量化进度
 * @access Private
 */
router.get('/items/:id/vectorization-progress', authenticateToken, asyncHandler(async (req, res) => {
  const itemId = req.params.id;
  const userId = req.user.id;

  // 检查项目是否存在和权限
  const [items] = await db.execute(
    'SELECT created_by FROM knowledge_items WHERE id = ?',
    [itemId]
  );

  if (items.length === 0) {
    throw new NotFoundError('知识库项目不存在');
  }

  if (items[0].created_by !== userId && req.user.role !== 'admin') {
    throw new AuthorizationError('无权查看此知识库项目');
  }

  const progress = await VectorService.getVectorizationProgress(itemId);
  
  if (!progress) {
    throw new ValidationError('获取进度失败');
  }

  res.json(createSuccessResponse(progress));
}));

/**
 * @route POST /api/knowledge/vector-search
 * @desc 向量检索
 * @access Private
 */
router.post('/vector-search', authenticateToken, asyncHandler(async (req, res) => {
  const { query, limit = 5 } = req.body;
  const userId = req.user.id;

  if (!query || query.trim().length === 0) {
    throw new ValidationError('查询内容不能为空');
  }

  const results = await VectorService.vectorSearch(query.trim(), limit, userId);
  
  res.json(createSuccessResponse(results));
}));

module.exports = router;