/**
 * 话术库管理路由
 * 
 * 功能说明：
 * - 话术信息管理
 * - 话术分类管理
 * - 话术标签管理
 * - 话术参数管理
 */

const express = require('express');
const router = express.Router();

const db = require('../config/database');
const { validate, scriptSchema, paginationSchema } = require('../middleware/validation');
const { authenticateToken, requireAdmin } = require('../middleware/auth');
const { asyncHandler, ValidationError, NotFoundError, AuthorizationError, createSuccessResponse } = require('../middleware/errorHandler');

// ==================== 分类管理 ====================

/**
 * @route GET /api/scripts/categories
 * @desc 获取话术库分类列表
 * @access Private
 */
router.get('/categories', authenticateToken, asyncHandler(async (req, res) => {
  const categories = await db.execute(
    `SELECT sc.id, sc.name, sc.description, sc.created_at, sc.updated_at,
            COUNT(s.id) as script_count
     FROM script_categories sc
     LEFT JOIN scripts s ON sc.id = s.category_id
     GROUP BY sc.id
     ORDER BY sc.name`
  );
  
  res.json(createSuccessResponse(categories));
}));

/**
 * @route POST /api/scripts/categories
 * @desc 创建话术库分类
 * @access Private (Admin only)
 */
router.post('/categories', authenticateToken, requireAdmin, asyncHandler(async (req, res) => {
  const { name, description } = req.body;
  
  if (!name || name.trim().length === 0) {
    throw new ValidationError('分类名称不能为空');
  }
  
  // 检查分类名称是否已存在
  const existing = await db.execute(
    'SELECT id FROM script_categories WHERE name = ?',
    [name.trim()]
  );
  
  if (existing.length > 0) {
    throw new ValidationError('分类名称已存在');
  }
  
  // 创建分类
  const result = await db.execute(
    'INSERT INTO script_categories (id, name, description, created_at, updated_at) VALUES (?, ?, ?, NOW(), NOW())',
    [name.toLowerCase().replace(/\s+/g, '_'), name.trim(), description || '']
  );
  
  // 获取创建的分类信息
  const categories = await db.execute(
    'SELECT id, name, description, created_at, updated_at FROM script_categories WHERE id = ?',
    [name.toLowerCase().replace(/\s+/g, '_')]
  );
  
  res.status(201).json(createSuccessResponse(categories[0], '分类创建成功'));
}));

/**
 * @route PUT /api/scripts/categories/:id
 * @desc 更新话术库分类
 * @access Private (Admin only)
 */
router.put('/categories/:id', authenticateToken, requireAdmin, asyncHandler(async (req, res) => {
  const categoryId = req.params.id;
  const { name, description } = req.body;
  
  // 检查分类是否存在
  const categories = await db.execute(
    'SELECT id FROM script_categories WHERE id = ?',
    [String(categoryId)]
  );
  
  if (categories.length === 0) {
    throw new NotFoundError('分类不存在');
  }
  
  // 构建更新语句
  const updateFields = [];
  const updateValues = [];
  
  if (name !== undefined) {
    updateFields.push('name = ?');
    updateValues.push(name.trim());
  }
  
  if (description !== undefined) {
    updateFields.push('description = ?');
    updateValues.push(description);
  }
  
  if (updateFields.length > 0) {
    updateFields.push('updated_at = NOW()');
    updateValues.push(String(categoryId));
    
    await db.execute(
      `UPDATE script_categories SET ${updateFields.join(', ')} WHERE id = ?`,
      updateValues
    );
  }
  
  res.json(createSuccessResponse(null, '分类更新成功'));
}));

/**
 * @route DELETE /api/scripts/categories/:id
 * @desc 删除话术库分类
 * @access Private (Admin only)
 */
router.delete('/categories/:id', authenticateToken, requireAdmin, asyncHandler(async (req, res) => {
  const categoryId = req.params.id;
  
  // 检查分类是否存在
  const categories = await db.execute(
    'SELECT id FROM script_categories WHERE id = ?',
    [String(categoryId)]
  );
  
  if (categories.length === 0) {
    throw new NotFoundError('分类不存在');
  }
  
  // 检查是否有关联的话术
  const scripts = await db.execute(
    'SELECT COUNT(*) as count FROM scripts WHERE category_id = ?',
    [String(categoryId)]
  );
  
  if (scripts[0].count > 0) {
    throw new ValidationError('无法删除包含话术的分类');
  }
  
  // 删除分类
  await db.execute(
    'DELETE FROM script_categories WHERE id = ?',
    [String(categoryId)]
  );
  
  res.json(createSuccessResponse(null, '分类删除成功'));
}));

// ==================== 话术信息管理 ====================

/**
 * @route GET /api/scripts
 * @desc 获取话术列表
 * @access Private
 */
router.get('/', authenticateToken, validate(paginationSchema, 'query'), asyncHandler(async (req, res) => {
  const { page, limit, sortBy, sortOrder, search } = req.query;
  const { categoryId, tags } = req.query;
  const offset = (page - 1) * limit;
  // 构建查询条件
  let whereClause = 'WHERE 1=1';
  let queryParams = [];
  
  if (search) {
    whereClause += ' AND (s.title LIKE ? OR s.description LIKE ? OR s.content LIKE ?)';
    queryParams.push(`%${search}%`, `%${search}%`, `%${search}%`);
  }

  if (categoryId && !['undefined', 'null', 'all'].includes(categoryId)) {
    whereClause += ' AND s.category_id = ?';
    queryParams.push(String(categoryId));
  }
  
  // 如果有标签筛选
  let tagJoin = '';
  if (tags) {
    const tagList = tags.split(',').map(tag => tag.trim()).filter(tag => tag);
    if (tagList.length > 0) {
      tagJoin = `
        INNER JOIN script_item_tags sit ON s.id = sit.script_id
        INNER JOIN script_tags st ON sit.tag_id = st.id
      `;
      whereClause += ` AND st.name IN (${tagList.map(() => '?').join(',')})`;
      queryParams.push(...tagList);
    }
  }

  
  // 获取话术列表
  const scripts = await db.execute(
    `SELECT DISTINCT s.category_id,s.id, s.title, s.description, s.content, s.is_public, s.usage_count, s.created_at, s.updated_at,
            sc.name as category_name,
            u.display_name as creator_name
     FROM scripts s
     LEFT JOIN script_categories sc ON s.category_id = sc.id
     LEFT JOIN users u ON s.created_by = u.id
     ${tagJoin}
     ${whereClause}
     ORDER BY s.${sortBy} ${sortOrder}
      LIMIT ? OFFSET ?`,
    [...queryParams, String(limit), String(offset)]
  );
  
  // 获取每个话术的标签和参数
  for (let script of scripts) {
    // 获取标签
    const scriptTags = await db.execute(
      `SELECT st.id, st.name
       FROM script_tags st
       INNER JOIN script_item_tags sit ON st.id = sit.tag_id
       WHERE sit.script_id = ?`,
      [script.id]
    );
    script.tags = scriptTags;
    
    // 获取参数
    const parameters = await db.execute(
      'SELECT parameter_name, parameter_type, default_value, description FROM script_parameters WHERE script_id = ? ORDER BY parameter_name',
      [script.id]
    );
    script.parameters = parameters;
  }
  
  // 获取总数
  const countResult = await db.execute(
    `SELECT COUNT(DISTINCT s.id) as total
     FROM scripts s
     ${tagJoin}
     ${whereClause}`,
    queryParams
  );
  
  const total = countResult[0].total;
  const totalPages = Math.ceil(total / limit);
  
  res.json(createSuccessResponse(scripts, '获取话术列表成功', {
    pagination: {
      page,
      limit,
      total,
      totalPages,
      hasNext: page < totalPages,
      hasPrev: page > 1
    }
  }));
}));

/**
 * @route GET /api/scripts/:id
 * @desc 获取话术详情
 * @access Private
 */
router.get('/:id', authenticateToken, asyncHandler(async (req, res) => {
  const scriptId = req.params.id;
  
  // 获取话术详情
  const originalScript = await db.execute(
    `SELECT s.id, s.title, s.description, s.content, s.is_public, s.usage_count, s.category_id, s.created_by, s.created_at, s.updated_at,
            sc.name as category_name,
            u.display_name as creator_name
     FROM scripts s
     LEFT JOIN script_categories sc ON s.category_id = sc.id
     LEFT JOIN users u ON s.created_by = u.id
     WHERE s.id = ?`,
    [String(scriptId)]
  );
  
  if (originalScript.length === 0) {
    throw new NotFoundError('话术不存在');
  }
  
  const script = originalScript[0];
  
  // 检查访问权限
  if (!script.is_public && script.created_by !== req.user.id && req.user.role !== 'admin') {
    throw new AuthorizationError('无权访问此话术');
  }
  
  // 获取标签
  const tags = await db.execute(
    `SELECT st.id, st.name
     FROM script_tags st
     INNER JOIN script_item_tags sit ON st.id = sit.tag_id
     WHERE sit.script_id = ?`,
    [scriptId]
    );
    script.tags = scriptTags;
  
  // 获取参数
  const parameters = await db.execute(
    'SELECT parameter_name, parameter_type, default_value, description FROM script_parameters WHERE script_id = ? ORDER BY parameter_name',
    [String(scriptId)]
  );
  script.parameters = parameters;
  
  // 增加使用次数
  await db.execute(
    'UPDATE scripts SET usage_count = usage_count + 1 WHERE id = ?',
    [String(scriptId)]
  );
  
  res.json(createSuccessResponse(script));
}));

/**
 * @route POST /api/scripts
 * @desc 创建话术
 * @access Private
 */
router.post('/', authenticateToken, validate(scriptSchema), asyncHandler(async (req, res) => {
  const { title, categoryId, description, content, isPublic, parameters, scriptTags: tags } = req.body;
  
  // 验证分类是否存在
  const categories = await db.execute(
    'SELECT id FROM script_categories WHERE id = ?',
    [String(categoryId)]
  );
  
  if (categories.length === 0) {
    throw new ValidationError('指定的分类不存在');
  }
  
  // 检查话术标题是否已存在（同一用户下）
  const existing = await db.execute(
    'SELECT id FROM scripts WHERE title = ? AND created_by = ?',
    [title, req.user.id]
  );
  
  if (existing.length > 0) {
    throw new ValidationError('您已有同名的话术');
  }
  
  // 开始事务
  const connection = await db.getConnection();
  await db.beginTransaction(connection);
  
  try {
    // 创建话术
    const result = await db.execute(
      `INSERT INTO scripts (title, category_id, description, content, is_public, created_by, created_at, updated_at)
       VALUES (?, ?, ?, ?, ?, ?, NOW(), NOW())`,
      [title, String(categoryId), description, content, isPublic || false, req.user.id],
      connection
    );
    
    const scriptId = result.insertId;
    
    // 处理标签
    if (tags && tags.length > 0) {
      for (const tagName of tags) {
        if (typeof tagName === 'string' && tagName.trim()) {
          // 查找或创建标签
          let existingTags = await db.execute(
            'SELECT id FROM script_tags WHERE name = ?',
            [tagName.trim()]
          );
          
          let tagId;
          if (existingTags.length === 0) {
              const tagResult = await db.execute(
                'INSERT INTO script_tags (name, created_at) VALUES (?, NOW())',
                [tagName.trim()]
              );
              tagId = tagResult.insertId;
          } else {
            tagId = existingTags[0].id;
          }
          
          // 关联标签和话术
          await db.execute(
            'INSERT INTO script_item_tags (script_id, tag_id) VALUES (?, ?)',
            [scriptId, tagId]
          );
        }
      }
    }
    
    // 处理参数
    if (parameters && Array.isArray(parameters)) {
      for (const param of parameters) {
        if (param.parameter_name && param.parameter_name.trim()) {
          await db.execute(
            'INSERT INTO script_parameters (script_id, parameter_name, parameter_type, default_value, description) VALUES (?, ?, ?, ?, ?)',
            [
              String(scriptId),
              param.parameter_name.trim(),
              param.parameter_type || 'string',
              param.default_value || null,
              param.description || null
            ]
          );
        }
      }
    }
    
    await db.commitTransaction(connection);
    
    // 获取创建的话术信息
    const _newScript = await db.execute(
      `SELECT s.id, s.title, s.description, s.content, s.is_public, s.usage_count, s.created_at, s.updated_at,
              sc.name as category_name,
              u.display_name as creator_name
       FROM scripts s
       LEFT JOIN script_categories sc ON s.category_id = sc.id
       LEFT JOIN users u ON s.created_by = u.id
       WHERE s.id = ?`,
      [scriptId]
    );
    
    const newScript = _newScript[0];
    
    // 获取话术的标签
    const scriptTags = await db.execute(
      `SELECT st.id, st.name
       FROM script_tags st
       INNER JOIN script_item_tags sit ON st.id = sit.tag_id
       WHERE sit.script_id = ?`,
      [String(scriptId)]
    );
    newScript.tags = scriptTags;
    
    // 获取参数
  const scriptParameters = await db.execute(
    'SELECT parameter_name, parameter_type, default_value, description FROM script_parameters WHERE script_id = ? ORDER BY parameter_name',
      [scriptId]
    );
    newScript.parameters = scriptParameters;
    
    res.status(201).json(createSuccessResponse(newScript, '话术创建成功'));
  } catch (error) {
    await db.rollbackTransaction(connection);
    throw error;
  }
}));

/**
 * @route PUT /api/scripts/:id
 * @desc 更新话术
 * @access Private
 */
router.put('/:id', authenticateToken, asyncHandler(async (req, res) => {
  const scriptId = req.params.id;
  const { title, categoryId, description, content, isPublic, tags, parameters } = req.body;
  
  // 检查话术是否存在和权限
  const scripts = await db.execute(
    'SELECT created_by FROM scripts WHERE id = ?',
    [String(scriptId)]
  );
  
  if (scripts.length === 0) {
    throw new NotFoundError('话术不存在');
  }
  
  if (scripts[0].created_by !== req.user.id && req.user.role !== 'admin') {
    throw new AuthorizationError('无权修改此话术');
  }
  
  // 验证分类是否存在
  if (categoryId) {
    const categories = await db.execute(
      'SELECT id FROM script_categories WHERE id = ?',
      [String(categoryId)]
    );
    
    if (categories.length === 0) {
      throw new ValidationError('指定的分类不存在');
    }
  }
  
  // 检查标题是否与其他话术冲突（同一用户下）
  if (title) {
    const existing = await db.execute(
      'SELECT id FROM scripts WHERE title = ? AND created_by = ? AND id != ?',
      [title, req.user.id, scriptId]
    );
    
    if (existing.length > 0) {
      throw new ValidationError('您已有同名的话术');
    }
  }
  
  // 开始事务
  const connection = await db.getConnection();
  await db.beginTransaction(connection);
  
  try {
    // 更新基本信息
    const updateFields = [];
    const updateValues = [];
    
    if (title !== undefined) {
      updateFields.push('title = ?');
      updateValues.push(title);
    }
    
    if (categoryId !== undefined) {
      updateFields.push('category_id = ?');
      updateValues.push(String(categoryId));
    }
    
    if (description !== undefined) {
      updateFields.push('description = ?');
      updateValues.push(description);
    }
    
    if (content !== undefined) {
      updateFields.push('content = ?');
      updateValues.push(content);
    }
    
    if (isPublic !== undefined) {
      updateFields.push('is_public = ?');
      updateValues.push(isPublic);
    }
    
    if (updateFields.length > 0) {
      updateFields.push('updated_at = NOW()');
      updateValues.push(String(scriptId));
      
      await db.execute(
        `UPDATE scripts SET ${updateFields.join(', ')} WHERE id = ?`,
        updateValues,
        connection
      );
    }
    
    // 更新标签
    if (tags !== undefined) {
      // 删除现有标签关联
      await db.execute(
        'DELETE FROM script_item_tags WHERE script_id = ?',
        [String(scriptId)],
        connection
      );
      
      // 添加新标签
      if (tags && tags.length > 0) {
        for (const tagName of tags) {
          if (tagName.trim()) {
            // 查找或创建标签
            const existingTag = await db.execute(
              'SELECT id FROM script_tags WHERE name = ?',
              [tagName.trim()]
            );
            
            let tagId;
            if (existingTags.length === 0) {
               const tagResult = await db.execute(
                 'INSERT INTO script_tags (name, created_at) VALUES (?, NOW())',
                 [tagName.trim()],
                 connection
               );
               tagId = tagResult.insertId;
             } else {
               tagId = existingTags[0].id;
             }
            
            // 关联标签和话术
            await db.execute(
              'INSERT INTO script_item_tags (script_id, tag_id) VALUES (?, ?)',
              [String(scriptId), tagId],
              connection
            );
          }
        }
      }
    }
    
    // 更新参数
    if (parameters !== undefined) {
      // 删除现有参数
      await db.execute(
        'DELETE FROM script_parameters WHERE script_id = ?',
        [String(scriptId)],
        connection
      );
      
      // 添加新参数
      if (parameters && Array.isArray(parameters)) {
        for (const param of parameters) {
          if (param.parameter_name && param.parameter_name.trim()) {
            await db.execute(
              'INSERT INTO script_parameters (script_id, parameter_name, parameter_type, default_value, description) VALUES (?, ?, ?, ?, ?)',
              [
                scriptId,
                param.parameter_name.trim(),
                param.parameter_type || 'string',
                param.default_value || null,
                param.description || null
              ],
              connection
            );
          }
        }
      }
    }
    
    await db.commitTransaction(connection);
    
    res.json(createSuccessResponse(null, '话术更新成功'));
  } catch (error) {
    await db.rollbackTransaction(connection);
    throw error;
  }
}));

/**
 * @route DELETE /api/scripts/:id
 * @desc 删除话术
 * @access Private
 */
router.delete('/:id', authenticateToken, asyncHandler(async (req, res) => {
  const scriptId = req.params.id;
  let connection;

  try {
    connection = await db.getConnection();
    await db.beginTransaction(connection);

    // 检查话术是否存在和权限
    const scripts = await db.execute(
      'SELECT created_by FROM scripts WHERE id = ?',
      [scriptId],
      connection
    );
    
    if (scripts.length === 0) {
      throw new NotFoundError('话术不存在');
    }
    
    if (scripts[0].created_by !== req.user.id && req.user.role !== 'admin') {
      throw new AuthorizationError('无权删除此话术');
    }
    
    // 删除参数
    await db.execute(
      'DELETE FROM script_parameters WHERE script_id = ?',
      [String(scriptId)],
      connection
    );
    
    // 删除标签关联
    await db.execute(
      'DELETE FROM script_item_tags WHERE script_id = ?',
      [String(scriptId)],
      connection
    );
    
    // 删除消息引用
    await db.execute(
      'DELETE FROM message_references WHERE reference_type = "script" AND reference_id = ?',
      [String(scriptId)],
      connection
    );
    
    // 删除话术
    await db.execute(
      'DELETE FROM scripts WHERE id = ?',
      [String(scriptId)],
      connection
    );
    
    await db.commitTransaction(connection);
    
    res.json(createSuccessResponse(null, '话术删除成功'));
  } catch (error) {
    if (connection) {
      await db.rollbackTransaction(connection);
    }
    throw error;
  }
}));

/**
 * @route POST /api/scripts/:id/copy
 * @desc 复制话术
 * @access Private
 */
router.post('/:id/copy', authenticateToken, asyncHandler(async (req, res) => {
  const scriptId = req.params.id;
  const { title } = req.body;
  let connection;

  try {
    connection = await db.getConnection();
    await db.beginTransaction(connection);

    // 获取原话术信息
    const originalScripts = await db.execute(
      'SELECT title, category_id, description, content FROM scripts WHERE id = ? AND (is_public = true OR created_by = ?)',
      [String(scriptId), req.user.id],
      connection
    );
    
    if (originalScripts.length === 0) {
      throw new NotFoundError('话术不存在或无权访问');
    }
    
    const script = originalScripts[0];
    const newTitle = title || `${script.title} - 副本`;
    
    // 检查新标题是否已存在（同一用户下）
    const existing = await db.execute(
      'SELECT id FROM scripts WHERE title = ? AND created_by = ?',
      [newTitle, req.user.id],
      connection
    );
    
    if (existing.length > 0) {
      throw new ValidationError('您已有同名的话术');
    }
    
    // 创建副本
    const result = await db.execute(
      `INSERT INTO scripts (title, category_id, description, content, is_public, created_by, created_at, updated_at)
       VALUES (?, ?, ?, ?, false, ?, NOW(), NOW())`,
      [newTitle, script.category_id, script.description, script.content, req.user.id],
      connection
    );
    
    const newScriptId = result.insertId;
    
    // 复制标签
    const originalTags = await db.execute(
      `SELECT st.name
       FROM script_tags st
       INNER JOIN script_item_tags sit ON st.id = sit.tag_id
       WHERE sit.script_id = ?`,
      [String(scriptId)],
      connection
    );
    
    for (const tag of originalTags) {
      // 查找或创建标签
      let existingTags = await db.execute(
        'SELECT id FROM script_tags WHERE name = ?',
        [tag.name],
        connection
      );
      
      let tagId;
      if (existingTags.length === 0) {
        const [tagResult] = await db.execute(
          'INSERT INTO script_tags (name, created_at) VALUES (?, NOW())',
          [tag.name],
          connection
        );
        tagId = tagResult.insertId;
      } else {
        tagId = existingTags[0].id;
      }
      
      // 关联标签和新话术
      await db.execute(
        'INSERT INTO script_item_tags (script_id, tag_id) VALUES (?, ?)',
        [newScriptId, tagId],
        connection
      );
    }
    
    // 复制参数
    const originalParameters = await db.execute(
      'SELECT parameter_name, parameter_type, default_value, description FROM script_parameters WHERE script_id = ?',
      [scriptId],
      connection
    );
    
    for (const param of originalParameters) {
      await db.execute(
        'INSERT INTO script_parameters (script_id, parameter_name, parameter_type, default_value, description) VALUES (?, ?, ?, ?, ?)',
        [newScriptId, param.parameter_name, param.parameter_type, param.default_value, param.description],
        connection
      );
    }
    
    await db.commitTransaction();
    
    // 获取创建的话术信息
    const newScripts = await db.execute(
      `SELECT s.id, s.title, s.description, s.content, s.is_public, s.usage_count, s.created_at, s.updated_at,
              sc.name as category_name,
              u.display_name as creator_name
       FROM scripts s
       LEFT JOIN script_categories sc ON s.category_id = sc.id
       LEFT JOIN users u ON s.created_by = u.id
       WHERE s.id = ?`,
      [newScriptId]
    );
    
    const newScript = newScripts[0];
    
    // 获取标签
    const scriptTags = await db.execute(
      `SELECT st.id, st.name
       FROM script_tags st
       INNER JOIN script_item_tags sit ON st.id = sit.tag_id
       WHERE sit.script_id = ?`,
      [newScriptId]
    );
    newScript.tags = scriptTags;
    
    // 获取参数
    const scriptParameters = await db.execute(
      'SELECT parameter_name, parameter_type, default_value, description FROM script_parameters WHERE script_id = ? ORDER BY parameter_name',
      [newScriptId]
    );
    newScript.parameters = scriptParameters;
    
    res.status(201).json(createSuccessResponse(newScript, '话术复制成功'));
  } catch (error) {
    await db.rollbackTransaction();
    throw error;
  }
}));

// ==================== 标签管理 ====================

/**
 * @route GET /api/scripts/tags
 * @desc 获取话术库标签列表
 * @access Private
 */
router.get('/tags', authenticateToken, asyncHandler(async (req, res) => {
  const tags = await db.execute(
    `SELECT st.id, st.name, st.created_at,
            COUNT(sit.script_id) as usage_count
     FROM script_tags st
     LEFT JOIN script_item_tags sit ON st.id = sit.tag_id
     GROUP BY st.id
     ORDER BY usage_count DESC, st.name`
  );
  
  res.json(createSuccessResponse(tags));
}));

/**
 * @route GET /api/scripts/stats
 * @desc 获取话术库统计信息
 * @access Private
 */
router.get('/stats', authenticateToken, asyncHandler(async (req, res) => {
  const userId = req.user.id;
  
  // 获取统计信息
  const stats = await db.execute(
    `SELECT 
       (SELECT COUNT(*) FROM scripts WHERE is_public = true OR created_by = ?) as total_scripts,
       (SELECT COUNT(*) FROM scripts WHERE created_by = ?) as my_scripts,
       (SELECT COUNT(*) FROM scripts WHERE is_public = true) as public_scripts,
       (SELECT COUNT(*) FROM script_categories) as total_categories,
       (SELECT COUNT(*) FROM script_tags) as total_tags`,
    [userId, userId]
  );
  
  // 获取分类统计
  const categoryStats = await db.execute(
    `SELECT sc.name, COUNT(s.id) as script_count
     FROM script_categories sc
     LEFT JOIN scripts s ON sc.id = s.category_id AND (s.is_public = true OR s.created_by = ?)
     GROUP BY sc.id
     ORDER BY script_count DESC
     LIMIT 10`,
    [userId]
  );
  
  // 获取最近创建的话术
  const recentScripts = await db.execute(
    `SELECT s.id, s.title, s.is_public, s.created_at,
            sc.name as category_name,
            u.display_name as creator_name
     FROM scripts s
     LEFT JOIN script_categories sc ON s.category_id = sc.id
     LEFT JOIN users u ON s.created_by = u.id
     WHERE s.is_public = true OR s.created_by = ?
     ORDER BY s.created_at DESC
     LIMIT 5`,
    [userId]
  );
  
  // 获取热门话术
  const popularScripts = await db.execute(
    `SELECT s.id, s.title, s.usage_count, s.is_public,
            sc.name as category_name,
            u.display_name as creator_name
     FROM scripts s
     LEFT JOIN script_categories sc ON s.category_id = sc.id
     LEFT JOIN users u ON s.created_by = u.id
     WHERE s.is_public = true OR s.created_by = ?
     ORDER BY s.usage_count DESC
     LIMIT 5`,
    [userId]
  );
  
  res.json(createSuccessResponse({
    overview: stats[0],
    categoryStats,
    recentScripts,
    popularScripts
  }));
}));

module.exports = router;