/**
 * 用户管理路由
 * 
 * 功能说明：
 * - 用户个人资料管理
 * - 用户设置管理
 * - 头像上传
 * - 用户列表管理（管理员功能）
 */

const express = require('express');
const multer = require('multer');
const path = require('path');
const fs = require('fs');
const router = express.Router();

const db = require('../config/database');
const { validate, userUpdateSchema, userSettingsSchema, paginationSchema } = require('../middleware/validation');
const { authenticateToken, requireAdmin } = require('../middleware/auth');
const { asyncHandler, ValidationError, NotFoundError, ConflictError, createSuccessResponse } = require('../middleware/errorHandler');

// 配置文件上传
const storage = multer.diskStorage({
  destination: (req, file, cb) => {
    const uploadDir = path.join(__dirname, '../uploads/avatars');
    if (!fs.existsSync(uploadDir)) {
      fs.mkdirSync(uploadDir, { recursive: true });
    }
    cb(null, uploadDir);
  },
  filename: (req, file, cb) => {
    const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
    cb(null, `avatar-${req.user.id}-${uniqueSuffix}${path.extname(file.originalname)}`);
  }
});

const upload = multer({
  storage,
  limits: {
    fileSize: parseInt(process.env.MAX_FILE_SIZE) || 5 * 1024 * 1024 // 5MB
  },
  fileFilter: (req, file, cb) => {
    const allowedTypes = ['image/jpeg', 'image/png', 'image/gif', 'image/webp'];
    if (allowedTypes.includes(file.mimetype)) {
      cb(null, true);
    } else {
      cb(new ValidationError('只支持 JPEG、PNG、GIF、WebP 格式的图片'));
    }
  }
});

/**
 * @route GET /api/users/profile
 * @desc 获取当前用户个人资料
 * @access Private
 */
router.get('/profile', authenticateToken, asyncHandler(async (req, res) => {
  const users = await db.execute(
    `SELECT u.id, u.username, u.display_name, u.role, u.status, u.avatar_url, 
            u.created_at, u.updated_at, u.last_login_at,
            s.theme, s.primary_color, s.font_size, s.animations, s.language, s.date_format,
            s.desktop_notifications, s.sound_notifications, s.default_chat_model, s.default_image_model
     FROM users u
     LEFT JOIN user_settings s ON u.id = s.user_id
     WHERE u.id = ?`,
    [req.user.id]
  );
  
  if (users.length === 0) {
    throw new NotFoundError('用户不存在');
  }
  
  const user = users[0];
  
  res.json(createSuccessResponse({
    id: user.id,
    username: user.username,
    displayName: user.display_name,
    role: user.role,
    status: user.status,
    avatarUrl: user.avatar_url,
    createdAt: user.created_at,
    updatedAt: user.updated_at,
    lastLoginAt: user.last_login_at,
    settings: {
      theme: user.theme,
      primaryColor: user.primary_color,
      fontSize: user.font_size,
      animations: user.animations,
      language: user.language,
      dateFormat: user.date_format,
      desktopNotifications: user.desktop_notifications,
      soundNotifications: user.sound_notifications,
      defaultChatModel: user.default_chat_model,
      defaultImageModel: user.default_image_model
    }
  }));
}));

/**
 * @route PUT /api/users/profile
 * @desc 更新用户个人资料
 * @access Private
 */
router.put('/profile', authenticateToken, validate(userUpdateSchema), asyncHandler(async (req, res) => {
  const { displayName, avatarUrl, currentPassword, newPassword } = req.body;
  const userId = req.user.id;
  let connection;

  try {
    connection = await db.getConnection();
    await db.beginTransaction(connection);

    // 如果要修改密码，验证当前密码
    if (newPassword) {
      if (!currentPassword) {
        throw new ValidationError('修改密码时需要提供当前密码');
      }
      
      const users = await db.execute(
        'SELECT password_hash FROM users WHERE id = ?',
        [userId],
        connection
      );
      
      const bcrypt = require('bcryptjs');
      const isCurrentPasswordValid = await bcrypt.compare(currentPassword, users[0].password_hash);
      if (!isCurrentPasswordValid) {
        throw new ValidationError('当前密码错误');
      }
      
      // 加密新密码
      const saltRounds = parseInt(process.env.BCRYPT_ROUNDS) || 12;
      const hashedNewPassword = await bcrypt.hash(newPassword, saltRounds);
      
      await db.execute(
        'UPDATE users SET password_hash = ?, updated_at = NOW() WHERE id = ?',
        [hashedNewPassword, userId],
        connection
      );
    }
    
    // 更新其他信息
    const updateFields = [];
    const updateValues = [];
    
    if (displayName !== undefined) {
      updateFields.push('display_name = ?');
      updateValues.push(displayName);
    }
    
    if (avatarUrl !== undefined) {
      updateFields.push('avatar_url = ?');
      updateValues.push(avatarUrl);
    }
    
    if (updateFields.length > 0) {
      updateFields.push('updated_at = NOW()');
      updateValues.push(userId);
      
      await db.execute(
        `UPDATE users SET ${updateFields.join(', ')} WHERE id = ?`,
        updateValues,
        connection
      );
    }
    
    await db.commitTransaction(connection);

    // 获取更新后的用户信息
    const updatedUsers = await db.execute(
      `SELECT u.id, u.username, u.display_name, u.role, u.status, u.avatar_url, 
              u.created_at, u.updated_at, u.last_login_at
       FROM users u WHERE u.id = ?`,
      [userId]
    );
    
    res.json(createSuccessResponse(updatedUsers[0], '个人资料更新成功'));
  } catch (error) {
    if (connection) {
      await db.rollbackTransaction(connection);
    }
    throw error;
  }
}));

/**
 * @route POST /api/users/avatar
 * @desc 上传用户头像
 * @access Private
 */
router.post('/avatar', authenticateToken, upload.single('avatar'), asyncHandler(async (req, res) => {
  if (!req.file) {
    throw new ValidationError('请选择要上传的头像文件');
  }
  
  const avatarUrl = `/uploads/avatars/${req.file.filename}`;
  let connection;

  try {
    connection = await db.getConnection();
    await db.beginTransaction(connection);

    // 删除旧头像文件
    const users = await db.execute(
      'SELECT avatar_url FROM users WHERE id = ?',
      [req.user.id],
      connection
    );
    
    if (users[0].avatar_url) {
      const oldAvatarPath = path.join(__dirname, '../', users[0].avatar_url);
      if (fs.existsSync(oldAvatarPath)) {
        fs.unlinkSync(oldAvatarPath);
      }
    }
    
    // 更新数据库中的头像URL
    await db.execute(
      'UPDATE users SET avatar_url = ?, updated_at = NOW() WHERE id = ?',
      [avatarUrl, req.user.id],
      connection
    );
    
    await db.commitTransaction(connection);

    res.json(createSuccessResponse({
      avatarUrl
    }, '头像上传成功'));
  } catch (error) {
    if (connection) {
      await db.rollbackTransaction(connection);
    }
    throw error;
  }
}));

/**
 * @route GET /api/users/settings
 * @desc 获取用户设置
 * @access Private
 */
router.get('/settings', authenticateToken, asyncHandler(async (req, res) => {
  const settings = await db.execute(
    `SELECT theme, primary_color, font_size, animations, language, date_format,
            desktop_notifications, sound_notifications, default_chat_model, default_image_model
     FROM user_settings WHERE user_id = ?`,
    [req.user.id]
  );
  
  if (settings.length === 0) {
    // 如果没有设置记录，创建默认设置
    await db.execute(
      `INSERT INTO user_settings (user_id, theme, primary_color, font_size, animations, language, 
       date_format, desktop_notifications, sound_notifications, created_at, updated_at) 
       VALUES (?, 'light', 'blue', 14, true, 'zh-CN', 'YYYY-MM-DD', true, true, NOW(), NOW())`,
      [req.user.id]
    );
    
    return res.json(createSuccessResponse({
      theme: 'light',
      primaryColor: 'blue',
      fontSize: 14,
      animations: true,
      language: 'zh-CN',
      dateFormat: 'YYYY-MM-DD',
      desktopNotifications: true,
      soundNotifications: true,
      defaultChatModel: null,
      defaultImageModel: null
    }));
  }
  
  const userSettings = settings[0];
  
  res.json(createSuccessResponse({
    theme: userSettings.theme,
    primaryColor: userSettings.primary_color,
    fontSize: userSettings.font_size,
    animations: userSettings.animations,
    language: userSettings.language,
    dateFormat: userSettings.date_format,
    desktopNotifications: userSettings.desktop_notifications,
    soundNotifications: userSettings.sound_notifications,
    defaultChatModel: userSettings.default_chat_model,
    defaultImageModel: userSettings.default_image_model
  }));
}));

/**
 * @route PUT /api/users/settings
 * @desc 更新用户设置
 * @access Private
 */
router.put('/settings', authenticateToken, validate(userSettingsSchema), asyncHandler(async (req, res) => {
  const {
    theme, primaryColor, fontSize, animations, language, dateFormat,
    desktopNotifications, soundNotifications, defaultChatModel, defaultImageModel
  } = req.body;
  let connection;

  try {
    connection = await db.getConnection();
    await db.beginTransaction(connection);

    // 检查是否已有设置记录
    const existingSettings = await db.execute(
      'SELECT id FROM user_settings WHERE user_id = ?',
      [req.user.id],
      connection
    );
    
    if (existingSettings.length === 0) {
      // 创建新的设置记录
      await db.execute(
        `INSERT INTO user_settings (user_id, theme, primary_color, font_size, animations, language, 
         date_format, desktop_notifications, sound_notifications, default_chat_model, default_image_model,
         created_at, updated_at) 
         VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, NOW(), NOW())`,
        [req.user.id, theme, primaryColor, fontSize, animations, language, dateFormat,
         desktopNotifications, soundNotifications, defaultChatModel, defaultImageModel],
        connection
      );
    } else {
      // 更新现有设置
      const updateFields = [];
      const updateValues = [];
      
      if (theme !== undefined) {
        updateFields.push('theme = ?');
        updateValues.push(theme);
      }
      if (primaryColor !== undefined) {
        updateFields.push('primary_color = ?');
        updateValues.push(primaryColor);
      }
      if (fontSize !== undefined) {
        updateFields.push('font_size = ?');
        updateValues.push(fontSize);
      }
      if (animations !== undefined) {
        updateFields.push('animations = ?');
        updateValues.push(animations);
      }
      if (language !== undefined) {
        updateFields.push('language = ?');
        updateValues.push(language);
      }
      if (dateFormat !== undefined) {
        updateFields.push('date_format = ?');
        updateValues.push(dateFormat);
      }
      if (desktopNotifications !== undefined) {
        updateFields.push('desktop_notifications = ?');
        updateValues.push(desktopNotifications);
      }
      if (soundNotifications !== undefined) {
        updateFields.push('sound_notifications = ?');
        updateValues.push(soundNotifications);
      }
      if (defaultChatModel !== undefined) {
        updateFields.push('default_chat_model = ?');
        updateValues.push(defaultChatModel);
      }
      if (defaultImageModel !== undefined) {
        updateFields.push('default_image_model = ?');
        updateValues.push(defaultImageModel);
      }
      
      if (updateFields.length > 0) {
        updateFields.push('updated_at = NOW()');
        updateValues.push(req.user.id);
        
        await db.execute(
          `UPDATE user_settings SET ${updateFields.join(', ')} WHERE user_id = ?`,
          updateValues,
          connection
        );
      }
    }
    
    await db.commitTransaction(connection);

    res.json(createSuccessResponse(null, '设置更新成功'));
  } catch (error) {
    if (connection) {
      await db.rollbackTransaction(connection);
    }
    throw error;
  }
}));

/**
 * @route GET /api/users
 * @desc 获取用户列表（管理员功能）
 * @access Private (Admin only)
 */
router.get('/', authenticateToken, requireAdmin, validate(paginationSchema, 'query'), asyncHandler(async (req, res) => {
  const { page, limit, sortBy, sortOrder, search } = req.query;
  const offset = (page - 1) * limit;
  
  // 构建查询条件
  let whereClause = '';
  let queryParams = [];
  
  if (search) {
    whereClause = 'WHERE (u.username LIKE ? OR u.display_name LIKE ?)';
    queryParams.push(`%${search}%`, `%${search}%`);
  }
  
  // 获取用户列表
  const users = await db.execute(
    `SELECT u.id, u.username, u.display_name, u.role, u.status, u.avatar_url, 
            u.created_at, u.updated_at, u.last_login_at,
            COUNT(cs.id) as session_count
     FROM users u
     LEFT JOIN chat_sessions cs ON u.id = cs.user_id
     ${whereClause}
     GROUP BY u.id
     ORDER BY ${sortBy} ${sortOrder}
     LIMIT ? OFFSET ?`,
    [...queryParams, String(limit), String(offset)]
  );
  
  // 获取总数
  const countResult = await db.execute(
    `SELECT COUNT(DISTINCT u.id) as total FROM users u ${whereClause}`,
    queryParams
  );
  
  const total = countResult[0].total;
  const totalPages = Math.ceil(total / limit);
  
  res.json(createSuccessResponse(users, '获取用户列表成功', {
    pagination: {
      page,
      limit,
      total,
      totalPages,
      hasNext: page < totalPages,
      hasPrev: page > 1
    }
  }));
}));

/**
 * @route GET /api/users/:id
 * @desc 获取指定用户信息（管理员功能）
 * @access Private (Admin only)
 */
router.get('/:id', authenticateToken, requireAdmin, asyncHandler(async (req, res) => {
  const userId = req.params.id;
  
  const users = await db.execute(
    `SELECT u.id, u.username, u.display_name, u.role, u.status, u.avatar_url, 
            u.created_at, u.updated_at, u.last_login_at,
            s.theme, s.primary_color, s.font_size, s.animations, s.language, s.date_format,
            s.desktop_notifications, s.sound_notifications, s.default_chat_model, s.default_image_model
     FROM users u
     LEFT JOIN user_settings s ON u.id = s.user_id
     WHERE u.id = ?`,
    [userId]
  );
  
  if (users.length === 0) {
    throw new NotFoundError('用户不存在');
  }
  
  const user = users[0];
  
  // 获取用户统计信息
  const stats = await db.execute(
    `SELECT 
       (SELECT COUNT(*) FROM chat_sessions WHERE user_id = ?) as session_count,
       (SELECT COUNT(*) FROM chat_messages WHERE user_id = ?) as message_count,
       (SELECT COUNT(*) FROM knowledge_items WHERE created_by = ?) as knowledge_count,
       (SELECT COUNT(*) FROM scripts WHERE created_by = ?) as script_count`,
    [userId, userId, userId, userId]
  );
  
  res.json(createSuccessResponse({
    ...user,
    settings: {
      theme: user.theme,
      primaryColor: user.primary_color,
      fontSize: user.font_size,
      animations: user.animations,
      language: user.language,
      dateFormat: user.date_format,
      desktopNotifications: user.desktop_notifications,
      soundNotifications: user.sound_notifications,
      defaultChatModel: user.default_chat_model,
      defaultImageModel: user.default_image_model
    },
    stats: stats[0]
  }));
}));

/**
 * @route PUT /api/users/:id/status
 * @desc 更新用户状态（管理员功能）
 * @access Private (Admin only)
 */
router.put('/:id/status', authenticateToken, requireAdmin, asyncHandler(async (req, res) => {
  const userId = req.params.id;
  const { status } = req.body;
  let connection;

  try {
    connection = await db.getConnection();
    await db.beginTransaction(connection);

    if (!['active', 'inactive', 'banned'].includes(status)) {
      throw new ValidationError('状态值无效');
    }
    
    // 不能修改自己的状态
    if (parseInt(userId) === req.user.id) {
      throw new ValidationError('不能修改自己的状态');
    }
    
    await db.execute(
      'UPDATE users SET status = ?, updated_at = NOW() WHERE id = ?',
      [status, userId],
      connection
    );
    
    await db.commitTransaction(connection);

    res.json(createSuccessResponse(null, '用户状态更新成功'));
  } catch (error) {
    if (connection) {
      await db.rollbackTransaction(connection);
    }
    throw error;
  }
}));

/**
 * @route POST /api/users
 * @desc 创建新用户 (管理员功能)
 * @access Private (Admin only)
 */
router.post('/', authenticateToken, requireAdmin, asyncHandler(async (req, res) => {
  const { username, password, display_name, role, status } = req.body;

  if (!username || !password || !display_name) {
    throw new ValidationError('用户名、密码和显示名称是必填项');
  }

  // 检查用户名是否已存在
  const existingUsers = await db.execute('SELECT id FROM users WHERE username = ?', [username]);
  if (existingUsers.length > 0) {
    throw new ConflictError('用户名已存在');
  }

  // 加密密码
  const bcrypt = require('bcryptjs');
  const saltRounds = parseInt(process.env.BCRYPT_ROUNDS) || 12;
  const password_hash = await bcrypt.hash(password, saltRounds);

  const result = await db.execute(
    `INSERT INTO users (username, password_hash, display_name, role, status, created_at, updated_at) 
     VALUES (?, ?, ?, ?, ?, NOW(), NOW())`,
    [username, password_hash, display_name, role || 'user', status || 'active']
  );

  const newUser = {
    id: result.insertId,
    username,
    displayName: display_name,
    role: role || 'user',
    status: status || 'active'
  };

  res.status(201).json(createSuccessResponse(newUser, '用户创建成功'));
}));

/**
 * @route PUT /api/users/:id
 * @desc 更新用户信息 (管理员功能)
 * @access Private (Admin only)
 */
router.put('/:id', authenticateToken, requireAdmin, asyncHandler(async (req, res) => {
  const userId = req.params.id;
  const { display_name, role, status, password } = req.body;

  // 检查用户是否存在
  const users = await db.execute('SELECT id FROM users WHERE id = ?', [userId]);
  if (users.length === 0) {
    throw new NotFoundError('用户不存在');
  }

  const updateFields = [];
  const updateValues = [];

  if (display_name) {
    updateFields.push('display_name = ?');
    updateValues.push(display_name);
  }
  if (role) {
    updateFields.push('role = ?');
    updateValues.push(role);
  }
  if (status) {
    updateFields.push('status = ?');
    updateValues.push(status);
  }
  if (password) {
    const bcrypt = require('bcryptjs');
    const saltRounds = parseInt(process.env.BCRYPT_ROUNDS) || 12;
    const password_hash = await bcrypt.hash(password, saltRounds);
    updateFields.push('password_hash = ?');
    updateValues.push(password_hash);
  }

  if (updateFields.length === 0) {
    return res.json(createSuccessResponse(null, '没有提供需要更新的信息'));
  }

  updateFields.push('updated_at = NOW()');
  updateValues.push(userId);

  await db.execute(
    `UPDATE users SET ${updateFields.join(', ')} WHERE id = ?`,
    updateValues
  );

  res.json(createSuccessResponse(null, '用户信息更新成功'));
}));

/**
 * @route DELETE /api/users/:id
 * @desc 删除用户 (管理员功能)
 * @access Private (Admin only)
 */
router.delete('/:id', authenticateToken, requireAdmin, asyncHandler(async (req, res) => {
  const userId = req.params.id;

  if (Number(req.user.id) === Number(userId)) {
    throw new ValidationError('不能删除自己');
  }

  // 检查用户是否存在
  const users = await db.execute('SELECT id FROM users WHERE id = ?', [userId]);
  if (users.length === 0) {
    throw new NotFoundError('用户不存在');
  }
  
  // 在事务中执行删除操作
  const connection = await db.getConnection();
  try {
    await db.beginTransaction(connection);
    await db.execute('DELETE FROM user_settings WHERE user_id = ?', [userId], connection);
    // 可根据需要删除其他关联数据
    await db.execute('DELETE FROM users WHERE id = ?', [userId], connection);
    await db.commitTransaction(connection);
    res.json(createSuccessResponse(null, '用户删除成功'));
  } catch(error) {
    await db.rollbackTransaction(connection);
    throw error;
  }
}));

module.exports = router;