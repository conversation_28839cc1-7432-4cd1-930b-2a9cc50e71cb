/**
 * AI绘图路由
 * 基于 aihuitu.spbst.cn 的API
 */

const express = require('express');
const router = express.Router();
const path = require('path');
const fs = require('fs').promises;
const AiHuituService = require('../services/aiHuituService');

// 创建AI绘图服务实例
const aiHuituService = new AiHuituService();

// 生成图片
router.post('/generate', async (req, res) => {
  try {
    const { prompt, options = {} } = req.body;
    
    if (!prompt) {
      return res.status(400).json({
        success: false,
        error: '请提供生图提示词'
      });
    }
    
    console.log('🎨 收到AI绘图请求:', prompt);
    
    // 调用AI绘图服务
    const result = await aiHuituService.generateImage(prompt, options);
    
    if (result.success) {
      res.json({
        success: true,
        message: 'AI绘图任务完成',
        data: {
          taskId: result.taskId,
          prompt: result.prompt,
          fullPrompt: result.fullPrompt,
          images: result.images,
          imageUrls: result.imageUrls,
          buttons: result.buttons,
          jobId: result.jobId,
          finishTime: result.finishTime
        }
      });
    } else {
      res.status(500).json({
        success: false,
        error: result.error || 'AI绘图失败'
      });
    }
    
  } catch (error) {
    console.error('❌ AI绘图路由错误:', error);
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

// 获取生成历史
router.get('/history', async (req, res) => {
  try {
    const limit = parseInt(req.query.limit) || 50;
    const result = await aiHuituService.getHistory(limit);
    
    res.json(result);
    
  } catch (error) {
    console.error('❌ 获取AI绘图历史失败:', error);
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

// 获取服务状态
router.get('/status', async (req, res) => {
  try {
    const hasToken = !!aiHuituService.token;

    res.json({
      success: true,
      status: {
        configured: true,
        loggedIn: hasToken,
        baseUrl: aiHuituService.baseUrl,
        username: aiHuituService.credentials.username
      }
    });

  } catch (error) {
    console.error('❌ 获取AI绘图状态失败:', error);
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

// 测试连接
router.post('/test', async (req, res) => {
  try {
    console.log('🧪 测试AI绘图服务连接');

    // 测试登录
    const loginSuccess = await aiHuituService.login();
    if (!loginSuccess) {
      return res.status(500).json({
        success: false,
        error: '登录失败'
      });
    }

    res.json({
      success: true,
      message: 'AI绘图服务连接测试成功',
      status: {
        login: true,
        service: 'ready'
      }
    });

  } catch (error) {
    console.error('❌ AI绘图服务测试失败:', error);
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

// 重新登录
router.post('/login', async (req, res) => {
  try {
    console.log('🔐 重新登录AI绘图服务');
    
    const success = await aiHuituService.login();
    
    if (success) {
      res.json({
        success: true,
        message: '登录成功'
      });
    } else {
      res.status(500).json({
        success: false,
        error: '登录失败'
      });
    }
    
  } catch (error) {
    console.error('❌ 重新登录失败:', error);
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

// 获取本地图片
router.get('/images/:filename', async (req, res) => {
  try {
    const filename = req.params.filename;
    const imagePath = path.join(aiHuituService.imagesDir, filename);
    
    // 检查文件是否存在
    await fs.access(imagePath);
    
    // 设置正确的Content-Type
    res.setHeader('Content-Type', 'image/png');
    
    // 发送文件
    res.sendFile(imagePath);
    
  } catch (error) {
    console.error('❌ 获取图片失败:', error);
    res.status(404).json({
      success: false,
      error: '图片不存在'
    });
  }
});

// 优雅关闭
process.on('SIGINT', async () => {
  console.log('🔌 正在关闭AI绘图服务...');
  await aiHuituService.close();
  process.exit(0);
});

process.on('SIGTERM', async () => {
  console.log('🔌 正在关闭AI绘图服务...');
  await aiHuituService.close();
  process.exit(0);
});

module.exports = router;
