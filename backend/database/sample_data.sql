-- SCAIA平台示例数据插入脚本
-- 创建日期: 2024-01-01
-- 描述: 插入前端示例数据到数据库

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- 插入AI模型数据
INSERT IGNORE INTO `ai_models` (`id`, `name`, `type`, `provider`, `api_endpoint`, `max_tokens`, `temperature`, `is_active`) VALUES
('deepseek', 'deepseek-chat', 'chat', 'deepseek', 'https://api.deepseek.com/v1/chat/completions', 4096, 0.70, 1),
('deepseek-reasoner', 'deepseek-reasoner', 'chat', 'deepseek', 'https://api.deepseek.com/v1/chat/completions', 8192, 0.70, 1),
('doubao', '豆包 (Do<PERSON><PERSON>)', 'chat', 'doubao', 'https://api.doubao.com/v1/chat/completions', 4096, 0.70, 1),
('custom', '自定义模型', 'chat', 'custom', 'https://api.custom.com/v1/chat/completions', 4096, 0.70, 1);

-- 插入知识库分类
INSERT IGNORE INTO `knowledge_categories` (`id`, `name`, `description`, `created_by`) VALUES
(2, '建筑设计', '建筑设计相关知识', 1),
(3, '室内设计', '室内设计相关知识', 1),
(4, '景观设计', '景观设计相关知识', 1),
(5, '城市规划', '城市规划相关知识', 1);

-- 插入知识库项目
INSERT IGNORE INTO `knowledge_items` (`id`, `title`, `content`, `category_id`, `file_name`, `file_type`, `file_size`, `is_public`, `created_by`) VALUES
(1, '建筑设计规范2023版', '最新的建筑设计规范，包含住宅、商业和公共建筑的设计标准和要求。\n\n第一章 总则\n1.1 为规范建筑设计活动，保证建筑工程质量和安全，满足使用功能要求，制定本规范。\n1.2 本规范适用于新建、扩建、改建的民用建筑设计。\n1.3 建筑设计应遵循安全、适用、经济、绿色、美观的原则。', 2, '建筑设计规范2023版.pdf', 'pdf', 4500000, 1, 1),
(2, '室内设计手册', '室内设计的基础理论和实践指南，涵盖空间规划、材料选择、色彩搭配等内容。', 3, '室内设计手册.pdf', 'pdf', 3200000, 1, 1),
(3, '景观设计指南', '景观设计的原理和方法，包括植物配置、地形设计、水景设计等。', 4, '景观设计指南.pdf', 'pdf', 2800000, 1, 1),
(4, '城市规划基础理论', '城市规划的基本理论和方法，涵盖土地利用、交通规划、环境保护等。', 5, '城市规划基础理论.pdf', 'pdf', 5100000, 1, 1);

-- 插入知识库标签
INSERT IGNORE INTO `knowledge_tags` (`id`, `name`, `color`) VALUES
(1, '规范', '#007bff'),
(2, '标准', '#28a745'),
(3, '建筑设计', '#dc3545'),
(4, '室内设计', '#ffc107'),
(5, '景观设计', '#17a2b8'),
(6, '城市规划', '#6f42c1');

-- 插入知识库项目标签关联
INSERT IGNORE INTO `knowledge_item_tags` (`item_id`, `tag_id`) VALUES
(1, 1), (1, 2), (1, 3),
(2, 4),
(3, 5),
(4, 6);

-- 插入大师库分类
INSERT IGNORE INTO `master_categories` (`id`, `name`, `description`, `created_by`) VALUES
(2, '建筑大师', '著名建筑设计师', 1),
(3, '设计理论家', '设计理论研究者', 1),
(4, '当代设计师', '当代活跃设计师', 1);

-- 插入大师库项目
INSERT IGNORE INTO `master_items` (`id`, `name`, `description`, `category_id`, `system_prompt`, `is_public`, `created_by`) VALUES
(1, '贝聿铭', '华裔美籍建筑师，现代主义建筑大师，以简洁的几何形体与光影的精妙运用著称。代表作品包括卢浮宫玻璃金字塔、肯尼迪图书馆等。', 2, '你是贝聿铭，一位享誉世界的建筑大师。你的设计理念强调简洁的几何形体与光影的精妙运用，追求建筑与环境的和谐统一。请以贝聿铭的身份和视角来回答问题，分享你的设计理念和经验。', 1, 1),
(2, '扎哈·哈迪德', '英国-伊拉克建筑师，解构主义建筑代表人物，以流动的空间与参数化设计闻名。代表作品包括广州大剧院、北京大兴国际机场等。', 2, '你是扎哈·哈迪德，一位革命性的建筑师。你的设计以流动的空间形态和参数化设计著称，挑战传统建筑的边界。请以扎哈的身份来回答问题，展现你对未来建筑的独特见解。', 1, 1),
(3, '安藤忠雄', '日本建筑师，以清水混凝土建筑和光影设计著称。代表作品包括光之教堂、水之教堂等，强调建筑与自然的对话。', 2, '你是安藤忠雄，一位追求建筑诗意的日本建筑师。你擅长运用清水混凝土和自然光线，创造宁静而富有精神性的空间。请以安藤忠雄的身份来回答问题，分享你对建筑与自然关系的理解。', 1, 1),
(4, '隈研吾', '日本建筑师，以"负建筑"理念和材料的诗意表达著称。代表作品包括国立竞技场、根津美术馆等，强调建筑与地域文化的融合。', 2, '你是隈研吾，一位倡导"负建筑"理念的日本建筑师。你善于运用传统材料创造现代空间，强调建筑与地域文化的深度融合。请以隈研吾的身份来回答问题，阐述你的设计哲学。', 1, 1);

-- 插入大师库标签
INSERT IGNORE INTO `master_tags` (`id`, `name`, `color`) VALUES
(1, '现代主义', '#28a745'),
(2, '解构主义', '#dc3545'),
(3, '极简主义', '#6c757d'),
(4, '参数化设计', '#17a2b8'),
(5, '可持续设计', '#28a745'),
(6, '地域性设计', '#ffc107');

-- 插入大师库项目标签关联
INSERT IGNORE INTO `master_item_tags` (`item_id`, `tag_id`) VALUES
(1, 1), (1, 3),
(2, 2), (2, 4),
(3, 1), (3, 3),
(4, 5), (4, 6);

-- 插入图片库分类
INSERT IGNORE INTO `image_categories` (`id`, `name`, `description`, `created_by`) VALUES
(2, '建筑设计', '建筑设计相关图片', 1),
(3, '室内设计', '室内设计相关图片', 1),
(4, '景观设计', '景观设计相关图片', 1),
(5, '产品设计', '产品设计相关图片', 1),
(6, '概念艺术', '概念艺术相关图片', 1),
(7, '参考图像', '设计参考图像', 1);

-- 插入图片库项目
INSERT IGNORE INTO `image_items` (`id`, `title`, `description`, `category_id`, `file_name`, `file_path`, `file_size`, `file_type`, `width`, `height`, `is_public`, `created_by`) VALUES
(1, '现代简约客厅设计', '现代简约风格客厅，宽敞明亮，白色墙面，灰色沙发，木质地板', 3, 'modern_living_room.jpg', '/uploads/images/modern_living_room.jpg', 1024000, 'image/jpeg', 1920, 1080, 1, 1),
(2, '城市公园景观规划', '城市中心公园鸟瞰图，绿色植被丰富，蜿蜒的步道', 4, 'city_park_landscape.jpg', '/uploads/images/city_park_landscape.jpg', 2048000, 'image/jpeg', 2560, 1440, 1, 1),
(3, '现代办公建筑外观', '现代风格办公建筑，玻璃幕墙，简洁线条', 2, 'modern_office_building.jpg', '/uploads/images/modern_office_building.jpg', 1536000, 'image/jpeg', 1920, 1280, 1, 1),
(4, '极简主义产品设计', '极简风格的电子产品设计，注重功能性和美观性', 5, 'minimalist_product.jpg', '/uploads/images/minimalist_product.jpg', 768000, 'image/jpeg', 1200, 800, 1, 1);

-- 插入图片库标签
INSERT IGNORE INTO `image_tags` (`id`, `name`, `color`) VALUES
(1, '现代', '#007bff'),
(2, '简约', '#6c757d'),
(3, '客厅', '#28a745'),
(4, '室内', '#ffc107'),
(5, '公园', '#17a2b8'),
(6, '景观', '#20c997'),
(7, '城市', '#6f42c1'),
(8, '规划', '#e83e8c'),
(9, '建筑', '#fd7e14'),
(10, '办公', '#dc3545'),
(11, '产品', '#198754'),
(12, '极简', '#adb5bd');

-- 插入图片库项目标签关联
INSERT IGNORE INTO `image_item_tags` (`item_id`, `tag_id`) VALUES
(1, 1), (1, 2), (1, 3), (1, 4),
(2, 5), (2, 6), (2, 7), (2, 8),
(3, 1), (3, 9), (3, 10),
(4, 2), (4, 11), (4, 12);

-- 插入提示词库分类
INSERT IGNORE INTO `prompt_categories` (`id`, `name`, `description`, `created_by`) VALUES
(2, '项目', '项目类型相关提示词', 1),
(3, '风格', '设计风格相关提示词', 1),
(4, '材料', '建筑材料相关提示词', 1),
(5, '环境', '环境设置相关提示词', 1),
(6, '光影/氛围', '光影和氛围相关提示词', 1),
(7, '构图/出图指令', '构图和出图指令相关提示词', 1);

-- 插入提示词库项目
INSERT IGNORE INTO `prompt_items` (`id`, `title`, `content`, `category_id`, `description`, `is_public`, `created_by`) VALUES
-- 项目类型
(1, 'Residential Building - 住宅建筑', 'Residential Building', 2, '住宅建筑项目提示词', 1, 1),
(2, 'Commercial Building - 商业建筑', 'Commercial Building', 2, '商业建筑项目提示词', 1, 1),
(3, 'Office Building - 办公建筑', 'Office Building', 2, '办公建筑项目提示词', 1, 1),
(4, 'Mixed-use Development - 综合体建筑', 'Mixed-use Development', 2, '综合体建筑项目提示词', 1, 1),
(5, 'Cultural Center - 文化中心', 'Cultural Center', 2, '文化中心项目提示词', 1, 1),
(6, 'Educational Building - 教育建筑', 'Educational Building', 2, '教育建筑项目提示词', 1, 1),
(7, 'Healthcare Facility - 医疗建筑', 'Healthcare Facility', 2, '医疗建筑项目提示词', 1, 1),
(8, 'Sports Complex - 体育建筑', 'Sports Complex', 2, '体育建筑项目提示词', 1, 1),
(9, 'Transportation Hub - 交通枢纽', 'Transportation Hub', 2, '交通枢纽项目提示词', 1, 1),
(10, 'Religious Building - 宗教建筑', 'Religious Building', 2, '宗教建筑项目提示词', 1, 1),

-- 风格类型
(11, 'Modern Architecture - 现代主义建筑', 'Modern Architecture', 3, '现代主义建筑风格', 1, 1),
(12, 'Minimalist Architecture - 极简主义建筑', 'Minimalist Architecture', 3, '极简主义建筑风格', 1, 1),
(13, 'Brutalist Architecture - 野兽派建筑', 'Brutalist Architecture', 3, '野兽派建筑风格', 1, 1),
(14, 'Postmodern Architecture - 后现代主义建筑', 'Postmodern Architecture', 3, '后现代主义建筑风格', 1, 1),
(15, 'Deconstructivist Architecture - 解构主义建筑', 'Deconstructivist Architecture', 3, '解构主义建筑风格', 1, 1),
(16, 'Parametric Architecture - 参数化建筑', 'Parametric Architecture', 3, '参数化建筑风格', 1, 1),
(17, 'Sustainable Architecture - 可持续建筑', 'Sustainable Architecture', 3, '可持续建筑风格', 1, 1),
(18, 'Biophilic Architecture - 亲生命建筑', 'Biophilic Architecture', 3, '亲生命建筑风格', 1, 1),
(19, 'Futuristic Architecture - 未来主义建筑', 'Futuristic Architecture', 3, '未来主义建筑风格', 1, 1),
(20, 'Neo-futurism - 新未来主义', 'Neo-futurism', 3, '新未来主义建筑风格', 1, 1),

-- 材料类型
(21, 'Concrete - 混凝土', 'Concrete', 4, '混凝土材料', 1, 1),
(22, 'Glass - 玻璃', 'Glass', 4, '玻璃材料', 1, 1),
(23, 'Steel - 钢材', 'Steel', 4, '钢材材料', 1, 1),
(24, 'Wood - 木材', 'Wood', 4, '木材材料', 1, 1),
(25, 'Stone - 石材', 'Stone', 4, '石材材料', 1, 1),
(26, 'Brick - 砖材', 'Brick', 4, '砖材材料', 1, 1),
(27, 'Metal Panel - 金属板材', 'Metal Panel', 4, '金属板材材料', 1, 1),
(28, 'Ceramic - 陶瓷', 'Ceramic', 4, '陶瓷材料', 1, 1),
(29, 'Composite Material - 复合材料', 'Composite Material', 4, '复合材料', 1, 1),
(30, 'Bamboo - 竹材', 'Bamboo', 4, '竹材材料', 1, 1),

-- 环境类型
(31, 'Urban Environment - 城市环境', 'Urban Environment', 5, '城市环境设置', 1, 1),
(32, 'Natural Environment - 自然环境', 'Natural Environment', 5, '自然环境设置', 1, 1),
(33, 'Waterfront - 滨水环境', 'Waterfront', 5, '滨水环境设置', 1, 1),
(34, 'Mountain Setting - 山地环境', 'Mountain Setting', 5, '山地环境设置', 1, 1),
(35, 'Forest Setting - 森林环境', 'Forest Setting', 5, '森林环境设置', 1, 1),

-- 光影/氛围
(41, 'Golden Hour - 黄金时刻', 'Golden Hour', 6, '黄金时刻光影效果', 1, 1),
(42, 'Blue Hour - 蓝调时刻', 'Blue Hour', 6, '蓝调时刻光影效果', 1, 1),
(43, 'Dramatic Lighting - 戏剧性光影', 'Dramatic Lighting', 6, '戏剧性光影效果', 1, 1),
(44, 'Soft Natural Light - 柔和自然光', 'Soft Natural Light', 6, '柔和自然光效果', 1, 1),
(45, 'Artificial Lighting - 人工照明', 'Artificial Lighting', 6, '人工照明效果', 1, 1),

-- 构图/出图指令
(51, 'Aerial View - 鸟瞰视角', 'Aerial View', 7, '鸟瞰视角构图', 1, 1),
(52, 'Eye Level - 平视角度', 'Eye Level', 7, '平视角度构图', 1, 1),
(53, 'Low Angle - 低角度', 'Low Angle', 7, '低角度构图', 1, 1),
(54, 'High Angle - 高角度', 'High Angle', 7, '高角度构图', 1, 1),
(55, 'Close-up Detail - 特写细节', 'Close-up Detail', 7, '特写细节构图', 1, 1);

-- 插入话术库分类
INSERT IGNORE INTO `script_categories` (`id`, `name`, `description`, `created_by`) VALUES
(2, '客户画像', '客户画像相关话术', 1),
(3, '设计观点', '设计观点相关话术', 1),
(4, '设计策略', '设计策略相关话术', 1),
(5, '销售', '销售相关话术', 1),
(6, '客服', '客服相关话术', 1),
(7, '技术', '技术相关话术', 1);

-- 插入话术库项目
INSERT IGNORE INTO `scripts` (`id`, `title`, `content`, `category_id`, `description`, `is_public`, `created_by`) VALUES
(1, '高端住宅客户画像', '请帮我分析高端住宅项目的目标客户群体特征：\n\n1. 人口统计特征（年龄、收入、职业、家庭结构）\n2. 生活方式和价值观\n3. 购房动机和决策因素\n4. 设计偏好和审美倾向\n5. 对空间功能的特殊需求\n\n请结合当前市场趋势和消费者行为变化，提供详细分析。', 2, '针对高端住宅项目的目标客户群体特征描述', 1, 1),
(2, '可持续设计核心理念', '请帮我阐述关于可持续设计的核心理念：\n\n1. 可持续设计的定义和重要性\n2. 可持续设计的基本原则（材料选择、能源效率、废物管理等）\n3. 可持续设计如何平衡美学与环保需求\n4. 成功的可持续设计案例分析\n5. 可持续设计面临的挑战和未来发展方向\n\n请提供深入的分析和见解，以帮助我形成自己的设计观点。', 3, '关于可持续设计的核心理念和价值观表述', 1, 1),
(3, '开场白话术', '您好，欢迎了解我们的产品。我是您的专属顾问，很高兴为您服务。请问您今天想了解哪方面的信息呢？我会根据您的需求为您提供最合适的解决方案。', 5, '销售开场白标准话术', 1, 1),
(4, '异议处理话术', '我理解您的担忧，让我为您详细解释一下。这个问题确实是很多客户都会关心的，根据我们的经验和数据分析，我可以为您提供以下几个角度的解答...', 5, '处理客户异议的标准话术', 1, 1),
(5, '客服问候语', '您好，很高兴为您服务！我是客服代表，请问有什么可以帮助您的吗？无论您遇到什么问题，我都会尽力为您解决。', 6, '客服标准问候语', 1, 1),
(6, '技术解答模板', '根据您的问题，我建议采用以下解决方案：\n\n1. 首先分析问题的根本原因\n2. 制定针对性的解决策略\n3. 实施具体的技术方案\n4. 验证解决效果\n5. 建立长期维护机制\n\n如果您需要更详细的技术说明，我可以为您提供进一步的解释。', 7, '技术问题解答标准模板', 1, 1);

-- 插入话术库标签
INSERT IGNORE INTO `script_tags` (`id`, `name`, `color`) VALUES
(1, '客户画像', '#6f42c1'),
(2, '高端住宅', '#dc3545'),
(3, '目标群体', '#28a745'),
(4, '可持续设计', '#17a2b8'),
(5, '设计理念', '#ffc107'),
(6, '环保', '#20c997'),
(7, '销售', '#fd7e14'),
(8, '客服', '#6c757d'),
(9, '技术', '#495057');

-- 插入话术库项目标签关联
INSERT IGNORE INTO `script_item_tags` (`item_id`, `tag_id`) VALUES
(1, 1), (1, 2), (1, 3),
(2, 4), (2, 5), (2, 6),
(3, 7),
(4, 7),
(5, 8),
(6, 9);

-- 插入首页功能特性数据（这些数据通常存储在配置表或直接在前端定义）
-- 由于没有专门的features表，我们可以创建一个配置表来存储这些数据
CREATE TABLE IF NOT EXISTS `app_features` (
    `id` int(11) NOT NULL AUTO_INCREMENT,
    `icon` varchar(10) NOT NULL,
    `title` varchar(100) NOT NULL,
    `description` text NOT NULL,
    `link` varchar(200) NOT NULL,
    `sort_order` int(11) NOT NULL DEFAULT 0,
    `is_active` tinyint(1) NOT NULL DEFAULT 1,
    `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='应用功能特性表';

-- 插入首页功能特性
INSERT IGNORE INTO `app_features` (`id`, `icon`, `title`, `description`, `link`, `sort_order`) VALUES
(1, '💬', '聊天', '智能对话中心，集成多模型和专业知识，支持多种大语言模型。', '/chat', 1),
(2, '🖼️', '生图', '基于 Midjourney 的专业级、结构化图像生成引擎，提供精准的创意表达。', '/image-generation', 2),
(3, '📚', '知识库', '用户的私人设计知识大脑，上传并管理设计规范、项目资料、技术文档等。', '/knowledge-base', 3),
(4, '👨‍🎨', '大师库', '结构化的设计思想与灵感来源，添加设计大师及其核心设计观点。', '/master-library', 4),
(5, '🏞️', '图片库', '可复用的个人灵感数据库，结构化存储和管理设计灵感图片。', '/image-library', 5),
(6, '📝', '话术库', '专业的沟通话术模板库，提升设计师与客户的沟通效率。', '/script-library', 6),
(7, '🎯', '提示词库', '精心整理的AI提示词集合，帮助生成更精准的设计内容。', '/prompt-library', 7);

SET FOREIGN_KEY_CHECKS = 1;

-- 完成示例数据插入
SELECT '示例数据插入完成' as message;