#!/usr/bin/env python3
"""
本地RAG配置 - 使用本地模型，无需OpenAI API
支持多种本地嵌入模型和LLM
"""

import asyncio
import os
import sys
from typing import List, Optional
import torch
from sentence_transformers import SentenceTransformer
import numpy as np

# 添加RAG-Anything路径
sys.path.insert(0, '/opt/RAG-Anything')

try:
    from lightrag.llm.openai import openai_complete_if_cache, openai_embed
    from lightrag.utils import EmbeddingFunc
    from raganything.raganything import RAGAnything
except ImportError as e:
    print(f"❌ 导入RAG-Anything模块失败: {e}")
    print("请确保RAG-Anything已正确安装")
    sys.exit(1)

class LocalEmbeddingFunc:
    """本地嵌入模型函数"""
    
    def __init__(self, model_name: str = "BAAI/bge-small-zh-v1.5"):
        """
        初始化本地嵌入模型
        
        Args:
            model_name: 嵌入模型名称，推荐：
                - BAAI/bge-small-zh-v1.5 (中文，轻量)
                - BAAI/bge-base-zh-v1.5 (中文，平衡)
                - sentence-transformers/all-MiniLM-L6-v2 (英文，轻量)
                - sentence-transformers/all-mpnet-base-v2 (英文，高质量)
        """
        self.model_name = model_name
        self.model = None
        self.embedding_dim = None
        
    def _load_model(self):
        """延迟加载模型"""
        if self.model is None:
            print(f"🔄 加载本地嵌入模型: {self.model_name}")
            try:
                self.model = SentenceTransformer(self.model_name)
                # 获取嵌入维度
                test_embedding = self.model.encode(["test"])
                self.embedding_dim = test_embedding.shape[1]
                print(f"✅ 模型加载成功，嵌入维度: {self.embedding_dim}")
            except Exception as e:
                print(f"❌ 模型加载失败: {e}")
                # 回退到默认模型
                print("🔄 尝试加载默认模型...")
                self.model = SentenceTransformer('all-MiniLM-L6-v2')
                test_embedding = self.model.encode(["test"])
                self.embedding_dim = test_embedding.shape[1]
                print(f"✅ 默认模型加载成功，嵌入维度: {self.embedding_dim}")
    
    def __call__(self, texts: List[str]) -> List[List[float]]:
        """
        对文本进行向量化
        
        Args:
            texts: 文本列表
            
        Returns:
            嵌入向量列表
        """
        self._load_model()
        
        try:
            # 使用本地模型进行嵌入
            embeddings = self.model.encode(texts, convert_to_tensor=False)
            
            # 确保返回格式正确
            if isinstance(embeddings, np.ndarray):
                embeddings = embeddings.tolist()
            
            return embeddings
            
        except Exception as e:
            print(f"❌ 嵌入生成失败: {e}")
            # 返回零向量作为回退
            return [[0.0] * self.embedding_dim for _ in texts]

class LocalLLMFunc:
    """本地LLM函数 - 支持Ollama"""
    
    def __init__(self, model_name: str = "qwen2:7b", base_url: str = "http://localhost:11434"):
        """
        初始化本地LLM
        
        Args:
            model_name: Ollama模型名称，推荐：
                - qwen2:7b (通义千问，中文友好)
                - llama3:8b (Meta Llama 3)
                - mistral:7b (Mistral AI)
                - gemma:7b (Google Gemma)
            base_url: Ollama服务地址
        """
        self.model_name = model_name
        self.base_url = base_url
        
    async def __call__(self, prompt: str, system_prompt: Optional[str] = None, 
                      history_messages: List = None, **kwargs) -> str:
        """
        调用本地LLM生成回答
        
        Args:
            prompt: 用户提示
            system_prompt: 系统提示
            history_messages: 历史消息
            **kwargs: 其他参数
            
        Returns:
            生成的回答
        """
        try:
            import requests
            
            # 构建消息
            messages = []
            
            if system_prompt:
                messages.append({"role": "system", "content": system_prompt})
            
            if history_messages:
                messages.extend(history_messages)
            
            messages.append({"role": "user", "content": prompt})
            
            # 调用Ollama API
            response = requests.post(
                f"{self.base_url}/api/chat",
                json={
                    "model": self.model_name,
                    "messages": messages,
                    "stream": False
                },
                timeout=60
            )
            
            if response.status_code == 200:
                result = response.json()
                return result.get("message", {}).get("content", "")
            else:
                print(f"❌ Ollama API调用失败: {response.status_code}")
                return "抱歉，本地模型暂时不可用。"
                
        except Exception as e:
            print(f"❌ 本地LLM调用失败: {e}")
            return "抱歉，本地模型调用出现错误。"

def create_local_rag(working_dir: str, 
                    embedding_model: str = "BAAI/bge-small-zh-v1.5",
                    llm_model: str = "qwen2:7b") -> RAGAnything:
    """
    创建本地RAG实例
    
    Args:
        working_dir: 工作目录
        embedding_model: 嵌入模型名称
        llm_model: LLM模型名称
        
    Returns:
        RAGAnything实例
    """
    
    # 创建本地嵌入函数
    local_embedding = LocalEmbeddingFunc(embedding_model)
    
    # 创建本地LLM函数
    local_llm = LocalLLMFunc(llm_model)
    
    # 创建嵌入函数包装器
    embedding_func = EmbeddingFunc(
        embedding_dim=384,  # 默认维度，会在加载模型时更新
        max_token_size=512,
        func=local_embedding
    )
    
    # 创建RAG实例
    rag = RAGAnything(
        working_dir=working_dir,
        llm_model_func=local_llm,
        embedding_func=embedding_func
    )
    
    # 更新嵌入维度
    if hasattr(local_embedding, 'embedding_dim') and local_embedding.embedding_dim:
        embedding_func.embedding_dim = local_embedding.embedding_dim
    
    return rag

async def process_document(working_dir: str, file_path: str):
    """处理文档"""
    try:
        print(f"🔄 开始处理文档: {file_path}")
        
        rag = create_local_rag(working_dir)
        
        # 处理文档
        result = await rag.insert_file(file_path)
        
        print(f"✅ 文档处理完成: {file_path}")
        print("LOCAL_RAG_RESULT:", "文档处理成功")
        
        return result
        
    except Exception as e:
        print(f"❌ 文档处理失败: {e}")
        print("LOCAL_RAG_ERROR:", str(e))
        raise

async def query_rag(working_dir: str, query: str):
    """查询RAG知识库"""
    try:
        print(f"🔍 查询RAG知识库: {query}")
        
        rag = create_local_rag(working_dir)
        
        # 查询知识库
        result = await rag.query_with_multimodal(query, mode="hybrid")
        
        print("LOCAL_RAG_RESULT:", result)
        
        return result
        
    except Exception as e:
        print(f"❌ RAG查询失败: {e}")
        print("LOCAL_RAG_ERROR:", str(e))
        raise

async def main():
    """主函数"""
    if len(sys.argv) < 3:
        print("用法: python local_rag_config.py <working_dir> <command> [args...]")
        print("命令:")
        print("  query <query_text>     - 查询RAG知识库")
        print("  process <file_path>    - 处理文档")
        sys.exit(1)
    
    working_dir = sys.argv[1]
    command = sys.argv[2]
    
    if command == "query":
        if len(sys.argv) < 4:
            print("❌ 缺少查询文本")
            sys.exit(1)
        query_text = sys.argv[3]
        await query_rag(working_dir, query_text)
        
    elif command == "process":
        if len(sys.argv) < 4:
            print("❌ 缺少文件路径")
            sys.exit(1)
        file_path = sys.argv[3]
        await process_document(working_dir, file_path)
        
    else:
        print(f"❌ 未知命令: {command}")
        sys.exit(1)

if __name__ == "__main__":
    asyncio.run(main())
