import { createRouter, createWebHistory } from 'vue-router'
import HomeView from '../views/HomeView.vue'
import LoginView from '../views/LoginView.vue'
import RegisterView from '../views/RegisterView.vue'
import ProfileView from '../views/ProfileView.vue'
import { isLoggedIn } from '../services/api.js'

const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  routes: [
    {
      path: '/login',
      name: 'login',
      component: LoginView,
      meta: { requiresAuth: false }
    },
    {
      path: '/register',
      name: 'register',
      component: RegisterView,
      meta: { requiresAuth: false }
    },
    {
      path: '/',
      name: 'home',
      component: HomeView
    },
    {
      path: '/chat',
      name: 'chat',
      component: () => import('../views/ChatView.vue')
    },
    {
      path: '/image-generation',
      name: 'image-generation',
      component: () => import('../views/ImageGenerationView.vue')
    },
    {
      path: '/knowledge-base',
      name: 'knowledge-base',
      component: () => import('../views/KnowledgeBaseView.vue')
    },
    {
      path: '/master-library',
      name: 'master-library',
      component: () => import('../views/MasterLibraryView.vue')
    },
    {
      path: '/image-library',
      name: 'image-library',
      component: () => import('../views/ImageLibraryView.vue')
    },
    {
      path: '/prompt-library',
      name: 'prompt-library',
      component: () => import('../views/PromptLibraryView.vue')
    },
    {
      path: '/script-library',
      name: 'script-library',
      component: () => import('../views/ScriptLibraryView.vue')
    },
    {      path: '/settings',
      name: 'settings',
      component: () => import('../views/SettingsView.vue')
    },

    {
      path: '/profile',
      name: 'profile',
      component: ProfileView,
      meta: { requiresAuth: true }
    }
  ]
})

// 路由守卫，检查用户是否已登录
router.beforeEach((to, from, next) => {
  // 检查用户登录状态
  const userLoggedIn = isLoggedIn();
  
  // 登录和注册页面特殊处理
  if (to.path === '/login' || to.path === '/register') {
    // 已登录用户访问登录或注册页面，重定向到首页
    if (userLoggedIn) {
      next({ path: '/' });
    } else {
      // 未登录用户可以访问登录或注册页面
      next();
    }
  } else {
    // 非登录/注册页面需要验证登录状态
    if (!userLoggedIn) {
      // 未登录用户访问其他页面，重定向到登录页面
      next({ path: '/login' });
    } else {
      // 已登录用户正常访问
      next();
    }
  }
});

export default router