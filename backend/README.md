# SCAIA 后端 API 服务

SCAIA（Smart Conversational AI Assistant）智能助手后端 API 服务，提供用户管理、聊天功能、知识库管理等核心功能。

## 🚀 功能特性

### 核心功能
- **用户认证与授权** - JWT 令牌认证、角色权限管理
- **聊天管理** - 会话管理、消息存储、AI 模型集成
- **知识库管理** - 文档上传、分类管理、全文搜索
- **大师库管理** - AI 角色管理、属性配置
- **图片库管理** - 图片上传、分类、标签管理
- **提示词库** - 提示词模板、分类管理
- **话术库管理** - 话术模板、参数化配置
- **设置管理** - 用户设置、系统配置

### 技术特性
- **RESTful API** - 标准化 API 设计
- **数据库支持** - MySQL 数据库
- **文件上传** - 支持多种文件格式
- **安全防护** - 速率限制、CORS、Helmet 安全头
- **日志记录** - 结构化日志、错误追踪
- **错误处理** - 统一错误处理机制

## 📋 系统要求

- **Node.js** >= 16.0.0
- **npm** >= 8.0.0
- **MySQL** >= 8.0
- **操作系统** Linux/macOS/Windows

## 🛠️ 安装与配置

### 1. 克隆项目

```bash
git clone <repository-url>
cd scaia/backend
```

### 2. 安装依赖

```bash
npm install
```

### 3. 环境配置

复制环境配置文件并填入实际配置：

```bash
cp .env.example .env
```

编辑 `.env` 文件，配置以下关键参数：

```env
# 数据库配置
DB_HOST=localhost
DB_USER=your_db_user
DB_PASSWORD=your_db_password
DB_NAME=scaia_db

# JWT 密钥
JWT_SECRET=your_super_secret_key

# 服务器端口
PORT=3000
```

### 4. 数据库设置

创建数据库并导入表结构：

```bash
# 创建数据库
mysql -u root -p -e "CREATE DATABASE scaia_db CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;"

# 导入表结构（如果有 SQL 文件）
mysql -u root -p scaia_db < database/schema.sql
```

### 5. 启动服务

```bash
# 开发模式（自动重启）
npm run dev

# 生产模式
npm start
```

## 📁 项目结构

```
backend/
├── config/              # 配置文件
│   ├── database.js      # 数据库配置
│   └── logger.js        # 日志配置
├── middleware/          # 中间件
│   ├── auth.js          # 认证中间件
│   ├── validation.js    # 数据验证
│   ├── errorHandler.js  # 错误处理
│   └── logger.js        # 日志中间件
├── routes/              # 路由控制器
│   ├── auth.js          # 用户认证
│   ├── users.js         # 用户管理
│   ├── chat.js          # 聊天功能
│   ├── knowledge.js     # 知识库
│   ├── masters.js       # 大师库
│   ├── images.js        # 图片库
│   ├── prompts.js       # 提示词库
│   ├── scripts.js       # 话术库
│   └── settings.js      # 设置管理
├── uploads/             # 文件上传目录
├── logs/                # 日志文件目录
├── app.js               # Express 应用配置
├── server.js            # 服务器启动文件
├── package.json         # 项目依赖
├── .env.example         # 环境配置示例
└── README.md            # 项目说明
```

## 🔌 API 端点

### 认证相关
- `POST /api/auth/register` - 用户注册
- `POST /api/auth/login` - 用户登录
- `POST /api/auth/logout` - 用户登出
- `POST /api/auth/refresh` - 刷新令牌
- `GET /api/auth/me` - 获取当前用户信息

### 用户管理
- `GET /api/users/profile` - 获取用户资料
- `PUT /api/users/profile` - 更新用户资料
- `POST /api/users/avatar` - 上传用户头像
- `GET /api/users/settings` - 获取用户设置
- `PUT /api/users/settings` - 更新用户设置

### 聊天功能
- `GET /api/chat/sessions` - 获取聊天会话列表
- `POST /api/chat/sessions` - 创建新会话
- `GET /api/chat/sessions/:id` - 获取会话详情
- `PUT /api/chat/sessions/:id` - 更新会话
- `DELETE /api/chat/sessions/:id` - 删除会话
- `GET /api/chat/sessions/:id/messages` - 获取会话消息
- `POST /api/chat/sessions/:id/messages` - 发送消息

### 知识库管理
- `GET /api/knowledge/categories` - 获取分类列表
- `GET /api/knowledge/items` - 获取知识库项目
- `POST /api/knowledge/items` - 创建知识库项目
- `GET /api/knowledge/items/:id` - 获取项目详情
- `PUT /api/knowledge/items/:id` - 更新项目
- `DELETE /api/knowledge/items/:id` - 删除项目

### 其他模块
- **大师库** - `/api/masters/*`
- **图片库** - `/api/images/*`
- **提示词库** - `/api/prompts/*`
- **话术库** - `/api/scripts/*`
- **设置管理** - `/api/settings/*`

## 🔧 开发指南

### 代码规范

```bash
# 代码检查
npm run lint

# 自动修复
npm run lint:fix
```

### 测试

```bash
# 运行测试
npm test

# 监听模式
npm run test:watch

# 覆盖率报告
npm run test:coverage
```

### 数据库迁移

```bash
# 运行迁移
npm run db:migrate

# 填充测试数据
npm run db:seed

# 重置数据库
npm run db:reset
```

## 🚀 部署

### 使用 PM2 部署

```bash
# 安装 PM2
npm install -g pm2

# 启动应用
npm run pm2:start

# 查看状态
pm2 status

# 查看日志
npm run pm2:logs

# 重启应用
npm run pm2:restart

# 停止应用
npm run pm2:stop
```

### Docker 部署

```bash
# 构建镜像
docker build -t scaia-backend .

# 运行容器
docker run -d -p 3001:3001 --name scaia-backend scaia-backend
```

## 📊 监控与日志

### 日志文件
- **应用日志** - `logs/app.log`
- **错误日志** - `logs/error.log`
- **访问日志** - `logs/access.log`

### 健康检查
- **健康检查端点** - `GET /health`
- **API 状态** - `GET /api`

## 🔒 安全配置

### 安全特性
- **JWT 认证** - 无状态身份验证
- **密码加密** - bcrypt 哈希加密
- **速率限制** - 防止 API 滥用
- **CORS 配置** - 跨域请求控制
- **Helmet 安全头** - HTTP 安全头设置
- **输入验证** - Joi 数据验证

### 安全建议
1. 定期更新依赖包
2. 使用强密码和密钥
3. 启用 HTTPS
4. 配置防火墙
5. 定期备份数据

## 🐛 故障排除

### 常见问题

**数据库连接失败**
```bash
# 检查数据库服务状态
sudo systemctl status mysql

# 检查连接配置
mysql -h localhost -u username -p
```

**端口被占用**
```bash
# 查看端口占用
lsof -i :3000

# 杀死进程
kill -9 <PID>
```

**权限错误**
```bash
# 检查文件权限
ls -la uploads/

# 修改权限
chmod 755 uploads/
```

## 📝 更新日志

### v1.0.0 (2024-01-01)
- 初始版本发布
- 完整的 API 功能实现
- 用户认证与授权
- 聊天功能
- 知识库管理
- 文件上传功能

## 🤝 贡献指南

1. Fork 项目
2. 创建功能分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 打开 Pull Request

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 📞 支持

如有问题或建议，请通过以下方式联系：

- 提交 Issue
- 发送邮件至 <EMAIL>
- 查看文档 [docs.scaia.com](https://docs.scaia.com)

---

**SCAIA Team** © 2024